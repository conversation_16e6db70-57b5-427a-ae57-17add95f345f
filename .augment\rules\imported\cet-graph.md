---
type: "manual"
---

# cet-graph 相关 AI 代码生成规则

## 1. 关键词触发规则

当用户描述或指令中出现以下关键词时，自动调用 cet-graph 图形组件进行代码生成：

- cet-graph
- 图形组件
- 组态画面
- PecDraw
- PecStar
- Matterhorn

## 2. 触发逻辑

检测到上述关键词后，自动生成包含 CetGraph 组件的标准用法代码片段。

## 3. 代码生成模板

生成代码片段应包含如下结构（以 Vue 组件为例）：

```vue
<template>
  <div style="height:100%">
    <CetGraph v-bind="CetGraphAttr"></CetGraph>
  </div>
</template>
<script>
import CetGraph from "cet-graph";
import { CetGraphConfig } from "cet-graph";

// 可选全局配置
CetGraphConfig.isNewAuth = false;
CetGraphConfig.enablePTZControl = false;
CetGraphConfig.locale = "zh-cn";

export default {
  data() {
    return {
      CetGraphAttr: {
        path_in: "示例路径\\示例.drw", // 必填，图形路径
        userName_in: "用户名", // 建议填写，涉及遥控遥调
        refresh_trigger_in: 0 // 必填，变化时触发刷新
      }
    };
  },
  mounted() {
    this.CetGraphAttr.refresh_trigger_in = new Date().getTime();
  }
};
</script>
```

## 4. 参数说明

- `path_in` {String}: 图形路径，必填。
- `refresh_trigger_in` {Number}: 变化时触发刷新，必填。
- `userName_in` {String}: 用户名，建议填写。
- 其余参数可根据上下文自动补全或留空。

## 5. 示例

**用户描述：**

> 请用 cet-graph 绘制深圳站南山区西丽厂的组态画面

**AI 生成代码：**

```vue
<template>
  <div style="height:100%">
    <CetGraph v-bind="CetGraphAttr"></CetGraph>
  </div>
</template>
<script>
import CetGraph from "cet-graph";
export default {
  data() {
    return {
      CetGraphAttr: {
        path_in: "深圳站\\南山区\\西丽厂.drw",
        refresh_trigger_in: 0
      }
    };
  },
  mounted() {
    this.CetGraphAttr.refresh_trigger_in = new Date().getTime();
  }
};
</script>
```
