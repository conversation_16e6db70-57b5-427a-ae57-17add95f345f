---
type: "manual"
---

# omega-i18n 国际化规则

## 1. 将页面上的中文用$T(“中文”)的语法包裹，必须使用$T，不能使用$t，生成后的代码必须符合 vue 的基础规则

1. 在 html 中的静态文字，示例

```js
<template>
  <div>{{ $T("中文") }}</div>
</template>
```

2. 在 html 中将中文文字绑定到元素的属性中，需要先将静态属性赋值，改为动态属性赋值，再为中文文字用$T(“中文”)的语法包裹，示例

```vue
<template>
  <div :title="$T('中文')"></div>
</template>
```

3. 在 js 中，将中文文字用$T(“中文”)的语法包裹，示例

```js
const msg = $T("中文");
```

4. 在 js 中，遇到中文中有动态的变量，需要用$T(“中文{0}{1}”, 变量 0,变量 1)的语法包裹，示例

```js
const year = 2025;
const week = 20;
const msg = $T("{0}年第{1}周", year, week);
```

## 2. 出现中文名称时，将页面中的所有中文，写入 src/config/lang/en.json 中的 JSON 对象中，并遵守以下规则：

1. 自动翻译该中文对应的英文，以中文为 key，英文为 value

2. 翻译出来的英文，需要遵守英文首字母大写的规范，每个单词的首字母必须大写（介词、冠词除外），多个单词之间用空格隔开

3. 翻译出来的英文如果为一个句子，那么句子开头的第一个单词的首字母必须大写

4. 禁止出现相同的中文 key，如果出现相同的中文 key，那么只保留第一个

5. 示例：

```json
{
  "中文": "Chinese",
  "累计收益": "Accumulated Revenue",
  "充电桩总数": "Total of Charge Stations",
  "请选择新能源站点": "Please select a new energy site",
  "{0}年第{1}周": "{0} Year Week {1}"
}
```

## 4. 如果需要获取当前语言，请使用@omega/i18n，示例：

```js
import OmgeaI18n from "@omega/i18n";
const locale = OmgeaI18n.locale; // "en"，"zh-CN"
```
