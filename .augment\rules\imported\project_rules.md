---
type: "always_apply"
description: "Example description"
---

1. 框架技术栈为 vue2 + js，使用 element v2 版本组件

2. 请学习 cet-common、@omega-theme、@omega-i18n、@omega-http 的源码，以便在后续使用这些依赖生成代码时，能正确生成代码

3. 在页面中新增、修改组件时，主动学习源码，生成正确的组件代码

4. 在国际化方面，必须严格遵守/omega-i18n.md 内的全部规则；

5. 在组件使用方面，必须严格遵守/rules/组件.md 内的全部规则；

6. 在样式设置方面，必须严格遵守/rules/omega-theme.md 内的全部规则；

7. 在接口定义及调用时，必须严格遵守/rules/omega-http.md 内的全部规则；

8. 必须严格遵守/rules/生成接口函数.md 内的全部规则；

9. 必须严格遵守/rules/生成目录.md 内的全部规则；

10. 必须严格遵守/rules/生成页面.md 内的全部规则；
