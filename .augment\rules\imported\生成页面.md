---
type: "manual"
---

# 生成页面规则

## 1. 执行规则

当识别到`生成xx页面`功能时，必须严格执行下方的规则来生成对应的页面

<!-- ## 2. 调用 MCP 获取 UI 信息，在对应的页面文件中生成对应的代码，生成时需要符合以下规则： -->

## 2. 根据上传的图片生成页面，生成时需要符合以下规则：

1. 最外层的盒子高度必须为 100%，占满整个页面

2. 需要根据 UI 图中的大小，宽度，高度等进行页面样式的适配

3. 禁止新增任何不存在于图片中的元素

4. 添加调用接口的逻辑时，优先去 `src/api 同名文件夹/index.js` 中获取对应的接口，获取不到的情况下，可以获取 api 下所有文件夹中的接口，获取成功后，调用 apifox-MCP 获取对应的接口信息，保证调用接口的入参和返回值与接口信息一致；

5. 禁止添加`加载中（loading）`效果

6. 禁止在接口请求失败时，弹出错误提示框。

7. 禁止在接口调用失败时进行错误信息输出等操作

8. 为接口传递参数时，禁止在 `headers` 中添加 `Use-ID` 和 `X-Auth-Tenant` 参数

9. 在处理接口返回值时，严格遵循返回值规则，接口返回值示例：

```js
response = {
  code: 0, // 状态码，0为正常调用，其他为不正常调用，用于判断接口是否调用成功
  data: {}, // 接口返回的具体数据，用于在页面中展示具体数据
  msg: "", // 成功/失败信息
  total: 0 // 数据总条数，可用于分页器展示总页数
};
```
