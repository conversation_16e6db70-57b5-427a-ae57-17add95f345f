---
type: "always_apply"
---

# 组件使用规则

## 1. 复杂组件

1. @omega-icon

遇到图标，优先使用 element 中的 `icon` 组件，如果在 element 中找不到对应的图标，请使用 `@omega-icon` 组件，详细使用规则请根据./trae/rules/omega-icon.md 中进行使用

2. cet-chart

遇到图表类功能，必须使用 `cet-chart` 组件，详细使用规则请根据./trae/rules/cet-chart.md 中进行使用

3. @omega-trend

遇到趋势曲线类功能，必须使用 `@omega-trend` 组件，详细使用规则请根据./trae/rules/omega-trend.md 中进行使用

4. @omega-unity3d

遇到需要内嵌 3d 功能的，必须使用 `@omega-unity3d` 组件，详细使用规则请根据./trae/rules/omega-unity3d.md 中进行使用

## 2. 常用组件

1. 时间组件

遇到时间组件，必须使用 `cet-common` 中的 `cet-dateSelect` 组件，不用进行引用，直接使用，示例：

```vue
<template>
  <CetDateSelect
    v-bind="CetDateSelect_time"
    v-on="CetDateSelect_time.event"
  ></CetDateSelect>
</template>
<script>
export default {
  data() {
    return {
      CetDateSelect_time: {
        mode: "button"
        value: {
          dateType: "2",
          value: new Date().getTime()
        }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义 7带时分秒选择的自定义
        typeList: ["day", "week", "month", "season",  "year", "daterange", "datetimerange"], // 自定义左侧下拉框可选择的日期类型，日、周、月、季、年、自定义、带时分秒选择的自定义
        showOption: true, // 设置是否显示左侧日期类型选择下拉框
        event: {
          date_out: this.CetDateSelect_time_date_out, // 修改日期输出
          dateType_out: this.CetDateSelect_time_dateType_out // 修改日期类型输出
        }
      }
    };
  },
  methods: {
    CetDateSelect_time_date_out(val) {},
    CetDateSelect_time_dateType_out(val) {},
  }
};
</script>
```

2. 侧边栏组件

遇到页面上有左侧侧边栏的，必须使用 `cet-common` 中的 `cet-aside` 组件，不用进行引用，直接使用，示例：

```vue
<template>
  <cet-aside>
    <template #aside>
      <!-- 左侧侧边栏具体内容-->
    </template>
    <template #container>
      <!-- 右侧内容区具体内容-->
    </template>
  </cet-aside>
</template>
```

3. 节点树组件

遇到页面上有节点树组件的，必须使用 `cet-common` 中的 `cet-gaintTree` 组件，不用进行引用，直接使用，示例：
cet-gaintTree 组件，示例：

```vue
<template>
  <CetGiantTree
    v-bind="CetGiantTree_left"
    v-on="CetGiantTree_left.event"
    :searchText_in.sync="CetGiantTree_left.searchText_in"
  ></CetGiantTree>
</template>
<script>
export default {
  data() {
    return {
      CetGiantTree_left: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        searchText_in: "",
        event: {
          created_out: this.CetGiantTree_left_created_out, // 节点树构建完成输出
          currentNode_out: this.CetGiantTree_left_currentNode_out, //选中单行输出
          checkedNodes_out: this.CetGiantTree_left_checkedNodes_out // 勾选多行输出
        }
      }
    };
  },
  methods: {
    CetGiantTree_left_created_out(val) {},
    CetGiantTree_left_currentNode_out(val) {},
    CetGiantTree_left_checkedNodes_out(val) {}
  }
};
</script>
```

## 3. 其他组件

1. 遇到选择框组件，如果选择框有前缀文字，必须使用 `custom-el-select` 组件，不用进行引用，直接使用，使用方式与 element v2 中选择框组件一致，新增了一个 `prefix_in` 属性，用于设置选择框前缀文字，如果没有前缀文字，则使用 element v2 中的选择框组件，示例：

```vue
<template>
  <!-- 带前缀的选择框 -->
  <custom-el-select v-model="selectValue" :prefix_in="$T('前缀文字')">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </custom-el-select>

  <!-- 不带前缀的选择框 -->
  <el-select v-model="selectValue">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  data() {
    return {
      selectValue: 1,
      options: [
        {
          label: $T("选项1"),
          value: 1
        },
        {
          label: $T("选项2"),
          value: 2
        }
      ]
    };
  }
};
</script>
```

2. 遇到文本输入框组件，如果输入框有前缀文字，必须使用 `custom-el-input` 组件，不用进行引用，直接使用，使用方式与 element v2 中输入框件一致，新增了一个 `prefix_in` 属性，用于设置输入框前缀文字，如果没有前缀文字，则使用 element v2 中的输入框件，示例：

```vue
<template>
  <!-- 带前缀的输入框 -->
  <custom-el-input
    v-model="inputValue"
    :prefix_in="$T('前缀文字')"
  ></custom-el-input>

  <!-- 不带前缀的输入框 -->
  <el-input v-model="inputValue"></el-input>
</template>

<script>
export default {
  data() {
    return {
      inputValue: ""
    };
  }
};
</script>
```

3. 遇到表格组件，使用 `el-tabl`e 组件，使用方式参考 element 官方文档，stripe 必须设置为 false，如果某列没有使用插槽，那么必须为该列设置 `show-overflow-tooltip` 属性，并设置为 true，示例：

```vue
<template>
  <el-table :data="tableData">
    <!-- 不带插槽的列 -->
    <el-table-column
      prop="name"
      :label="$T('名称')"
      show-overflow-tooltip
    ></el-table-column>
    <!-- 带插槽的列 -->
    <el-table-column :label="$T('操作')">
      <template slot-scope="scope">
        <el-button
          type="text"
          @click="handleEdit(scope.row)"
          class="text-ZS mr-J1"
        >
          {{ $T("编辑") }}
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: []
    };
  }
};
</script>
```

4. 除了上述提到的组件，其他组件一律使用 `element v2` 中的组件，不使用 cet 和 omega 的组件，使用规则请参考 element 的官方文档,地址：https://element.eleme.cn/#/zh-CN/component/tree
