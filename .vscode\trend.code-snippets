{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  //cet-trend的代码片段
  "cet-trend-template": {
    "prefix": "cet-trend-template",
    "body": [
      " <CetTrend",
      "   :queryMode=\"CetTrend_${1:请输入组件唯一识别字符串}.queryMode\"    ",
      "   :dataConfig=\"CetTrend_${1:请输入组件唯一识别字符串}.dataConfig\"    ",
      "   :queryTime_in=\"CetTrend_${1:请输入组件唯一识别字符串}.queryTime_in\"    ",
      "   :params_in=\"CetTrend_${1:请输入组件唯一识别字符串}.params_in\"    ",
      "   :interval_in=\"CetTrend_${1:请输入组件唯一识别字符串}.interval_in\"    ",
      "   :title_in=\"CetTrend_${1:请输入组件唯一识别字符串}.title_in\"    ",
      "   :exportImgName_in=\"CetTrend_${1:请输入组件唯一识别字符串}.exportImgName_in\"    ",
      "   :queryTrigger_in=\"CetTrend_${1:请输入组件唯一识别字符串}.queryTrigger_in\"    ",
      "   :clearTrigger_in=\"CetTrend_${1:请输入组件唯一识别字符串}.clearTrigger_in\"    ",
      "   :scatter_in=\"CetTrend_${1:请输入组件唯一识别字符串}.scatter_in\"    ",
      "   v-bind=\"CetTrend_$1.config\"   ",
      "   @scatterClick_out=\"CetTrend_$1_scatterClick_out\"   ",
      " ></CetTrend>"
    ],
    "description": ""
  },
  "cet-trend-data": {
    "prefix": "cet-trend-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                            ",
      " CetTrend_$1: {                                                             ",
      "   //组件模式设置项                                                      ",
      "   queryMode: \"trigger\", //查询按钮触发trigger，或者查询条件变化立即查询diff                                                     ",
      "   //组件数据绑定设置项                                                     ",
      "   dataConfig: {                                                           ",
      "     queryUrl: \"\",                                   ",
      "     type: \"POST\"                                   ",
      "   },                                                                                        ",
      "   queryTrigger_in: Date.now(),                                   ",
      "   clearTrigger_in: Date.now(),                                   ",
      "   // 格式为{timeType:1,time:null} time字段中一个是起始时间，两个第一个是起始时间，第二个是终止时间,初始化查询需要设置该值          ",
      "   queryTime_in: null,                                   ",
      "   title_in: \"趋势曲线\",                                   ",
      "   exportImgName_in: \"趋势曲线\",   //自定义导出图片名称                                ",
      "   params_in: [],                                   ",
      "   interval_in: 5,     //整数值, 0或者1不进行抽点, 2或者大于2的值按设置值进行抽点                              ",
      "   scatter_in: [],                                   ",
      "   config: {                                                ",
      "     showTableButton: true,                                   ",
      "     showLegend: true,                                   ",
      "     showExtremButton: true,                                   ",
      "     showAverageButton: true,                                   ",
      "     showPointButton: true                                   ",
      "   }                                                         ",
      " },                                                        "
    ],
    "description": ""
  },
  "cet-trend-method": {
    "prefix": "cet-trend-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                   ",
      "    CetTrend_$1_scatterClick_out(val) {},                                   "
    ],
    "description": ""
  }
}
