{
  // Place your snippets for vue-html here. Each snippet is defined under a snippet name and has a prefix, body and
  // description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $1 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the
  // same ids are connected.
  // Example:
  // "Print to console": {
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$1"
  // 	],
  // 	"description": "Log output to console"
  // }
  "cet-template": {
    "prefix": "cet-template",
    "body": [
      "//cet-$1的代码片段",
      "\"cet-$1\": {",
      "\"prefix\": \"cet-$1\",                                   ",
      "\"body\": [],                                   ",
      "\"description\":\"\"",
      "},"
    ],
    "description": ""
  },

  "Print to console": {
    "prefix": "vue",
    "body": [
      "<template>",
      " <div class=\"page\">\n",
      " </div>",
      "</template>\n",
      "<script>",
      "export default {",
      "name: \"${1:请输入弹窗页面name}\",",
      "components:{},",
      " data() {",
      "  return {\n",
      "  }",
      " },",
      "watch: {},",
      "methods: {},",
      "}",
      "</script>\n",
      "<style lang=\"scss\" scoped >\n",
      ".page {",
      "width: 100%;",
      "height: 100%;",
      "position: relative;",
      " }",
      "</style>",
      "$1"
    ],
    "description": "Log output to console"
  },
  // 容器组件只包含 template部分、不包含data部分、methods部分
  //div的代码片段
  "div-template": {
    "prefix": "div-template",
    "body": [" <div style=\"float:right;\">", " </div>"],
    "description": ""
  },
  //el-container的代码片段
  "el-container-template": {
    "prefix": "el-container-template",
    "body": [" <el-container style=\"height:100%;padding:0px 16px 11px 12px\">", " </el-container>"],
    "description": ""
  },
  //el-main的代码片段
  "el-main-template": {
    "prefix": "el-main-template",
    "body": [" <el-main style=\"height:100%;padding:0px 16px 11px 12px\">", " </el-main>"],
    "description": ""
  },
  //el-aside的代码片段
  "el-aside-template": {
    "prefix": "el-aside-template",
    "body": [" <el-aside width=\"315px\">", " </el-aside>"],
    "description": ""
  },
  //el-header的代码片段
  "el-header-template": {
    "prefix": "el-header-template",
    "body": [" <el-header height=\"58px\" style=\"padding:0px;background-color:white;line-height:58px;\">", " </el-header>"],
    "description": ""
  },
  //el-footer的代码片段
  "el-footer-template": {
    "prefix": "el-footer-template",
    "body": [" <el-footer height=\"50px\" style=\"padding:0px;background-color:white; line-height:50px;\">", " </el-footer>"],
    "description": ""
  },
  //el-row的代码片段
  "el-row-template": {
    "prefix": "el-row-template",
    "body": [" <el-row :gutter=\"20\">", " </el-row>"],
    "description": ""
  },
  //el-col的代码片段
  "el-col-template": {
    "prefix": "el-col-template",
    "body": [" <el-col :span=\"5\">", " </el-col>"],
    "description": ""
  },
  //el-tabs的代码片段
  "el-tabs-template": {
    "prefix": "el-tabs-template",
    "body": [
      " <el-tabs tab-position=\"top\" type=\"border-card\" style=\"height: 100%;width: 100%;\">",
      "  <el-tab-pane label=\"页面1\" style=\"height: 100%;\">",
      "  </el-tab-pane>  ",
      "  <el-tab-pane label=\"页面2\" style=\"height: 100%;\">",
      "  </el-tab-pane>  ",
      " </el-tabs>"
    ],
    "description": ""
  },
  //el-tab-pane的代码片段
  "el-tab-pane-template": {
    "prefix": "el-tab-pane-template",
    "body": [" <el-tab-pane label=\"页面1\" style=\"height: 100%;\">", " </el-tab-pane>"],
    "description": ""
  },
  //"容器布局"的代码片段，一次性添加4个panel
  "容器布局-template": {
    "prefix": "容器布局-template",
    "body": [
      "<el-container style=\"height:100%;\">",
      "  <el-aside width=\"315px\">",
      "  </el-aside>",
      "  <el-container style=\"padding:0px 16px 11px 12px;height:100%\">",
      "    <el-header height=\"58px\" style=\"padding:0px;background-color:white;line-height:58px;\">",
      "    </el-header>",
      "    <el-container style=\"padding:0px;height:100%\">",
      "    </el-container>",
      "    <el-footer height=\"50px\" style=\"padding:0px;background-color:white; line-height:50px;\">",
      "      <div style=\"float:right;\">",
      "      </div>",
      "    </el-footer>",
      "  </el-container>",
      "</el-container>"
    ],
    "description": ""
  },
  //"栅格布局"的代码片段
  "栅格布局-template": {
    "prefix": "栅格布局-template",
    "body": ["<el-row :gutter=\"20\">", "  <el-col :span=\"5\"/>", "  <el-col :span=\"5\"/>", "</el-row>"],
    "description": ""
  }
  // 每个组件一般包含 template部分、data部分、methods部分三块，分别引入使用组件的页面中，通过tab切换，配置好其唯一识别字符串等内容即可
}
