module.exports = {
  dockerReposServer: {
    // 镜像仓库名称
    host: "*************",
    repo_name: "front-frame/base-fusionweb-1.25.1",
    user: {
      name: "dev",
      password: "Dev57611_Dev57611_Dev57611_Dev57611"
    },
    image_max: 12,
    // 重要说明：
    // 版本号支持 2~4位版本号，以4位版本号为例 v1.3.1.{n}, {n}会根据镜像私库的近999条版本号进行排序，计算出其下一个版本号的{n}。
    // {n} 仅支持放在最后一位。
    // 如需要手动管理版本号只需要将{n}替换成想要的版本即可。例如v1.3.1.13代表我推送到镜像库的为v1.3.1.13版本
    // 详细见代码 ../dockerRepo.js
    tag: "v1.0.{n}"
  },
  dingDingRobot: {
    // 钉钉自定义机器人创建详细步骤: http://***********:4999/web/#/5?page_id=140
    // 钉钉自定义机器人
    robots: [
      // {
      //   secret: "SEC2d6f74875fbd3d7a0b7043463099ae8709bd7cb9031fbc16baaaf3cdb684eed4",
      //   webhook: "https://oapi.dingtalk.com/robot/send?access_token=71ffbb725526785c02e61946feb2af52362b51cf671372ba22ea14726a069f93"
      // }
    ]
    // isAtAll: false,
    // 通过手机号@相关人
    // atMobiles: []
  },
  sonarQubeServer: {
    origin: "http://10.12.135.234:9000"
  },
  buildOption: {
    movePackageJsonToBuildDir: false
  }
};
