# 运行

```
npm install

npm run dev
```

# 跳过登录功能

在开发环境中，可以通过以下方式跳过登录直接进入系统：

## 方法1：修改.env文件
将 `.env` 文件中的 `omegaCliDevserver.mockAuth` 设置为 `true`：

```sh
omegaCliDevserver.mockAuth=true
omegaCliDevserver.mockAuthUserId=1
omegaCliDevserver.proxyFilename="proxy.local.js"
```

## 方法2：创建.env.local文件（推荐）
创建 `.env.local` 文件（不会被版本控制跟踪），内容如下：

```sh
omegaCliDevserver.mockAuth=true
omegaCliDevserver.mockAuthUserId=1
omegaCliDevserver.proxyFilename="proxy.local.js"
```

## 方法3：配置白名单路由
在 `src/omega/auth.js` 中的 `whiteRouteList` 数组中添加需要跳过登录的路由：

```js
whiteRouteList: ["/bladeView", "/your-route-path"]
```

配置完成后，重启开发服务器即可生效。

# .env 文件说明

.env 文件为 vue-cli 提供的环境变量配置，如果需要本地私有配置需要手动创建一个`.env.local`文件

```sh
omegaCliDevserver.mockAuth=true
omegaCliDevserver.mockAuthUserId=1
omegaCliDevserver.proxyFilename="proxy.local.js"

// 详细参考 @omega/cli-devserver 使用说明
// mockAuth {Boolean} - 是否开启跳过登录功能
// mockAuthUserId {Number} - 需要模拟的用户ID，默认为1（ROOT）。
// proxyFilename {String} - 使用的代理文件的名称，该文件格式为抛出的代理配置对象，方便本地多环境配置
```

# 本地私有代理配置文件说明

在项目目录下新建一个 `var` 目录，然后新建一个代理配置文件，其文件名只要和 `omegaCliDevserver.proxyFilename` 保持一致即可生效。

文件内容格式如下：

```js
module.exports = {
  // 内容直接复制 vue.config.js-> devServer.proxy
}
```


