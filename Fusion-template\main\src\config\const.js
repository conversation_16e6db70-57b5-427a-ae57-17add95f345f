const EVENT_LEVEL = [
  {
    id: 0,
    text: $T("预警"),
    color: "#FF9D09"
  },
  {
    id: 1,
    text: $T("事故"),
    color: "#F95E5A"
  },
  {
    id: 2,
    text: $T("告警"),
    color: "#FFCE20"
  },
  {
    id: 3,
    text: $T("一般"),
    color: "#4CA6FF"
  },
  {
    id: 4,
    text: $T("预警"),
    color: "#FFE1BF"
  },
  {
    id: 5,
    text: $T("其他"),
    color: "#8D7BFE"
  }
];

//事件的反应，0：无反应，1：动作
const EVENT_Reaction = {
  NOT: 0, //不闪屏，不声音报警，不短信通知，不独立卡片展示
  ACTION: 1
};

const RELOAD_KEY = "omega_reload_path_record";

const TENANT_KEY = "omega_project_id";

export const PLATPROJECTID = 1;


export { EVENT_LEVEL, EVENT_Reaction, RELOAD_KEY, TENANT_KEY };
