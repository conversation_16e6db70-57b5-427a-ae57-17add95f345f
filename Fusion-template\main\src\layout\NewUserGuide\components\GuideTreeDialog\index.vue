<template>
  <div class="GuideTreeDialog">
    <components
      :is="'CetDialog'"
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      :key="openTrigger_in"
    >
      <el-container>
        <CetTree
          :selectNode.sync="CetTree.selectNode"
          v-bind="CetTree"
          v-on="CetTree.event"
          showFilterChildNode
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span
              :class="[
                data?.depth === 0 ? 'groupName' : 'stepName',
                'text-ellipsis'
              ]"
            >
              {{ language == "zh_cn" ? node?.label : data?.i18nName }}
            </span>
            <el-button
              v-if="data?.step != null"
              size="mini"
              class="enter-Guide"
              @click="onGuideClick(data)"
            >
              <omega-icon class="icon-Guide" symbolId="navigation" />
              {{ $T("进入引导") }}
            </el-button>
          </span>
        </CetTree>
      </el-container>
      <template v-slot:footer>
        <span>
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
    </components>
  </div>
</template>
<script>
import { appState } from "@altair/lord";

export default {
  name: "GuideTreeDialog",
  components: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    stepsTreeData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    language() {
      return window.localStorage.getItem("omega_language");
    }
  },
  watch: {},
  data() {
    return {
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("新手引导"),
        width: "900px",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      selectNode: {},
      CetTree: {
        inputData_in: [],
        selectNode: {},
        checkedNodes: [],
        filterNodes_in: null,
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {}
      }
    };
  },
  methods: {
    /**
     * ### Initialize data
     * ### 数据初始化
     * @description: Assign the steps tree data to the input data property of the CetTree component
     * @description: 将引导树数据赋值给CetTree组件的inputData属性
     */
    initData() {
      this.CetTree.inputData_in = this.stepsTreeData;
    },
    onGuideClick(node) {
      this.$emit("useEventHandler", "onGuideClick", node);
    },
    CetButton_cancel_statusTrigger_out() {
      this.$emit("useEventHandler", "onClose");
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    }
  },
  created() {},
  mounted() {
    this.initData();
  }
};
</script>
<style lang="scss" scoped>
.GuideTreeDialog ::v-deep {
  .line-bottom {
    border-bottom: 0;
  }
  .el-dialog {
    border-radius: 10px;
    height: 70%;

    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
      overflow: hidden !important;
      padding: 8px !important;
      @include background_color(BG);
    }
  }
  .el-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: auto;
    padding: 24px;
    @include background_color(BG1);
    display: flex;
    flex-direction: column;
  }
  .el-tree-node__content {
    height: 34px;
    border-radius: 5px;
    .groupName {
      font-size: 16px;
    }
    .stepName {
      font-size: 16px;
    }
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 20px;
    .enter-Guide {
      opacity: 0;
      display: flex;
      align-items: center;
    }
    .icon-Guide {
      margin-right: 8px;
      font-size: 12px;
      transform: rotate(90deg);
    }
  }
  .custom-tree-node:hover {
    .enter-Guide {
      opacity: 1;
      transition: opacity 1s ease;
    }
  }
  .el-tree > .el-tree-node > .el-tree-node__content {
    background-color: var(--BG4);
    margin: 10px;
  }
  .text-ellipsis {
    width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
