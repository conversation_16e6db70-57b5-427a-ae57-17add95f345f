<template>
  <div class="NewUserGuide" v-show="showGuide">
    <el-tooltip :content="$T('新手引导')" placement="bottom">
      <OmegaIcon
        symbolId="question"
        class="icon-hover-normal icon-p6"
        @click="onOpenClick"
      />
    </el-tooltip>
    <GuideTreeDialog
      v-if="GuideTreeDialog.openTrigger"
      ref="GuideTreeDialog"
      v-bind="GuideTreeDialog"
      :stepsTreeData="stepsTreeData"
      @useEventHandler="useEventHandler"
    />
  </div>
</template>

<script>
// api
import { appState } from "@altair/lord";
// components
import GuideTreeDialog from "./components/GuideTreeDialog/index.vue";

export default {
  name: "NewUserGuide",
  components: { GuideTreeDialog },
  data() {
    return {
      showGuide: false, // Show Guide/是否展示引导(默认不展示)
      stepsTreeData: [], // Dao Tree Data/引导树数据
      GuideTreeDialog: {
        openTrigger: false,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  computed: {
    language() {
      return window.localStorage.getItem("omega_language");
    }
  },
  methods: {
    /**
     * ### Guide Navigation
     * ### 引导导航
     * @description: Generates route parameters from clicked node info and navigates to the specified page.
     * @description: 根据点击的节点信息生成路由参数,并导航到指定页面
     * @param {Object} node - Navigation Node Object, containing application name, path, and step information.
     * @param {Object} node - 导航节点对象,包含应用名称,路径和步骤信息
     * @return {*}
     */
    onGuideClick(node) {
      const { appName, path, step } = node;
      const timestamp = Date.parse(new Date() + "") / 1000;

      // Default Name for Fusion Module
      const FUSION_NAME = "fusion";

      const params = {
        path: `/${FUSION_NAME}/${appName}${path}`,
        query: {
          t: timestamp,
          step
        }
      };

      this.$router.push(params);

      this.onClose();
    },
    /**
     * ### Open Guide Dialog
     * ### 打开引导弹框
     * @return {*}
     */
    onOpenClick() {
      this.GuideTreeDialog.openTrigger = true;
      this.GuideTreeDialog.openTrigger_in = new Date().getTime();

      this.$nextTick(() => {
        this.$refs.GuideTreeDialog.CetDialog_pagedialog.openTrigger_in =
          new Date().getTime();
      });
    },
    /**
     * ### Close Guide Dialog
     * ### 关闭引导弹框
     * @return {*}
     */
    onClose() {
      this.isDrawerMode = false;
      this.GuideTreeDialog.openTrigger = false;
    },
    /**
     * ### Unified event handling
     * ### 事件统一处理
     * @return {*}
     */
    useEventHandler(e, ...args) {
      if (typeof e !== "string" || !this.hasOwnProperty(e)) {
        console.warn(`Invalid function name: ${e}`);
        return;
      }

      const handler = this[e];

      if (typeof handler === "function") {
        try {
          handler.call(this, ...args);
        } catch (error) {
          console.error(`Error executing function ${e}:`, error);
        }
      } else {
        console.warn(` ${e} is not a function`);
      }
    },
    /**
     * ### Initialize the New User Guide Steps
     * ### 初始化新手引导步骤
     * @description: Initializes the steps for the new user guide by processing app information and generating the corresponding guide steps.
     *               This function does not accept parameters.
     * @description: 初始化新手引导步骤，通过处理应用信息并生成相应的引导步骤。
     * @return {*}: The return value is not directly used within the function, but it populates the guide steps data.
     * @return {*}: 返回值在函数内部没有直接使用,但它会填充引导步骤的数据。
     */
    initNewGuideSteps() {
      const apps = appState.info.app;

      const steps =
        apps
          ?.map((i, k) => {
            const { i18n = {}, newGuideSteps = [] } = i.config.normal;
            const obj = {
              name: i.name,
              children: newGuideSteps
            };

            this.traverse([obj], i.name, k, i18n);

            return obj;
          })
          ?.filter(i => !!i?.children?.length) || [];

      this.showGuide = !!steps?.length;
      this.stepsTreeData = steps;
    },

    /**
     * ### Traverse the node tree and add attributes to each node
     * ### 遍历节点树，为每个节点添加属性
     * @description: The main functionality includes generating a unique tree_id for each node, setting the node's depth, and adding corresponding names based on provided internationalization information.
     * @description: 主要功能包括为每个节点生成唯一的tree_id,设置节点的深度,并根据提供的国际化信息添加相应的名称.
     * @param {Array} nodes - An array of nodes, where each node is an object.
     * @param {Array} nodes - 节点数组
     * @param {String} name - The name of the application or module to which the node belongs.
     * @param {String} name - 节点所属的应用或模块的名称
     * @param {Number} depth - The current depth of the node, default is 0.
     * @param {Number} depth - 当前节点的深度，默认为0
     * @param {Object} i18n - An internationalization object used to provide localized names for the nodes.
     * @param {Object} i18n - 国际化对象，默认为{}
     * @return {*}
     */
    traverse(nodes, name, depth = 0, i18n = {}) {
      if (!nodes?.length) return;

      nodes.forEach((node, key) => {
        node.tree_id = `${name}_${depth}_${key}`;
        node.depth = depth;
        node.depth = depth;
        node.appName = name;
        node.i18nName =
          i18n?.[this.language]?.[node.name] || $T(node?.name) || node?.name;

        if (node.children && node.children.length > 0) {
          this.traverse(node.children, name, depth + 1, i18n);
        }
      });
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.initNewGuideSteps();
    });
  }
};
</script>

<style lang="scss" scoped></style>
