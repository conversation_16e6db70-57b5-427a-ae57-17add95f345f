<template>
  <el-button
    v-if="projectMode() === 'PlatInProject'"
    @click="goBack"
    size="mini"
    type="text"
    class="back-to-project"
    icon="el-icon-s-home"
  >
    {{ $T("返回平台") }}
  </el-button>
  <!-- <div style="width: 100px; height: 100px"></div> -->
</template>

<script>
import { projectMode } from "@altair/lord";
export default {
  name: "BackToProject",
  data() {
    return {};
  },
  methods: {
    projectMode() {
      return projectMode();
    },
    goBack() {
      sessionStorage.removeItem("omega_project_id");
      window.location.reload();
    }
  },
  computed: {}
};
</script>

<style lang="scss" scoped>
.back-to-project {
  order: -1;
  margin-left: 10px;
}
</style>
