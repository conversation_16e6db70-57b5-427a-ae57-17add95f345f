<template>
  <div class="alarm-shadow twinkle">
    <div
      class="shadow-top"
      :style="`boxShadow: ${shadowPropMap.top} ${
        twinkle ? alarmColor : 'transparent'
      }`"
    ></div>
    <div
      class="shadow-right"
      :style="`boxShadow: ${shadowPropMap.right} ${
        twinkle ? alarmColor : 'transparent'
      }`"
    ></div>
    <div
      class="shadow-bottom"
      :style="`boxShadow: ${shadowPropMap.bottom} ${
        twinkle ? alarmColor : 'transparent'
      }`"
    ></div>
    <div
      class="shadow-left"
      :style="`boxShadow: ${shadowPropMap.left} ${
        twinkle ? alarmColor : 'transparent'
      }`"
    ></div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { EVENT_LEVEL, EVENT_Reaction } from "@/config/const";
export default {
  name: "AlarmTwinkle",
  components: {},
  data() {
    return {
      shadowPropMap: {
        top: "0px -10px 3em 20px",
        bottom: "0px 10px 3em 20px",
        left: "-10px 0px 3em 20px",
        right: "10px 0px 3em 20px"
      },
      alarmColor: "#EF4139"
    };
  },
  computed: {
    ...mapState("settings", ["twinkle"]),
    ...mapState("notice", ["items"])
  },
  watch: {
    items(val) {
      if (val.length === 0) this.alarmColor = "transparent";
      else {
        const message = val[0];
        const content = message.content ? JSON.parse(message.content) : {};
        const eventClass = content.eventClass;
        const flickering = content?.flickering; //根据闪烁字段判断是否闪烁，默认闪烁，0不闪烁
        if (this.twinkle && flickering !== EVENT_Reaction.NOT) {
          const obj = EVENT_LEVEL.find(item => item.id === eventClass) || {};
          this.alarmColor = obj.color || "transparent";
        } else this.alarmColor = "transparent";
      }
    }
  },
  mounted() {
    this.alarmColor = "transparent";
  }
};
</script>

<style lang="scss" scoped>
.twinkle {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  animation: twinkle 1s infinite;
  animation-direction: alternate;
}
@keyframes twinkle {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0.25;
  }
  50% {
    opacity: 0.5;
  }
  75% {
    opacity: 0.75;
  }
  100% {
    opacity: 1;
  }
}
.alarm-shadow {
  & > div {
    position: fixed;
    &.shadow {
      &-top {
        height: 1px;
        top: -1px;
        right: 0;
        left: 0;
        box-shadow: 0px -10px 3em 20px #e4393c;
      }
      &-bottom {
        height: 1px;
        bottom: -1px;
        right: 0;
        left: 0;
        box-shadow: 0px 10px 3em 20px #e4393c;
      }
      &-left {
        z-index: 100000000;
        width: 1px;
        left: -1px;
        top: 0;
        bottom: 0px;
        box-shadow: -10px 0px 3em 20px #e4393c;
      }
      &-right {
        width: 1px;
        right: -1px;
        top: 0;
        bottom: 0px;
        box-shadow: 10px 0px 3em 20px #e4393c;
      }
    }
  }
}
</style>
