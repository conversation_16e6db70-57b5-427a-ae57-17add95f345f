<template>
  <div class="noticedrawepage" v-if="showNotice && alarmNotice">
    <div class="noticeHead">
      {{ $T("事件告警") }}
      <i class="el-icon-close closebar" @click="changeShow"></i>
    </div>
    <div class="noticebox">
      <div v-for="(item, index) in notice" :key="index" class="noticeitem">
        <div class="p6">{{ formatterTime(item.time) }}</div>
        <div class="p6">{{ item.logTypeName }}</div>
        <div class="content" :title="item.description">
          {{ item.description }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";

export default {
  name: "noticeDrawe",
  data() {
    return {
      notice: [],
      showNotice: false
    };
  },
  computed: {
    ...mapState("settings", ["alarmNotice"]),
    ...mapState("notice", ["items"])
  },
  watch: {
    items(val) {
      if (val.length > this.notice.length) {
        this.showNotice = true;
      } else {
        if (val.length === 0) {
          this.showNotice = false;
        }
      }
      this.notice = _.cloneDeep(val);
    }
  },
  methods: {
    changeShow() {
      this.showNotice = false;
    },
    formatterTime(time) {
      return this.$moment(time).format("YYYY-MM-DD HH:mm:ss.SSS");
    }
  }
};
</script>
<style scoped lang="scss">
.noticedrawepage {
  position: fixed;
  right: 10px;
  bottom: 10px;
  width: 450px;
  height: 350px;
  @include background_color(BG1);
  border: 1px solid;
  @include border_color(BG3);
  z-index: 999;

  .noticeHead {
    font-size: 18px;
    padding: 16px 16px 0px;
    .closebar {
      cursor: pointer;
      float: right;
    }
  }
  .noticebox {
    overflow-y: auto;
    height: calc(100% - 38px);
    .noticeitem {
      margin: 16px;
      border-bottom: 1px solid;
      @include border_color(BG3);
      padding-bottom: 10px;
      .p6 {
        padding-bottom: 6px;
      }
      .content {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden; //溢出内容隐藏
        text-overflow: ellipsis; //文本溢出部分用省略号表示
        display: -webkit-box; //特别显示模式
        -webkit-line-clamp: 2; //行数
        line-clamp: 2;
        -webkit-box-orient: vertical; //盒子中内容竖直排列
      }
    }
  }
}
</style>
