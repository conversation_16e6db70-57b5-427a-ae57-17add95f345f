<!--
 * @Author: wwj <EMAIL>
 * @Date: 2023-03-30 09:29:21
 * @LastEditors: wwj <EMAIL>
 * @LastEditTime: 2023-03-30 09:29:34
 * @FilePath: \BIZ-iPowerCloud-Web2022\src\layout\components\setting\components\AlarmNotice.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="text-center">
    <el-switch v-model="alarmTip" v-bind="switchConfig" />
  </div>
</template>

<script>
export default {
  name: "AlarmTip",
  components: {},
  computed: {
    alarmTip: {
      get() {
        return this.$store.state.settings.alarmTip;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "alarmTip",
          value: val
        });
      }
    }
  },
  data() {
    return {
      switchConfig: {
        activeColor: "#13ce66",
        inactiveColor: "#ff4949"
      }
    };
  },
  watch: {},
  methods: {},
  mounted() {}
};
</script>

<style lang="scss" scoped></style>
