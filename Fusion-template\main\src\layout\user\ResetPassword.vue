<template>
  <omega-dialog
    width="500px"
    :has-full-screen="false"
    :on-before-confirm="onBeforeConfirm"
    :title="$T('重置密码')"
  >
    <el-form
      class="content dialog-content"
      ref="form"
      :model="form"
      label-width="110px"
      label-position="top"
      :rules="rules"
    >
      <el-form-item :label="$T('原密码')" prop="oldPassword">
        <el-input
          clearable
          type="password"
          maxlength="32"
          :placeholder="$T('请输入原密码（当前账号密码）')"
          v-model.trim="form.oldPassword"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          clearable
          type="password"
          maxlength="18"
          :placeholder="$T('请输入新的密码')"
          v-model.trim="form.newPassword"
        />
      </el-form-item>
      <el-form-item label="新密码确认" prop="_checkPassword">
        <el-input
          clearable
          type="password"
          maxlength="18"
          :placeholder="$T('请输入确认密码')"
          v-model.trim="form._checkPassword"
        />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>
<script>
import common from "@/utils/common";
import { httping } from "@omega/http";
import omegaAuth from "@omega/auth";
import { encrypto } from "@/utils/util";

export default {
  name: "ResetUserPassword",
  data() {
    return {
      form: {
        oldPassword: "",
        newPassword: "",
        _checkPassword: ""
      },
      rules: {
        oldPassword: [
          { required: true, message: $T("原密码不能为空"), trigger: "blur" }
        ],
        newPassword: [
          common.check_strongPassword,
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this._.isNil(value) || value === "") {
                callback(new Error($T("新密码不能为空")));
                return;
              }

              callback();
            }
          }
        ],
        _checkPassword: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (value !== this.form.newPassword) {
                callback(new Error($T("密码不一致")));
                return;
              }
              callback();
            }
          }
        ]
      }
    };
  },
  methods: {
    getData() {
      const form = this.form;
      return {
        id: omegaAuth.user.getUserId(),
        name: encrypto(omegaAuth.user.getUserName()),
        newPassword: encrypto(form.newPassword),
        oldPassword: encrypto(form.oldPassword)
      };
    },

    async onBeforeConfirm() {
      const { form } = this.$refs;

      await form.validate();

      await httping({
        url: `/auth/v1/user/password/updateSecurity`,
        method: "PUT",
        data: this.getData()
      });

      return true;
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  //width: 300px;
  margin: auto;
}
</style>
