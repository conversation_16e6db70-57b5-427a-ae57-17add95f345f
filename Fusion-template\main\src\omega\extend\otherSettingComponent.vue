<template>
  <el-form class="container" :model="form" ref="form" label-position="top">
    <el-form-item label="国际化">
      <el-switch
        v-model="form.i18n"
        active-color="#13ce66"
        inactive-color="#ff4949"
      ></el-switch>
    </el-form-item>
    <el-form-item label="皮肤">
      <el-select v-model="form.theme" placeholder="请选择">
        <el-option label="亮色" value="light"></el-option>
        <el-option label="暗色" value="dark"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item :label="$T('页签标题')">
      <el-input
        v-model="form.systemTitle"
        style="width: 250px"
        :placeholder="$T('智慧云平台')"
      ></el-input>
    </el-form-item>
    <el-form-item :label="$T('登录页名称')">
      <el-input
        v-model="form.systemName"
        style="width: 250px"
        :placeholder="$T('智慧云平台')"
      ></el-input>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: "customeSetting",
  props: ["setting"],
  model: {
    prop: "setting",
    event: "change"
  },
  data() {
    return {
      form: {
        systemTitle: "智慧云平台",
        systemName: "智慧云平台",
        i18n: false,
        theme: "light"
      }
    };
  },
  watch: {
    form: {
      handler: function (val) {
        this.$emit("change", { ...val });
      },
      deep: true
    },
    setting: {
      handler: function (val, oldVal) {
        Object.assign(this.form, val);
      },
      deep: true
    }
  }
};
</script>

<style></style>
