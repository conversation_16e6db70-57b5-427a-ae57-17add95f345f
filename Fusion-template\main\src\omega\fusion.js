import omegaApp from "@omega/app";
import { FusionManager } from "@altair/lord";
//开发环境的配置
const develop = {
  appList: [
    {
      name: "fusion-list"
    },
    {
      name: "cloud-auth"
    },
    {
      name: "vpp-agg",
      // url: "/fusion/vpp-agg-1.0.0/",
      url: "//localhost:9529"
      // alive: false
      // preload: true,
      // exec: true
    }
  ],
  options: {
    systemConfig: "local",
    isMultiProject: true
  }
};
//部署环境的配置
const config = {
  appList: [],
  options: {
    isMultiProject: true
  }
};

omegaApp.plugin.register(
  FusionManager,
  process.env.NODE_ENV === "development" ? develop : config
);
