import omegaApp from "@omega/app";
import { OmegaHttpPlugin } from "@omega/http";

omegaApp.plugin.register(OmegaHttpPlugin, {
  // baseURL: "/apix",
  // globalRequestInterceptor: (config) => {
  //   console.log('globalRequestInterceptor', config);
  //   return config;
  // },
  // globalResponseInterceptor: (config) => {
  //   console.log('globalResponseInterceptor', config);
  //   return config;
  // }
});
