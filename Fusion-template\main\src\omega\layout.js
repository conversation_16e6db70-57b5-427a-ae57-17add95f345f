import { OmegaLayoutPlugin, SettingItem } from "@omega/layout";
import omegaApp from "@omega/app";

import User from "../layout/user/index.vue";
import BackToProject from "../layout/backToProject/index.vue";
import Notice from "../layout/notice/index.vue";
import ToBlade from "../layout/toBlade/index.vue";
import NewUserGuide from "../layout/NewUserGuide/index.vue";

import VolumeCtrl from "@/layout/setting/VolumeCtrl.vue";
import AlarmSoundMode from "@/layout/setting/AlarmSoundMode.vue";
import AlarmTip from "@/layout/setting/AlarmTip.vue";
import AlarmNotice from "@/layout/setting/AlarmNotice.vue";
import AlarmTwinkleMode from "@/layout/setting/AlarmTwinkleMode.vue";

omegaApp.plugin.register(OmegaLayoutPlugin, {
  // isSidebarCollapse: false,
  // layoutMode: "horizontal",
  // isShowFavorites: true,
  // isShowSetting: true,
  // isShowSettingTheme: true,
  // isShowSearch: true,
  // isShowSettingLanguage: false,
  // 采用内置皮肤设置
  settingThemeList: ["light", "dark", "blue"],
  // 自定义 [id, label, color]
  // id: 全局皮肤唯一id
  // label: 皮肤名称
  // color: 换肤颜色块颜色
  // settingThemeList: [
  // ["light", "浅色", "#987770"],
  // ["dark", "深色", "#ccc"]
  // ],
  // isNavmenuUniqueOpened: true,
  // isIconSubmenuOffset: false,
  renderHeaderRightTools(h) {
    return [
      <NewUserGuide />,
      <BackToProject />,
      <ToBlade />,
      <Notice />,
      <User />
    ];
  },
  renderSettingItems(h) {
    return [
      h(
        SettingItem,
        {
          props: {
            title: $T("告警音量"),
            symbolId: "layout-alarm-volume-lin"
          }
        },
        [h(VolumeCtrl)]
      ),
      h(
        SettingItem,
        {
          props: {
            title: $T("告警提示音"),
            symbolId: "layout-bell-lin"
          }
        },
        [h(AlarmSoundMode)]
      ),
      h(
        SettingItem,
        {
          props: {
            title: $T("告警提示"),
            symbolId: "layout-alarm-monitor-lin"
          }
        },
        [h(AlarmTip)]
      ),
      h(
        SettingItem,
        {
          props: {
            title: $T("事件告警弹窗"),
            symbolId: "layout-alarm-dialog"
          }
        },
        [h(AlarmNotice)]
      ),
      h(
        SettingItem,
        {
          props: {
            title: $T("告警闪屏"),
            symbolId: "layout-alarm-twinkle-lin"
          }
        },
        [h(AlarmTwinkleMode)]
      )
    ];
  }
});
