<!--
 * @Author: your name
 * @Date: 2022-03-17 14:19:08
 * @LastEditTime: 2022-03-21 15:42:40
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \Dashboard\src\plugins\dashboard\pue\config.vue
-->
<template>
  <CetDateSelect
    v-bind="CetDateSelect_pue"
    v-on="CetDateSelect_pue.event"
  />
</template>
<script>
import _ from "lodash";
export default {
  props: {
    content: {
      type: String
    }
  },
  watch: {
    content(val) {
      this.queryObj = JSON.parse(val);
    }
  },
  data() {
    return {
      queryObj: {}, //查询条件对象
      // pue组件
      CetDateSelect_pue: {
        value: { dateType: "2", value: new Date().getTime() - 34 * 3600 * 1000 * 24 }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义
        //自定义选项 typeList: ["day", "week", "month", "season",  "year", "daterange"]
        event: {
          date_out: this.CetDateSelect_pue_date_out,
          dateType_out: this.CetDateSelect_pue_dateType_out
        }
      }
    };
  },
  methods: {
    // pue输出,方法名要带_out后缀
    CetDateSelect_pue_date_out(val) {
      this.queryObj.queryTime = val;
      this.out();
    },
    CetDateSelect_pue_dateType_out(val, dateValue) {},
    out() {
      this.$emit("update:content", JSON.stringify(this.queryObj));
    }
  },
  created() {}
};
</script>
