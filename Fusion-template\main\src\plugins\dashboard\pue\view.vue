<!--
 * @Author: your name
 * @Date: 2021-08-04 15:30:24
 * @LastEditTime: 2022-03-21 15:42:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\plugins\dashboard\pue\view.vue
-->
<template>
  <el-table :style="chartStyle" :height="chartStyle.height">
    <ElTableColumn v-bind="ElTableColumn_ttt" />
  </el-table>
</template>
<script>
import _ from "lodash";
export default {
  props: {
    content: {
      type: String
    },
    chartStyle: {
      require: false,
      type: Object,
      default: () => {
        return {
          height: "500px"
        };
      }
    }
  },
  data() {
    return {
      // ttt组件
      ElTableColumn_ttt: {
        //type: "",      // selection 勾选 index 序号
        prop: "", // 支持path a[0].b
        label: "fsfsfsd", //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
        //minWidth: "200",  //该宽度会自适应
        //width: "100",     //绝对宽度
        //sortable: true,  //true 前端排序  "custom" 后端排序
        //formatter: null,      //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  methods: {}
};
</script>
