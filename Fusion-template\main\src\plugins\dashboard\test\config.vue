<template>
  <CetDateSelect
    v-bind="CetDateSelect_pue"
    v-on="CetDateSelect_pue.event"
  />
</template>
<script>
import _ from "lodash";
export default {
  props: {
    content: {
      type: String
    }
  },
  watch: {
    content(val) {
      this.queryObj = JSON.parse(val);
    }
  },
  data() {
    return {
      queryObj: {}, //查询条件对象
      // pue组件
      CetDateSelect_pue: {
        value: { dateType: "2", value: new Date().getTime() - 34 * 3600 * 1000 * 24 }, //设置日期值, dateType 1 日 2 周 3月 4季 5年 6自定义
        //自定义选项 typeList: ["day", "week", "month", "season",  "year", "daterange"]
        event: {
          date_out: this.CetDateSelect_pue_date_out,
          dateType_out: this.CetDateSelect_pue_dateType_out
        }
      }
    };
  },
  methods: {
    // pue输出,方法名要带_out后缀
    CetDateSelect_pue_date_out(val) {
      this.queryObj.queryTime = val;
      this.out();
    },
    CetDateSelect_pue_dateType_out(val, dateValue) {},
    out() {
      this.$emit("update:content", JSON.stringify(this.queryObj));
    }
  },
  created() {}
};
</script>
