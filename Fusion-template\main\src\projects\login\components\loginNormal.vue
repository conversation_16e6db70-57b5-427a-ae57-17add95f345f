<template>
  <div class="login-normal">
    <el-form :model="login" :rules="rules" ref="form">
      <FormLineItem symbolId="user-one-lin" :label="$T('账号')">
        <el-form-item prop="userName">
          <el-input :placeholder="$T('请输入账号')" v-model="login.userName" />
        </el-form-item>
      </FormLineItem>
      <FormLineItem symbolId="password-lin" :label="$T('密码')">
        <el-form-item prop="passWord">
          <el-input
            :placeholder="$T('请输入密码')"
            v-model="login.passWord"
            show-password
          />
        </el-form-item>
      </FormLineItem>
      <el-button
        class="login-btn"
        type="primary"
        size="medium"
        @click="evLoginBtnClick"
      >
        {{ $T("登录") }}
      </el-button>
    </el-form>
  </div>
</template>

<script>
import FormLineItem from "./formLineItem.vue";
import omegaAuth from "@omega/auth";
export default {
  name: "LoginNormal",
  components: { FormLineItem },
  data() {
    return {
      login: {
        userName: "",
        passWord: ""
      },
      rules: {
        userName: [
          {
            required: true,
            message: $T("账号不能为空"),
            trigger: "change"
          }
        ],
        passWord: [
          {
            required: true,
            message: $T("密码不能为空"),
            trigger: "change"
          }
        ]
      }
    };
  },
  methods: {
    async evLoginBtnClick() {
      await this.$refs.form.validate();

      const param = {
        userName: this.login.userName,
        password: this.login.passWord
      };

      await omegaAuth.login(param, { type: "security" });
    }
  }
};
</script>

<style lang="scss" scoped>
.login-btn {
  @include margin_top(J4);
  @include font_color(T5);
  width: 100%;
}
</style>
