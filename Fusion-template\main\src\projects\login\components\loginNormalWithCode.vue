<template>
  <div class="login-normal">
    <el-form :model="login" :rules="rules" ref="form">
      <FormLineItem symbolId="user-one-lin" :label="$T('账号')">
        <el-form-item prop="userName">
          <el-input :placeholder="$T('请输入账号')" v-model="login.userName" />
        </el-form-item>
      </FormLineItem>
      <FormLineItem symbolId="password-lin" :label="$T('密码')">
        <el-form-item prop="passWord">
          <el-input
            :placeholder="$T('请输入密码')"
            v-model="login.passWord"
            show-password
          />
        </el-form-item>
      </FormLineItem>
      <FormLineItem symbolId="permission-management-lin" :label="$T('验证码')">
        <el-form-item prop="captchaCode">
          <el-input
            :placeholder="$T('请输入验证码')"
            v-model="login.captchaCode"
          />
          <img
            :src="url"
            :alt="$T('验证码')"
            @click="getVerification"
            :title="$T('点击更新验证码')"
          />
        </el-form-item>
      </FormLineItem>
      <el-button
        class="login-btn"
        type="primary"
        size="medium"
        @click="evLoginBtnClick"
      >
        {{ $T("登录") }}
      </el-button>
    </el-form>
  </div>
</template>

<script>
import FormLineItem from "./formLineItem.vue";
import { http } from "@omega/http";
import omegaAuth from "@omega/auth";
export default {
  name: "LoginNormal",
  components: { FormLineItem },
  data() {
    return {
      login: {
        userName: "",
        passWord: "",
        captchaCode: ""
      },
      rules: {
        userName: [
          {
            required: true,
            message: $T("账号不能为空"),
            trigger: "change"
          }
        ],
        passWord: [
          {
            required: true,
            message: $T("密码不能为空"),
            trigger: "change"
          }
        ],
        captchaCode: [
          {
            required: true,
            message: $T("验证码不能为空"),
            trigger: "change"
          }
        ]
      },
      url: "",
      pageId: null
    };
  },
  methods: {
    async evLoginBtnClick() {
      await this.$refs.form.validate();

      const param = {
        userName: this.login.userName,
        password: this.login.passWord,
        captchaCode: this.login.captchaCode,
        pageId: this.pageId
      };
      try {
        let res = await omegaAuth.login(param, { type: "security" });
        // let relativeUserGroup = res.user.relativeUserGroup;
        // window.localStorage.setItem(
        //   "relativeUserGroup",
        //   JSON.stringify(relativeUserGroup)
        // );
      } catch (e) {
        this.getVerification();
      }
    },

    getVerification() {
      this.pageId = Math.random().toString(36).substr(2, 4);
      http({
        url: `/auth/v1/login/captchaImageBase64?pageId=${this.pageId}`,
        method: "GET"
      }).then(res => {
        if (res.code === 0) {
          this.url = res.data || "";
        }
      });
    }
  },
  beforeMount() {
    this.getVerification();
  }
};
</script>

<style lang="scss" scoped>
.login-btn {
  @include margin_top(J4);
  @include font_color(T5);
  width: 100%;
}
</style>
