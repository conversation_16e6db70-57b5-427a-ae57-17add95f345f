<template>
  <div class="login" :style="primartBackgroudStyle">
    <div
      class="loginSetCodeBox"
      @mouseover="showCode = true"
      @mouseout="showCode = false"
      v-if="codeSrc"
    >
      <img src="/static/image/code.svg" class="imageCode" alt="" />
      <div class="downloadBox">
        <p style="margin: 0px">扫码下载App</p>
        <p style="margin: 0px">Download App</p>
      </div>
    </div>
    <img
      :src="codeSrc"
      v-if="showCode"
      alt=""
      style="
        float: right;
        width: 150px;
        height: 150px;
        margin: 70px -150px 0px 0px;
      "
    />
    <div class="login-main">
      <div class="login-main-right">
        <div class="login-logo">
          <div class="login-logo-main">
            <div class="login-logo-img" :style="logoBackgroundStyle" />
            <div class="login-logo-text">
              {{ systemName }}
            </div>
          </div>
        </div>
        <div class="login-form">
          <LoginNormalWithCode v-if="isCodeLogin" />
          <LoginNormal v-else />
        </div>
      </div>
      <div class="login-main-left" :style="mainLeftBackgroundStyle"></div>
    </div>
  </div>
</template>

<script>
import LoginNormal from "./components/loginNormal.vue";
import LoginNormalWithCode from "./components/loginNormalWithCode.vue";
import { conf, store } from "@omega/app";
import $ from "jquery";
import { allConfig, getImg } from "@altair/blade";

export default {
  name: "Login",
  components: {
    LoginNormal,
    LoginNormalWithCode
  },
  computed: {
    isCodeLogin() {
      return allConfig?.system?.code_login || false;
    },
    systemName() {
      return store.state.systemName || $T("Matterhorn综合能源管理平台");
    },
    mainLeftBackgroundStyle() {
      return conf.state.resource.login_background_image_url
        ? `background-image: url(${conf.state.resource.login_background_image_url})`
        : "";
    },
    logoBackgroundStyle() {
      return conf.state.resource.login_logo_image_url
        ? `background-image: url(${conf.state.resource.login_logo_image_url})`
        : "";
    },
    primartBackgroudStyle() {
      return conf.state.resource.login_primary_image_url
        ? `background-image: url(${conf.state.resource.login_primary_image_url})`
        : "";
    }
  },
  data() {
    return {
      codeSrc: null,
      showCode: false
    };
  },
  async mounted() {
    const cb = evt => {
      if (evt.key === "Enter") {
        $(this.$el).find(".login-form .login-btn").click();
      }
    };

    const $document = $(window.document);
    $document.on("keyup", cb);
    this.$on("hook:beforeDestroy", () => $document.off("keyup", cb));
    this.codeSrc = await getImg("Qrcode");
  }
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  @include background_static(LOGIN_BG);
  background-size: cover !important;
  .loginSetCodeBox {
    float: right;
    margin: 20px 20px 0px 0px;
    width: 200px;
    height: 40px;
    position: relative;
    .imageCode {
      width: 40px;
      height: 40px;
      position: absolute;
      top: 0px;
      cursor: pointer;
      background: #fff;
    }
    .downloadBox {
      display: inline-block;
      position: absolute;
      left: 50px;
      top: 0px;
    }
  }
  &-main {
    position: absolute;
    top: 50%;
    margin-top: -350px;
    left: 50%;
    margin-left: -700px;
    width: 1400px;
    height: 700px;
    &-right {
      position: absolute;
      width: 400px;
      height: 100%;
      top: 0px;
      right: 0px;
      background-repeat: no-repeat;
      border-radius: 4px;

      @include background_color(BG1);
      @include box_shadow(S1);
      background-size: 100% 100%;
      z-index: 1000;
    }
    &-left {
      position: absolute;
      height: 100%;
      top: 0px;
      right: 400px;
      left: 0px;
      background-size: 100% 100%;
      @include background_image_static(LOGIN_CAROUSEL_IMG);
    }
  }
}

.login-logo {
  position: relative;
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
  &-main {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  &-img {
    background: no-repeat center bottom;
    background-size: contain;
    width: 300px;
    height: 100px;
    @include background_image_static(LOGIN_LOGO_IMG);
    @include margin_bottom(J3);
  }
  &-text {
    @include font_size(H1);
    @include line_height(H1);
    @include font_color(T3);
    text-align: center;
    padding: 0 22px;
  }
}
.login-form {
  height: calc(100% - 280px);
  @include padding(J2);
}
.login-form::v-deep .el-tabs {
  .el-tabs__nav {
    width: 100%;
  }
  .el-tabs__item {
    width: 33%;
    text-align: center;
    padding-right: 0;
    &:not(.is-active) {
      @include font_color(T3);
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
}
</style>
