import { LayoutMain } from "@omega/layout";

import { FusionContainer } from "@altair/lord";

import { BladeView } from "@altair/blade";

import pages from "./pages.js";

/** @type {import("vue-router").RouteConfig[]} */

const appRoutes = [
  ...pages,
  // {
  //   path: "/aa/testttt",
  //   component: () => import("@/projects/chartdemo/index.vue")
  // },
  {
    path: "/",
    component: LayoutMain,
    children: [
      {
        path: "/fusion/*",
        // meta: {
        //   keepAlive: true
        // },//不能keepAlive
        component: FusionContainer
      },
      {
        path: "/bladeView",
        component: BladeView
      }
    ]
  }
];

export default {
  routes: appRoutes
};
