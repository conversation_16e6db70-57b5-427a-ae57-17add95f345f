/*
 * @Author: your name
 * @Date: 2021-10-22 13:52:45
 * @LastEditTime: 2023-08-17 10:37:59
 * @LastEditors: wang<PERSON><PERSON> <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \dev\src\store\modules\settings.js
 */
const defaultSeetings = {
  soundMode: "single",
  twinkle: true,
  alarmTip: true,
  alarmNotice: true,
  videoDialog: true,
  isMuted: false,
  volume: 100
};
const fusion_web_settings = localStorage.getItem("fusion_web_settings");

const state = fusion_web_settings
  ? JSON.parse(fusion_web_settings)
  : defaultSeetings;

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    state[key] = value;
    localStorage.setItem("fusion_web_settings", JSON.stringify(state));
  }
};

const actions = {
  changeSetting({ commit }, data) {
    commit("CHANGE_SETTING", data);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
