<template>
  <ElDrawer class="drawer" v-bind="$attrs" v-on="$listeners">
    <slot></slot>
    <div
      class="flex flex-row justify-end footer"
      v-if="confirmText_in || cancelText_in"
    >
      <CetButton
        v-if="cancelText_in"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-if="confirmText_in"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </div>
  </ElDrawer>
</template>

<script>
export default {
  props: {
    confirmText_in: String,
    cancelText_in: String
  },
  data() {
    return {
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: this.confirmText_in,
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: () => {
            this.$emit("confirm_out");
          }
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: this.cancelText_in,
        plain: true,
        event: {
          statusTrigger_out: () => {
            this.$emit("cancel_out");
          }
        }
      }
    };
  }
};
</script>

<style lang="scss" scoped>
.drawer {
  :deep() {
    .el-drawer__body {
      display: flex;
      flex-direction: column;
      padding: 0;
    }
  }
  .footer {
    border-radius: 0;
  }
}
</style>
