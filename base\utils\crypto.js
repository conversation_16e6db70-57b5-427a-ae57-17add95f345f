import CryptoJS from "crypto-js";
// 以下为 8924534290ABCDEF1264147890ACAB56 的 16 进制码
// eslint-disable-next-line no-undef
const key = CryptoJS.enc.Hex.parse(
  "3839323435333432393041424344454631323634313437383930414341423536"
);
// eslint-disable-next-line no-undef
const iv = CryptoJS.enc.Hex.parse("2934577290ABCDEF1264147890ACAE75");

/**
 * 加密处理方法
 * @param {String} value
 * @returns
 */
export function encrypt(value) {
  // eslint-disable-next-line no-undef
  const AESValue = CryptoJS.AES.encrypt(value, key, { iv: iv }).toString();

  return AESValue;
}

/**
 * 解密处理方法
 * @param {String} value
 * @returns
 */
export function decrypt(value) {
  // eslint-disable-next-line no-undef
  const AESValue = CryptoJS.AES.decrypt(value, key, { iv: iv }).toString(
    CryptoJS.enc.Utf8
  );

  return AESValue;
}

/**
 * 针对对象里面某些属性进行加密
 * @param {Object} obj
 * @param {String[]} props
 * @returns
 */
export function encryptAttributes(obj, props = []) {
  const newObj = { ...obj };

  // 需要进行加密
  props.forEach(prop => {
    const value = newObj[prop];

    if (typeof value !== "string") {
      return;
    }

    newObj[prop] = encrypt(newObj[prop]);
  });

  return newObj;
}

/**
 * 针对对象里面某些属性进行解密
 * @param {Object} obj
 * @param {String[]} props
 * @returns
 */
export function decryptAttributes(obj, props = []) {
  const newObj = { ...obj };

  // 需要进行加密
  props.forEach(prop => {
    const value = newObj[prop];

    if (typeof value !== "string") {
      return;
    }

    newObj[prop] = decrypt(newObj[prop]);
  });

  return newObj;
}
