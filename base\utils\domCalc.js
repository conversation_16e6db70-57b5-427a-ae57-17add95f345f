/**
 * # 计算文本宽度
 * @param {*} text 文本
 * @param {*} padding 边距
 * @returns
 */
const calcTextWidthPad = (() => {
  // 缓存 canvas 和 context
  const canvas = document.createElement("canvas");
  const context = canvas.getContext("2d");

  return (text, padding = 20, size) => {
    if (typeof text !== "string" || !text.trim()) {
      return padding; // 如果文本为空，直接返回 padding
    }

    // 获取 body 的计算样式
    const computedStyle = window.getComputedStyle(document.body);
    const fontSize = size || computedStyle.fontSize || "16px"; // 默认字体大小
    const fontFamily = computedStyle.fontFamily || "微软雅黑"; // 默认字体

    // 设置 canvas 的字体
    context.font = `${fontSize} ${fontFamily}`;

    // 测量文本宽度并加上 padding
    const width = Math.ceil(context.measureText(text).width);
    return width + (isNaN(padding) ? 0 : padding);
  };
})();

export default {
  calcTextWidthPad
};
