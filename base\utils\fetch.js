import { HttpBase } from "@omega/http";
import { api } from "@altair/knight";
/**
 * 是否为开发环境
 */
function isDev() {
  return process.env.NODE_ENV === "development";
}

const user = api.getUser()?._user;

const option = {
  headers: {
    "User-ID": isDev() ? user.id : undefined,
    post: {
      "Content-Type": "application/json;charset=UTF-8 "
    }
  },
  responseType: "json"
};
const httping = new HttpBase(
  {
    rejectErrorCode: false
  },
  option
);

const hideNoticeFetch = new HttpBase(
  {
    rejectErrorCode: false,
    loading: false
  },
  option
);
export { httping as default, hideNoticeFetch };
