__registeAppConfig.setAppconfig({
  version: "1.0.0",
  i18n: {
    en: {
      基础配置: "Basic Config",
      管理层级配置: "Manage Level Config",
      管网层级配置: "Equipment Level Config",
      层级全量配置: "Level Full Config",
      基本项目配置: "Basic Project Config",
      "项目层级配置（表计全量创建）":
        "Project level config(full creation of meters)",
      创建能源类型: "Create Energy Type",
      表计全量创建: "Total Creation Meters",
      "项目层级配置（批量导入子层级）":
        "Project level config(batch import of sub levels)",
      批量导入: "Batch Import",
      拓扑快速接入: "Topology fast access"
    }
  },
  navmenu: [
    {
      label: "基础配置",
      icon: "permission-management-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "项目管理层级配置",
          category: "project",
          type: "menuItem",
          location: "/cloudProjectConfig",
          permission: "cloudProjectConfig"
        },
        {
          label: "项目管网层级配置",
          category: "project",
          type: "menuItem",
          location: "/networkCloudProjectConfig",
          permission: "networkCloudProjectConfig"
        },
        {
          label: "项目层级批量配置",
          category: "project",
          type: "menuItem",
          location: "/projectBatchConfig",
          permission: "projectBatchConfig"
        },
        {
          label: "拓扑快速接入",
          category: "project",
          type: "menuItem",
          location: "/topologyConfig",
          permission: "topologyConfig"
        }
      ]
    }
  ],
  newGuideSteps: [
    // 层级批量设置未联调，先屏蔽
    // {
    //   name: "基本项目配置",
    //   icon: "",
    //   desc: "",
    //   children: [
    //     {
    //       step: 1,
    //       path: "/cloudProjectConfig",
    //       name: "项目层级配置（表计全量创建）",
    //       icon: "",
    //       desc: "",
    //       children: [
    //         {
    //           step: 2,
    //           path: "/cloudProjectConfig",
    //           name: "创建能源类型",
    //           icon: "",
    //           desc: ""
    //         },
    //         {
    //           step: 1,
    //           path: "/projectBatchConfig",
    //           name: "表计全量创建",
    //           icon: "",
    //           desc: ""
    //         }
    //       ]
    //     },
    //     {
    //       step: 3,
    //       path: "/cloudProjectConfig",
    //       name: "项目层级配置（批量导入子层级）",
    //       icon: "",
    //       desc: "",
    //       children: [
    //         {
    //           step: 2,
    //           path: "/cloudProjectConfig",
    //           name: "创建能源类型",
    //           icon: "",
    //           desc: ""
    //         },
    //         {
    //           step: 4,
    //           path: "/cloudProjectConfig",
    //           name: "批量导入",
    //           icon: "",
    //           desc: ""
    //         }
    //       ]
    //     }
    //   ]
    // }
  ]
});
