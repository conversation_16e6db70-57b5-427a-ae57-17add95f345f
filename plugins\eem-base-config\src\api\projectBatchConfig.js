import fetch from "eem-base/utils/fetch";
const version = "v1";

// 查询生成层级或者未生成层级的节点树
export function organizationConfigTree(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/tree`,
    method: "POST",
    data
  });
}

// 根据cloud.json文件分析房间和其子层级的内容
export function organizationConfigRoomConfig() {
  return fetch({
    url: `/eem-service/${version}/organization/config/roomConfig`,
    method: "POST"
  });
}

// 返回需要去掉的能源类型列表
export function organizationConfigFilterEnergyType() {
  return fetch({
    url: `/eem-service/${version}/organization/config/filter/energyType`,
    method: "POST"
  });
}

// 解析cloud.json返回列的接口
export function organizationConfigColumnName() {
  return fetch({
    url: `/eem-service/${version}/organization/config/columnName`,
    method: "POST"
  });
}

// 返回右侧表格限制条数的接口
export function organizationConfigLimit() {
  return fetch({
    url: `/eem-service/${version}/organization/config/configLimit`,
    method: "POST"
  });
}

// 查询管理层级各个名称列表
export function organizationConfigManageName() {
  return fetch({
    url: `/eem-service/${version}/organization/config/manageName`,
    method: "POST"
  });
}

// 查询配置列表
export function organizationConfigQuery(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/query`,
    method: "POST",
    data
  });
}

// 删除配置
export function organizationDeleteConfig(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/deleteConfig`,
    method: "DELETE",
    data
  });
}

// 保存草稿
export function organizationConfigSaveDraft(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/saveDraft`,
    method: "POST",
    data
  });
}

// 生成配置
export function organizationConfigSaveConfig(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/saveConfig`,
    method: "POST",
    data
  });
}

// 返回当前拖动的表计在库里有无重复草稿的接口（判断是否已经生成or已经有了同样的草稿状态的配置）
export function organizationConfigCheckConfig(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/checkConfig`,
    method: "POST",
    data
  });
}

// 返回右侧表格限制条数的接口--导入节点
export function organizationConfigLimitImportConfig() {
  return fetch({
    url: `/eem-service/${version}/organization/config/limit/importConfig`,
    method: "POST"
  });
}

// 查询导入配置列表
export function organizationConfigQueryImportConfig(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/query/importConfig`,
    method: "POST",
    data
  });
}

// 导入配置列表
export function organizationConfigSaveImportConfig(data) {
  return fetch({
    url: `/eem-service/${version}/organization/config/save/importConfig`,
    method: "POST",
    data
  });
}

// 刷新节点树
export function organizationConfigTreeRefresh() {
  return fetch({
    url: `/eem-service/${version}/organization/config/refresh/tree`,
    method: "POST"
  });
}
