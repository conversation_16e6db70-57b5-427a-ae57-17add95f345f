import { driver } from "driver.js";
import "driver.js/dist/driver.css";

const DRIVER_CONFIG = {
  showProgress: true, // 展示步骤进度
  allowClose: true, // 是否允许点击遮罩时关闭
  animate: true, // 是否开启动画
  opacity: 0.75, // 遮罩透明度
  padding: 5, // 弹窗内边距
  showButtons: true, // 是否展示按钮
  prevBtnText: $T("上一步"), // 上一步按钮文本
  nextBtnText: $T("下一步"), // 下一步按钮文本
  doneBtnText: $T("完成"), // 完成按钮文本
  progressText: "{{current}} / {{total}}" // 步骤进度文本
};

/**
 * ###  driver用户引导demo
 * @return {*} Driver实例
 */
const useCounter = (config = {}) => {
  const Driver = driver();
  Driver.setConfig({ ...DRIVER_CONFIG, ...config });

  return [Driver];
};

export default useCounter;
