<template>
  <CetDialog v-bind="CetDialog_1" append-to-body v-on="CetDialog_1.event">
    <CetForm
      :data.sync="CetForm_1.data"
      v-bind="CetForm_1"
      v-on="CetForm_1.event"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item :label="typeName" prop="type">
            <ElSelect
              v-model="ElSelect_energytype.value"
              v-bind="ElSelect_energytype"
              v-on="ElSelect_energytype.event"
              :disabled="editData_in ? true : false"
            >
              <ElOption
                v-for="item in ElOption_energytype.options_in"
                :key="item[ElOption_energytype.key]"
                :label="item[ElOption_energytype.label]"
                :value="item[ElOption_energytype.value]"
                :disabled="item[ElOption_energytype.disabled]"
              ></ElOption>
            </ElSelect>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$T('基础单位')" prop="basicUnitSymbolName">
            <ElInput
              :placeholder="$T('请输入')"
              v-model.trim="CetForm_1.data.basicUnitSymbolName"
              disabled
            ></ElInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$T('目标单位(英文)')" prop="uniten">
            <ElInput
              :placeholder="$T('请输入如:{0}', 'mWh')"
              v-model.trim="CetForm_1.data.uniten"
              v-bind="ElInput_unit"
              v-on="ElInput_unit.event"
              onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
            ></ElInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$T('目标单位(中文)')" prop="unitcn">
            <ElInput
              :placeholder="$T('请输入如:{0}', '兆瓦时')"
              v-model.trim="CetForm_1.data.unitcn"
              v-bind="ElInput_unit"
              v-on="ElInput_unit.event"
              onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
            ></ElInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$T('转换系数')" prop="coef">
            <ElInputNumber
              v-model="CetForm_1.data.coef"
              v-bind="ElInputNumber_coef"
              v-on="ElInputNumber_coef.event"
            ></ElInputNumber>
            <div>
              {{ $T("预览") }}：
              <span
                v-show="
                  CetForm_1.data.uniten &&
                  CetForm_1.data.coef &&
                  CetForm_1.data.basicUnitSymbolName
                "
              >
                {{
                  `1${CetForm_1.data.uniten || "--"}=${
                    CetForm_1.data.coef || "--"
                  }${CetForm_1.data.basicUnitSymbolName || "--"}`
                }}
              </span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </CetForm>
    <span slot="footer">
      <CetButton
        class="mr-J1"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import commonApi from "@/api/custom.js";
export default {
  name: "addUnitTransition",
  components: {},
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    editData_in: Object,
    projectEnergytype: Array,
    tableData_in: Array,
    unitClass_in: Number,
    typeName: String
  },
  data() {
    return {
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "640px",
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "140px",
        labelPosition: "top",
        rules: {
          type: [
            {
              required: true,
              message: $T("请选择能源类型"),
              trigger: ["blur", "change"]
            }
          ],
          uniten: [
            {
              required: true,
              message: $T("请输入英文单位，如：mWh"),
              trigger: ["blur", "change"]
            }
          ],
          unitcn: [
            {
              required: true,
              message: $T("请输入中文单位，如：兆瓦时"),
              trigger: ["blur", "change"]
            }
          ],
          coef: [
            {
              required: true,
              message: $T("请输入单位转换系数"),
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElSelect_energytype: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_energytype_change_out
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElInput_unit: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      ElInputNumber_coef: {
        min: 0.001,
        max: 999999999,
        step: 2,
        precision: 3,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        value: "",
        controls: false,
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    energyKey() {
      const keyMap = {
        1: "energytype",
        2: "producttype",
        4: "id"
      };
      return keyMap[this.unitClass_in];
    }
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.ElOption_energytype.options_in = this.projectEnergytype;
      this.CetDialog_1.openTrigger_in = val;
      this.CetDialog_1.title = this.editData_in
        ? $T("编辑单位转换配置")
        : $T("新增单位转换配置");

      if (!this.editData_in) {
        this.reset();
        this.CetForm_1.resetTrigger_in = new Date().getTime();
        return;
      }

      const list = this.ElOption_energytype.options_in || [];

      const clickVal = list.find(
        item => item[this.energyKey] === this.editData_in.type
      );
      this.ElSelect_energytype.value = clickVal && clickVal.id;

      this.CetForm_1.data = this.editData_in;
      this.CetForm_1.resetTrigger_in = new Date().getTime();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    reset() {
      this.ElSelect_energytype.value = null;
      this.CetForm_1.data = {
        type: "",
        unitMultiplierName: "",
        unitmultiplier: "",
        transformCoef: ""
      };
    },
    async ElSelect_energytype_change_out(val) {
      const list = this.ElOption_energytype.options_in || [];
      const clickType = list.find(item => item.id === val);
      const type = clickType?.[this.energyKey] ?? null;

      if (!type) {
        return;
      }
      this.CetForm_1.data.type = type;

      const params = {
        projectUnitClassify: this.unitClass_in
      };
      const data = [type];

      const res = await commonApi.getDefaultUnitSetting(data, params);
      if (res.code !== 0) {
        return;
      }

      const basicUnitSymbolName = res.data?.[0]?.basicUnitSymbolName ?? "";
      this.$set(
        this.CetForm_1.data,
        "basicUnitSymbolName",
        basicUnitSymbolName
      );
    },
    async addUnitTransition(val) {
      const data = [
        {
          type: val.type,
          projectunitclassify: this.unitClass_in,
          id: val.id,
          projectid: this.projectId,
          uniten: val.uniten,
          unitcn: val.unitcn,
          coef: val.coef
        }
      ];
      const response = await commonApi.addUnitTransition(data);
      if (response.code !== 0) {
        return;
      }

      this.$message.success($T("保存成功"));
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
      this.$emit("updata_out");
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    CetForm_1_saveData_out(val) {
      this.addUnitTransition(val);
    }
  },
  activated: function () {}
};
</script>
