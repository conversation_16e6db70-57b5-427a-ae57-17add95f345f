<template>
  <!--地图容器-->
  <div class="map">
    <div class="mapContainer"></div>
    <slot name="infobox"></slot>
    <div
      v-if="searchSite && searchSite.isNeed === true"
      :style="{
        position: searchSite.position,
        top: searchSite.top,
        left: searchSite.left,
        bottom: searchSite.bottom,
        right: searchSite.right,
        zIndex: searchSite.zIndex
      }"
    >
      <div id="r-result">
        <input
          ref="searchInput"
          type="text"
          class="input-on-map"
          id="suggestId"
          size="20"
          :value="$T('百度')"
          style="width: 150px"
          :placeholder="$T('请输入地点')"
        />
      </div>
      <div
        id="searchResultPanel"
        style="
          border: 1px solid #c0c0c0;
          width: 150px;
          height: auto;
          display: none;
          z-index: 30000;
        "
      ></div>
      <div class="editDiv">
        <el-button
          v-if="!!curPolygon && isOnline"
          type="plain"
          class="drawPanel"
          @click="onEditMap()"
        >
          {{ !editSwitch ? $T("开启区域编辑") : $T("关闭区域编辑") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import mapConfig from "./map-config.json";
import { httping } from "@omega/http";
export default {
  name: "baidumap",
  props: [
    "points",
    "customMark",
    "customPolygon",
    "searchSite",
    "isOpenWin",
    "homeMap",
    "isMakePath",
    "routeData",
    "mapRange",
    "mapStyle",
    "showArea"
  ],
  data() {
    return {
      curPolygon: null,
      editSwitch: false,
      map: {},
      mark: {},
      infoWindow: {},
      address: "",
      customPoint: "",
      winPoint: {},
      zoom: 0,
      img: "../../../../static/assets/map/normal.png",
      startIcon: "../../../../static/assets/map/start.png", //人员轨迹开始点图标
      endIcon: "../../../../static/assets/map/end.png", //人员轨迹接受点图标
      operatorLists: []
    };
  },
  watch: {
    customPolygon(val) {
      this.initPolygon();
    },
    points: {
      handler: function (val) {
        this.points = val;
        this.initMapPoint();
      },
      deep: true
    },
    isOpenWin: function () {
      var me = this;
      me.map.openInfoWindow(me.infoWindow, me.winPoint);
    },
    homeMap: function (val) {
      // this.getOperatorInfo()
    }
  },
  activated() {
    var me = this,
      theme;
    theme = sessionStorage.getItem("interfaceStyle");
    if (theme && !this.mapStyle) me.changeMapStyle(theme);
    if (this.mapStyle) me.changeMapStyle(this.mapStyle);
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    interFaceStyle() {
      return this.$store.state.interFaceStyle;
    },
    mapZoomMax() {
      if (!this.$store.state.systemCfg.onLine) {
        return this.$store.state.systemCfg.mapZoomMax || 18;
      }
      return 18;
    },
    isOnline() {
      return this.$store.state.systemCfg.onLine;
    }

    //   this.operatorLists = this.operatorList
    //   this.makeMarkers()
    //   return this.operatorList
    // }
  },
  mounted() {
    this.pro();
    this.$nextTick(() => {
      this.initMap();
    });
  },
  methods: {
    pro() {
      window.__WUJIE_RAW_WINDOW__.parent["$BAIDU$"] = {};
      const proxyBmap = new Proxy(
        window.__WUJIE_RAW_WINDOW__.parent["$BAIDU$"],
        {
          get: function (target, key) {
            return window.__WUJIE_RAW_WINDOW__["$BAIDU$"][key];
          },
          set: function (target, key, val) {
            target[key] = val;
          }
        }
      );
      window.__WUJIE_RAW_WINDOW__.parent["$BAIDU$"] = proxyBmap;
    },
    initMap() {
      var me = this;
      // 创建Map实例
      var mapContainer = me.$el.getElementsByClassName("mapContainer")[0];
      var map = new BMap.Map(mapContainer, {
        enableMapClick: true,
        minZoom: 1,
        maxZoom: this.mapZoomMax
      });

      // 初始化地图,设置中心点坐标和地图级别
      map.centerAndZoom(new BMap.Point(116.331398, 39.897445), 5); //116.331398, 39.897445

      //添加控件和比例尺
      // 百度地图API功能
      var bottom_left_control = new BMap.ScaleControl({
        // anchor: BMap_ANCHOR_BOTTOM_RIGHT
      }); // 左上角，添加比例尺
      var bottom_left_navigation = new BMap.NavigationControl({
        // anchor: BMap_ANCHOR_BOTTOM_RIGHT,
        // type: BMap_NAVIGATION_CONTROL_ZOOM
      }); //左上角，添加默认缩放平移控件
      map.addControl(bottom_left_control);
      // map.addControl(bottom_left_navigation);

      // 开启鼠标滚轮缩放
      map.enableScrollWheelZoom(true);
      //如果customMark为true，添加地图选点功能
      if (me.customMark === true) {
        me.addCustomMark(map);
      }
      //如果searchSite为true，添加搜索地点功能
      if (me.searchSite && me.searchSite.isNeed === true && me.isOnline) {
        me.addSearchSiteFunc(map);
      }
      me.map = map;

      var sContent = document.getElementsByClassName("infoBox")[0]; //读取弹窗infobox的dom
      me.infoWindow = new BMap.InfoWindow(sContent); // 创建信息窗口对象

      this.initPolygon();
      this.initMapPoint();
      // this.getOperatorInfo()
      if (this.isMakePath) {
        this.makePath();
      }

      var theme = sessionStorage.getItem("interfaceStyle");
      if (theme && !this.mapStyle) me.changeMapStyle(theme);
      if (this.mapStyle) me.changeMapStyle(this.mapStyle);
    },
    reset() {
      setTimeout(() => {
        this.initMapPoint();
      }, 100); //TODO:卧槽他大爷百度地图都是神一样的代码
    },
    initMapPoint() {
      //如果传入了点，就创建标记点
      if (this.points && this.points.length > 0) {
        this.addMarkToMap();
      }
    },
    closeEditMap() {
      this.editSwitch = false;
      this.switchEditPolygon();
    },
    getAreaData() {
      return this.curPolygon ? this.curPolygon.getPath() : "";
    },
    getPolygonData(x, y) {
      if (isNaN(x) || isNaN(y)) {
        return null;
      }
      let iZoom = this.map.getZoom();
      let iMargin = 0.25 - (3.16 / 200) * iZoom; //按照缩放级别16显示0.0095,10显示0.1
      return JSON.stringify([
        new BMap.Point(x + iMargin, y + iMargin),
        new BMap.Point(x + iMargin, y - iMargin),
        new BMap.Point(x - iMargin, y - iMargin),
        new BMap.Point(x - iMargin, y + iMargin)
      ]);
    },

    initPolygon(polygon) {
      if (!this.showArea || !this.isOnline) {
        return;
      }
      if (!this.customPolygon || this.customPolygon === "") {
        if (this.curPolygon) {
          this.curPolygon.remove();
        }
        this.curPolygon = null;
        return;
      }
      let originData = JSON.parse(this.customPolygon);
      let pointList = originData.map(item => {
        return new BMap.Point(item.lng, item.lat);
      });
      if (!this.curPolygon) {
        var polygon = new BMap.Polygon(pointList, {
          strokeColor: "blue",
          strokeWeight: 1,
          strokeOpacity: 0.5
        });
        polygon.name = "polygon";
        this.curPolygon = polygon;
      } else {
        this.curPolygon.setPath(pointList);
      }

      setTimeout(() => {
        this.map.addOverlay(this.curPolygon);
      }, 100);
    },

    onEditMap() {
      this.editSwitch = !this.editSwitch;
      this.switchEditPolygon();
    },
    switchEditPolygon() {
      // 绘制面curPoint存在就直接画
      if (!this.curPolygon) {
        return;
      }
      if (this.editSwitch) {
        this.curPolygon.enableEditing();
        if (this.mark && this.customMark) {
          this.mark.disableDragging();
        }
      } else {
        if (this.mark && this.customMark) {
          this.mark.enableDragging();
        }
        this.curPolygon.disableEditing();
      }
    },
    changeMapStyle(val) {
      var me = this;
      if (!me.map || !this.isOnline) {
        return;
      }
      if (val === "darkBlue") {
        // 切换深夜主题
        me.map.setMapStyleV2({ styleJson: mapConfig });
        // me.map.setMapStyle({style: 'midnight'})
      } else if (val === "simple") {
        me.map.setMapStyle({ style: "" });
      }
    },

    //添加标记点
    addMarkToMap() {
      if (this.map && this.map.getOverlays) {
        let allOverlay = this.map
          .getOverlays()
          .filter(i => i.name !== "polygon");
        allOverlay.forEach(item => {
          this.map.removeOverlay(item);
        });
      }
      // this.map.clearOverlays();
      var geoCoordMap = this.points,
        pointArray = new Array();
      console.log("重新打点", geoCoordMap.length);

      //给所有点添加弹窗
      for (var i = 0; i < geoCoordMap.length; i++) {
        var lng = parseFloat(geoCoordMap[i].longitude),
          lat = parseFloat(geoCoordMap[i].latitude);

        //筛选有效的经纬度值才能创建标注，不然无效的会在经纬度(0,0)处创建标注
        if (lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90) {
          //自定义标记点图标
          if (geoCoordMap[i].customMarkerImg) {
            var img = geoCoordMap[i].customMarkerImg;
            var myIcon = new BMap.Icon(
              img.url, //图片路径
              new BMap.Size(img.width, img.height), //图片容器大小
              {
                //图标相对地理位置的偏移值
                anchor: new BMap.Size(img.width / 2, img.height),
                //图片的大小，比容器大的话显示不全
                imageSize: new BMap.Size(img.width, img.height)
              }
            );
            // 创建标注
            var marker = new BMap.Marker(new BMap.Point(lng, lat), {
              icon: myIcon,
              title: geoCoordMap[i].markerTitle
            });
          } else {
            // 创建标注
            var marker = new BMap.Marker(new BMap.Point(lng, lat), {
              title: geoCoordMap[i].markerTitle
            });
          }

          this.map.addOverlay(marker); // 将标注添加到地图中
          pointArray.push(new BMap.Point(lng, lat)); //设置最佳视野时用到
          // this.addClickHandler(marker, geoCoordMap[i]); //给每个标记点添加监听事件
        }
        if (this.customMark) {
          this.mark = marker;
        }
        // var marker = new BMap.Marker(new BMap.Point(lng, lat));
        // map.addOverlay(marker);// 将标注添加到地图中
        // pointArray.push(new BMap.Point(lng, lat));//设置最佳视野时用到
        // this.addClickHandler(marker, geoCoordMap[i]);//给每个标记点添加监听事件
      }

      //让所有点在视野范围内
      var viewport = this.map.getViewport(pointArray); //获取最佳视野

      //如果上次和本次的地图层级一样的话，点击标记点之后，地图中心点会跑到上次的中心点
      if (viewport.zoom === this.zoom) {
        if (this.zoom > 5) {
          viewport.zoom = this.zoom - 1;
        } else {
          viewport.zoom = this.zoom + 1;
        }
      }
      this.zoom = viewport.zoom;
      if (pointArray.length === 1) {
        viewport.zoom = this.mapZoomMax > 16 ? 16 : this.mapZoomMax;
      }
      this.map.setViewport(viewport, { enableAnimation: true }); //设置最佳视野
    },

    //点击标记点触发
    addClickHandler(marker, data) {
      let me = this;
      marker.addEventListener("click", function (e) {
        let p = e.target;
        me.winPoint = new BMap.Point(p.getPosition().lng, p.getPosition().lat);

        //infoBox需要用到的值
        me.$emit("datachange", data);
      });
    },

    //添加地图选点功能
    addCustomMark(map) {
      var me = this,
        geoc = new BMap.Geocoder(); //创建一个地址解析器的实例

      //给地图添加监听事件，点击地图添加一个标记点
      map.addEventListener("click", function (e) {
        if (me.editSwitch) {
          return;
        }
        map.clearOverlays(); //先清除原有的点
        if (me.mark) {
          //如果存在添加的点，则移除；不存在，就添加。目的是只能在地图上添加一个点
          map.removeOverlay(me.mark);
        }
        me.mark = new BMap.Marker(new BMap.Point(e.point.lng, e.point.lat));
        map.addOverlay(me.mark);
        if (!me.isOnline) {
          me.$emit("customPoint", [e.point]);
        }
        geoc.getLocation(e.point, function (rs) {
          me.address = rs?.address;

          //监听customPoint事件，返回所选点的经纬度和地址
          me.$emit("customPoint", [e.point, me.address]);
        });

        //设置标记点可拖拽
        me.mark.enableDragging();

        //给点添加监听事件，拖拽后执行程序
        me.mark.addEventListener("dragend", function (e) {
          geoc.getLocation(e.point, function (rs) {
            me.address = rs?.address;
            me.$emit("customPoint", [e.point, me.address]);
          });
        });
      });
    },

    //添加搜索位置功能
    addSearchSiteFunc(map) {
      const inputEl = this.$refs.searchInput;
      let me = this,
        ac = new BMap.Autocomplete({
          input: inputEl,
          location: map,
          onSearchComplete: function (results) {
            if (window.__POWERED_BY_WUJIE__) {
              const rect = inputEl.getBoundingClientRect();
              const bodyRect = document
                .querySelector("body")
                .getBoundingClientRect();
              const main = document.querySelectorAll(
                ".tangram-suggestion-main"
              );
              const nowSearch = main[main.length - 1];
              nowSearch.style.left = rect.left - bodyRect.left + "px";
              nowSearch.style.top = rect.top - bodyRect.top + 30 + "px";
            }
          }
        }); //建立一个自动完成的对象

      ac.addEventListener("onhighlight", function (e) {
        //鼠标放在下拉列表上的事件
        let str = "";
        let _value = e.fromitem.value;
        let value = "";
        if (e.fromitem.index > -1) {
          value =
            _value.province +
            _value.city +
            _value.district +
            _value.street +
            _value.business;
        }
        str =
          "FromItem<br />index = " +
          e.fromitem.index +
          "<br />value = " +
          value;

        value = "";
        if (e.toitem.index > -1) {
          _value = e.toitem.value;
          value =
            _value.province +
            _value.city +
            _value.district +
            _value.street +
            _value.business;
        }
        str +=
          "<br />ToItem<br />index = " +
          e.toitem.index +
          "<br />value = " +
          value;
        me.getEl("searchResultPanel").innerHTML = str;
      });

      let myValue;
      ac.addEventListener("onconfirm", function (e) {
        //鼠标点击下拉列表后的事件
        let _value = e.item.value;
        myValue =
          _value.province +
          _value.city +
          _value.district +
          _value.street +
          _value.business;
        me.getEl("searchResultPanel").innerHTML =
          "onconfirm<br />index = " +
          e.item.index +
          "<br />myValue = " +
          myValue;

        me.setPlace(myValue);
      });
    },

    setPlace(val) {
      var map = this.map;

      //map.clearOverlays();    //清除地图上所有覆盖物
      function myFun() {
        var pp = local.getResults().getPoi(0).point; //获取第一个智能搜索的结果
        map.centerAndZoom(pp, 18);
        //map.addOverlay(new BMap.Marker(pp));    //添加标注
      }

      var local = new BMap.LocalSearch(map, {
        //智能搜索
        onSearchComplete: myFun
      });
      local.search(val);
    },

    getEl(id) {
      return document.getElementById(id);
    },
    //运维人员marker点击
    personClick(content, marker) {
      let self = this;
      marker.addEventListener("click", function (e) {
        self.openInfo(content, e);
      });
    },
    //打开运维人员信息窗体
    openInfo(content, e) {
      let p = e.target;
      let opts = {
        width: 10, // 信息窗口宽度
        height: 10, // 信息窗口高度
        title: "", // 信息窗口标题
        enableMessage: true //设置允许信息窗发送短息
      };
      let scontent = "";
      if (content.state == 2) {
        scontent = `<div class='user-info-window'>
           <p><span class="user-icon"></span><span class="user-name cet-theme-font">${
             content.userName
           }</span><span class="user-working"></span><span class="cet-theme-font">${$T(
          "工作中"
        )}</span></p>
           <p><span class='goToUser'>${$T("行程查询")}</span></p>
         </div>`;
      } else {
        scontent = `<div class='user-info-window'>
           <p><span class="user-icon"></span><span class="user-name cet-theme-font">${
             content.userName
           }</span><span class="user-waiting"></span><span class="cet-theme-font">${$T(
          "待命中"
        )}</span></p>
           <p><span class='goToUser'>${$T("行程查询")}</span></p>
         </div>`;
      }

      let point = new BMap.Point(p.getPosition().lng, p.getPosition().lat);
      let infoWindow = new BMap.InfoWindow(scontent);
      this.map.openInfoWindow(infoWindow, point);
      let me = this;
      document.getElementsByClassName("goToUser")[0].onclick = function () {
        me.$router.push({ path: "/user" });
        me.map.closeInfoWindow();
      };
    },
    //获取运维人员信息生成marker
    getOperatorInfo() {
      let self = this;
      if (this.operatorLists.length > 0) {
        self.makeMarkers();
      } else {
        httping({
          url: `custom/api/pc/cm/location/operator/locations?token=${self.token}`,
          method: "GET"
        }).then(function (res) {
          self.operatorLists = res.data;
          self.makeMarkers();
        });
      }
    },
    makeMarkers() {
      let self = this;
      let myIcon = new BMap.Icon(self.img, new BMap.Size(50, 50));
      let items = this.operatorLists || [];
      let len = items.length;
      for (let i = 0; i < len; i++) {
        let item = items[i];

        if (item.location) {
          let lng = parseFloat(item.location.longitude);
          let lat = parseFloat(item.location.latitude);
          if (lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90) {
            let loc = new BMap.Point(
              item.location.longitude,
              item.location.latitude
            );
            let marker = new BMap.Marker(loc, { icon: myIcon });
            self.map.addOverlay(marker);
            self.personClick(item, marker);
          }
        }
      }
    },
    //人员轨迹线开始接受点
    makePath() {
      // this.map.clearOverlays()
      let pointList = this.routeData;
      if (pointList.length == 0) {
        return;
      }

      let firstLine = pointList[0];
      this.makeStartAndEndMaker(firstLine[0], this.startIcon);

      for (let i = 0; i < pointList.length; i++) {
        let polyline = pointList[i];
        let points = [];
        for (let j = 0; j < polyline.length; j++) {
          let lng = polyline[j].longitude;
          let lat = polyline[j].latitude;
          let point = new BMap.Point(lng, lat);
          points.push(point);
        }
        let drawPolyLine = new BMap.Polyline(points, {
          strokeColor: "blue",
          strokeWeight: 3,
          strokeOpacity: 0.5
        }); //创建弧线对象
        this.map.addOverlay(drawPolyLine);
        this.setMapViewport();
      }

      let lastLine = pointList[pointList.length - 1];
      let endPoint = lastLine[lastLine.length - 1];
      this.makeStartAndEndMaker(endPoint, this.endIcon);
    },
    //添加开始于结束点位置
    makeStartAndEndMaker(polyline, icon) {
      let lng = polyline.longitude;
      let lat = polyline.latitude;
      let loc = new BMap.Point(lng, lat);
      let startAndEndIcon = new BMap.Icon(icon, new BMap.Size(21, 30));
      let marker = new BMap.Marker(loc, { icon: startAndEndIcon });
      this.map.addOverlay(marker);
    },
    //设置运维人员轨迹显示视窗
    setMapViewport() {
      for (let key in this.mapRange) {
        if (!this.mapRange[key]) {
          return;
        }
      }
      let points = [];
      points.push(
        new BMap.Point(this.mapRange.minLongitude, this.mapRange.minLatitude)
      );
      points.push(
        new BMap.Point(this.mapRange.maxLongitude, this.mapRange.maxLatitude)
      );
      let viewport = this.map.getViewport(points);
      this.map.setViewport(viewport);
    }
  }
};
</script>

<style lang="scss" scoped>
#r-result {
  position: relative;
  left: -1px;
}
.editDiv {
  display: flex;
  justify-content: center;
  width: 200px;
  position: absolute;
  left: -26px;
  top: 30px;
  .drawPanel {
  }
}
.map {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  z-index: -1;
  font-family: "微软雅黑";
}

.mapContainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  // z-index: -1;
  font-family: "微软雅黑";
}
</style>

<style>
.tangram-suggestion-main {
  z-index: 30000;
}

.user-info-window {
  width: 210px;
  height: 40px;
}

.user-info-window .user-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  /* background: url(../assets/operationStaff/user-icon.png) center center
    no-repeat; */
  background-size: 100% 100%;
  vertical-align: middle;
}

.user-info-window .user-name {
  display: inline-block;
  width: 118px;
}

.user-info-window .user-working {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-left: 0px;
  margin-right: 6px;
  /* background: url(../assets/operationStaff/user-state.png) center center
    no-repeat; */
  background-size: 100% 100%;
  vertical-align: middle;
}

.user-info-window .user-waiting {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-left: 0px;
  margin-right: 6px;
  /* background: url(../assets/operationStaff/waiting-state.png) center center
    no-repeat; */
  background-size: 100% 100%;
  vertical-align: middle;
}

.user-info-window .goToUser {
  text-align: center;
  cursor: pointer;
  display: inline-block;
  width: 70px;
  height: 20px;
  line-height: 20px;
  border-radius: 10px;
  background: linear-gradient(to right, #24d0ca, #0a99d5);
  background-repeat: no-repeat;
  color: #fff;
  margin-left: 140px;
}
</style>
