<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <CustomSteps class="custom-steps p0" :active="stepActive" :steps="steps" />
    <div class="mt-J1 content selectContent" v-if="stepActive === 0">
      <div
        id="cloudProjectConfig_batchImport_nodeType"
        class="selectBox nodeType energySelect flex-col flex"
        :class="{
          light: lightTheme
        }"
      >
        <div class="titleBox flex flex-row">
          <div class="icon mr-J3"></div>
          <div class="title">{{ $T("节点类型") }}</div>
        </div>
        <div class="ElSelect mt-J3 mb-J3">
          <ElSelect
            class="fullwidth"
            v-model="ElSelect_nodeType.value"
            v-bind="ElSelect_nodeType"
            v-on="ElSelect_nodeType.event"
          >
            <ElOption
              v-for="item in ElOption_nodeType.options_in"
              :key="item[ElOption_nodeType.key]"
              :label="item[ElOption_nodeType.label]"
              :value="item[ElOption_nodeType.value]"
              :disabled="item[ElOption_nodeType.disabled]"
            ></ElOption>
          </ElSelect>
        </div>
      </div>
      <div
        id="cloudProjectConfig_batchImport_energyType"
        class="selectBox energyType flex-col flex"
        :class="{
          light: lightTheme
        }"
      >
        <div class="titleBox flex flex-row">
          <div class="icon mr-J3"></div>
          <div class="title">{{ $T("能源类型") }}</div>
        </div>
        <div class="ElSelect mt-J3 mb-J3">
          <ElSelect
            class="w-full"
            v-model="ElSelect_energyType.value"
            v-bind="ElSelect_energyType"
            v-on="ElSelect_energyType.event"
          >
            <ElOption
              v-for="item in ElOption_energyType.options_in"
              :key="item[ElOption_energyType.key]"
              :label="item[ElOption_energyType.label]"
              :value="item[ElOption_energyType.value]"
              :disabled="item[ElOption_energyType.disabled]"
            ></ElOption>
          </ElSelect>
        </div>
      </div>
    </div>
    <div class="mt-J1 tableContent" v-if="stepActive === 1">
      <BatchConfig
        ref="batchConfig"
        :energyType_in="ElSelect_energyType.value"
        :currentNode_in="currentNode_in"
        :nodeLabel_in="nodeLabel"
      />
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
        class="mr-J1"
      ></CetButton>
      <CetButton
        v-if="stepActive === 0"
        v-bind="CetButton_next"
        v-on="CetButton_next.event"
      ></CetButton>
      <CetButton
        v-if="stepActive === 1"
        v-bind="CetButton_goBack"
        v-on="CetButton_goBack.event"
        class="mr-J1"
      ></CetButton>
      <CetButton
        id="cloudProjectConfig_batchImport_confirm"
        v-if="stepActive === 1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import commonApi from "@/api/custom.js";
import omegaTheme from "@omega/theme";
import { findNode } from "@/utils/analysisServiceConfig.js";
import BatchConfig from "./batchConfig/index.vue";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import { CustomSteps } from "eem-base/components";
export default {
  name: "batchImport",
  components: {
    BatchConfig,
    CustomSteps
  },
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    currentNode_in: Object,
    netWork: Boolean
  },
  data() {
    return {
      steps: [
        {
          title: $T("选择节点及能源类型"),
          description: $T("进行能源及节点类型配置")
        },
        {
          title: $T("层级信息配置"),
          description: $T("配置管网、管理层级信息")
        }
      ],
      nodeLabel: "",
      CetDialog_1: {
        title: $T("导入子级"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "1440px",
        top: "5vh",
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("完成"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_goBack: {
        visible_in: true,
        disable_in: false,
        title: $T("上一步"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_goBack_statusTrigger_out
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: $T("下一步"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      stepActive: 0,
      ElSelect_energyType: {
        value: "",
        placeholder: $T("请选择能源类型"),
        event: {}
      },
      ElOption_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_nodeType: {
        value: "",
        placeholder: $T("请选择节点类型"),
        event: {}
      },
      ElOption_nodeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    lightTheme() {
      return omegaTheme.theme === "light";
    }
  },
  watch: {
    visibleTrigger_in(val) {
      this.init();
      this.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    async CetButton_confirm_statusTrigger_out() {
      // 如果已有过滤条件存在，则不进行保存
      if (!this.ifHandsontableFilter()) return;
      const saveData = this.getSaveData();
      if (!saveData) return;
      const res = await customApi.organizationConfigSaveImportConfig(saveData);
      if (res.code !== 0) return;
      const msgList = res.data;
      if (msgList?.length) {
        this.$message({
          dangerouslyUseHTMLString: true,
          type: "error",
          message: msgList.join("<br/>")
        });
        return;
      }
      this.$message.success($T("操作成功"));
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.$emit("batchImportSave_out");
    },
    ifHandsontableFilter() {
      const handsontableFilter =
        this.$refs.batchConfig.$refs.handsontable.handsontableFilter;
      if (handsontableFilter) {
        this.$message.warning($T("请点击重置全部过滤，再点生成配置！"));
        return false;
      }
      return true;
    },
    getSaveData() {
      let tableData = this.$refs.batchConfig.$refs.handsontable.getTableData();
      if (!tableData?.length) {
        this.$message.warning($T("请选择设备"));
        return;
      }
      const roomConfig = this.$refs.batchConfig.roomConfig;

      const checkNamePattern = common.pattern_name.pattern;
      const checkNameLength = common.check_name.max;
      // 第xx行元件类型未选择
      const checkComponentTypeRequired = [];
      // 第xx行所属功能房未选择
      const checkFunctionalRoomNameRequired = [];
      const checkFunctionalRoomNameLength = [];
      const checkFunctionalRoomNameCharacter = [];

      tableData.forEach((item, index) => {
        if (item.recordStatus === 2) {
          return;
        }
        item.rowNumber = index + 1;
        item.lineFunctionType = this.getLineFunctionType(
          item.componentType,
          roomConfig
        );
        // 元件类型由名称转为modelLabel
        const list = roomConfig?.children ?? [];
        item.componentType = list.find(
          i => i.name === item.componentType
        )?.modelLabel;

        // 校验检查
        if (!item.componentType) {
          checkComponentTypeRequired.push(index + 1);
        }
        if (!item.functionalRoomName) {
          checkFunctionalRoomNameRequired.push(index + 1);
        }
        if (item.functionalRoomName?.length > checkNameLength) {
          checkFunctionalRoomNameLength.push(index + 1);
        }
        if (!checkNamePattern.test(item.functionalRoomName)) {
          checkFunctionalRoomNameCharacter.push(index + 1);
        }
      });

      if (
        checkComponentTypeRequired.length ||
        checkFunctionalRoomNameRequired.length ||
        checkFunctionalRoomNameLength.length ||
        checkFunctionalRoomNameCharacter.length
      ) {
        let text = "";
        const roomName =
          this.ElSelect_energyType.value === 2
            ? $T("所属配电房")
            : $T("所属管道房");
        if (checkComponentTypeRequired.length) {
          text += $T(
            "第【{0}】行元件类型未选择",
            checkComponentTypeRequired.join("、")
          );
          text += "</br>";
        }
        if (checkFunctionalRoomNameRequired.length) {
          text += $T(
            `第【{0}】行{1}未选择`,
            checkFunctionalRoomNameRequired.join("、"),
            roomName
          );
          text += "</br>";
        }
        if (checkFunctionalRoomNameLength.length) {
          text += $T(
            `第【{0}】行{1}名称过长`,
            checkFunctionalRoomNameLength.join("、"),
            roomName
          );
          text += "</br>";
        }
        if (checkFunctionalRoomNameCharacter.length) {
          text += $T(
            `第【{0}】行{1}存在特殊字符`,
            checkFunctionalRoomNameCharacter.join("、"),
            roomName
          );
          text += "</br>";
        }
        this.$message({
          type: "warning",
          dangerouslyUseHTMLString: true,
          message: text
        });
        return;
      }

      tableData = tableData.filter(i => i.recordStatus !== 2);
      if (!tableData?.length) return;
      return {
        importConfigVos: tableData,
        manageNode: {
          id: this.currentNode_in.id,
          modelLabel: this.currentNode_in.modelLabel
        },
        energyType: this.ElSelect_energyType.value,
        nodeLabel: this.nodeLabel
      };
    },
    // 从功能房中根据元件类型解析出线功能
    getLineFunctionType(componentType, roomConfig) {
      const list = roomConfig?.children ?? [];
      const item = list.find(i => i.name === componentType);
      return item?.lineFunctionType;
    },
    CetButton_goBack_statusTrigger_out() {
      const list = this.$refs.batchConfig.$refs.handsontable.getTableData();
      const editFlag = list.find(item => item.recordStatus === -1);
      if (!editFlag) {
        this.stepActive = 0;
        return;
      }
      // 提示是否放弃
      this.$confirm(
        $T("返回上一步配置将不会保存，是否返回上一步?"),
        $T("提示"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(() => {
          this.stepActive = 0;
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    CetButton_next_statusTrigger_out() {
      if (!this.ElSelect_nodeType.value) {
        this.$message.warning($T("请选择节点类型"));
        return;
      }
      if (!this.ElSelect_energyType.value) {
        this.$message.warning($T("请选择能源类型"));
        return;
      }
      const nodeType = this.ElOption_nodeType.options_in.find(
        i => i.id === this.ElSelect_nodeType.value
      );
      this.nodeLabel = nodeType?.modelLabel;
      this.stepActive = 1;
    },
    init() {
      this.stepActive = 0;
      this.ElSelect_energyType.value = null;
      this.ElSelect_nodeType.value = null;
      this.queryProjectEnergy();
      this.queryNodeTypes();
    },
    async queryProjectEnergy() {
      const energyTypeRes =
        await commonApi.organizationConfigFilterEnergyType();
      const totalEnergyTypes = energyTypeRes?.data ?? [];
      const params = {
        projectId: this.projectId
      };
      const res = await commonApi.queryProjectEnergyList(params);
      if (res.code !== 0) return;
      const energytypeList = res.data || [];
      // 过滤折标能源类型
      this.ElOption_energyType.options_in = energytypeList.filter(
        item => !totalEnergyTypes.includes(item.energytype)
      );
      this.ElSelect_energyType.value =
        this.ElOption_energyType.options_in?.[0]?.energytype;
    },
    async queryNodeTypes() {
      let query = {
        modelLabel: this.currentNode_in.modelLabel
      };
      if (this.currentNode_in.modelLabel === "room") {
        query.roomType = this.currentNode_in.roomtype || null;
      }
      const node = findNode(query);
      const children = node?.children ?? [];
      let options = [];
      children.forEach(item => {
        const optionsItem = {
          id: `${item.modelLabel}_${item.roomType || null}`,
          modelLabel: item.modelLabel,
          roomType: item.roomType,
          name: item.name
        };
        const nodeType = this.netWork ? 2 : 1;
        if (item?.nodeTypes?.length && item.nodeTypes.includes(nodeType)) {
          options.push(optionsItem);
        }
      });
      this.ElOption_nodeType.options_in = options;
      this.ElSelect_nodeType.value = options?.[0]?.id;
    }
  }
};
</script>

<style lang="scss" scoped>
.tableContent {
  height: 580px;
  background-color: transparent;
  padding: 0;
}
.custom-steps {
  border-radius: var(--Ra1);
}
.selectContent {
  height: 580px;
  position: relative;
  .selectBox {
    width: 800px;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    .titleBox {
      height: 40px;
      line-height: 40px;
      .icon {
        height: 40px;
        width: 40px;
      }
      .title {
        font-size: 32px;
        @include font_color(ZS);
      }
    }
    .ElSelect {
      width: 100%;
      box-sizing: border-box;
      padding: 24px 40px;
      background: url("./assets/energyTypeBox.png");
      background-size: 100% 100%;
    }
    &.energyType {
      top: 268px;
      .titleBox .icon {
        background: url("./assets/energyTypeIcon.png");
        background-size: 100% 100%;
      }
    }
    &.nodeType {
      top: 82px;
      .titleBox .icon {
        background: url("./assets/nodeTypeIcon.png");
        background-size: 100% 100%;
      }
    }
    &.light {
      &.energyType .titleBox .icon {
        background: url("./assets/energyTypeIcon_light.png");
        background-size: 100% 100%;
      }
      &.nodeType .titleBox .icon {
        background: url("./assets/nodeTypeIcon_light.png");
        background-size: 100% 100%;
      }
      .ElSelect {
        background: url("./assets/energyTypeBox_light.png");
        background-size: 100% 100%;
      }
    }
  }
}
</style>
