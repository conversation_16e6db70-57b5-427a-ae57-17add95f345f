<template>
  <div>
    <div class="flex flex-row" v-if="fileName">
      <el-tooltip :content="fileName" effect="light">
        <div class="fileName flex-auto text-ellipsis">{{ fileName }}</div>
      </el-tooltip>
      <div class="el-icon-success successIcon mt-J"></div>
      <div class="upload ml-J3" @click="reUpload">{{ $T("重新上传") }}</div>
      <div class="deleteFile ml-J3" @click="deleteFile">{{ $T("删除") }}</div>
    </div>
    <div class="upload" v-else @click="upload">{{ $T("上传") }}</div>
    <el-upload
      v-show="false"
      class="upload"
      ref="elupload"
      action=""
      :accept="fileTypes_in.join(',')"
      :before-upload="handleBeforeUpload"
      :http-request="uploadFile"
      :limit="1"
      :multiple="false"
      :headers="{ Authorization: this.token }"
      :on-exceed="handleExceed"
      :show-file-list="false"
    >
      <input type="text" ref="input" />
    </el-upload>
  </div>
</template>

<script>
import customApi from "@/api/custom";
export default {
  name: "UploadFile",
  props: {
    filePath: String,
    fileTypes_in: {
      type: Array,
      default: () => [".xls", ".xlsx", ".docx", ".pdf"]
    },
    fileSize: Number
  },
  data() {
    return {};
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    fileName() {
      if (!this.filePath) return "";
      const fileName = this.filePath.split("/")[3];
      let fileNameArr = fileName.split("_");
      fileNameArr.length = fileNameArr.length - 1;
      return `${fileNameArr.join("_")}.${fileName.split(".")[1]}`;
    }
  },
  methods: {
    handleBeforeUpload(file) {
      if (!this.fileTypes_in?.length) return;
      let isOk = !!this.fileTypes_in.find(item => {
        return file.name.indexOf(item) != -1;
      });
      if (!isOk) {
        this.$message({
          type: "error",
          message: $T(`只能上传{0}格式文件`, this.fileTypes_in.join("/"))
        });
        return;
      }
      const uploadPicSize = this.fileSize || this.systemCfg.uploadPicSize;
      const isLimit100M = file.size / 1024 / 1024 < uploadPicSize;
      if (!isLimit100M) {
        this.$message.error($T("上传文件超过规定的最大上传大小"));
      }
      return isLimit100M;
    },
    async uploadFile({ file }) {
      const data = new FormData();
      data.append("file", file);
      const response = await customApi.commonUploadFile(data);
      if (response.code !== 0) {
        return;
      }
      this.$emit("update:filePath", response.data);
    },
    reUpload() {
      customApi.commonDeleteFile({ path: this.filePath }, true);
      this.$emit("update:filePath", "");
      this.$refs.elupload.clearFiles();
      this.upload();
    },
    upload() {
      this.$refs.input.click();
    },
    async deleteFile() {
      if (!this.filePath) {
        return;
      }
      await customApi.commonDeleteFile({ path: this.filePath }, true);
      this.$refs.elupload.clearFiles();
      this.$emit("update:filePath", "");
    },
    handleExceed() {
      this.$message.warning($T("当前限制选择 1 个文件"));
    }
  }
};
</script>

<style lang="scss" scoped>
.upload {
  cursor: pointer;
  color: var(--ZS);
}
.deleteFile {
  cursor: pointer;
  color: var(--Sta3);
}
.successIcon {
  color: var(--Sta1);
}
</style>
