<template>
  <div
    class="w-full h-full flex flex-row"
    v-loading="importing"
    :element-loading-text="$T('配置生成中')"
  >
    <div
      class="tree-box w-[315px] box-border p-[24px] mr-J2 border-solid border-t-0 border-l-0 border-b-0 border-r-[2px] border-B1"
    >
      <BusbarsectionTree
        ref="busbarsectionTree"
        :apiName="'topologyManageTreeLinenode'"
        :updataTreeData="updataTreeData"
        @checkedNodesOut="checkedNodesOut"
        @initTableData="initTableData"
      />
    </div>
    <div class="content-box flex-auto p-[24px] box-border flex flex-col">
      <el-empty
        class="fullheight"
        v-show="isLight && dataEmpty"
        :image-size="524"
        image="static/assets/empty_max_light.png"
      ></el-empty>
      <el-empty
        v-show="!isLight && dataEmpty"
        class="fullheight"
        :image-size="524"
        image="static/assets/empty_max.png"
      ></el-empty>
      <div class="flex flex-row justify-end items-center" v-show="!dataEmpty">
        <el-popover placement="bottom" trigger="hover" width="1120">
          <div class="text-T3 mb-J3">{{ $T("操作") }}</div>
          <div class="mb-J3">
            {{
              $T(
                "点击新增关联可在原设备下新建一条关联，复制原关联的所属配电房、线功能、管网名称及所属母线，可自定义修改连接元件类型及连接设备名称"
              )
            }}
          </div>
          <div class="text-T3 mb-J3">{{ $T("关联规则") }}</div>
          <img class="association-img" :src="associationImg" alt="" />
          <span class="text-T3 cursor-pointer" slot="reference">
            <i class="el-icon-question"></i>
          </span>
        </el-popover>
        <CetButton
          class="ml-J2"
          v-bind="CetButton_reset"
          v-on="CetButton_reset.event"
        ></CetButton>
        <CetButton
          class="ml-J2"
          v-bind="CetButton_delete"
          v-on="CetButton_delete.event"
          :disable_in="busbarsectionTable.deleteDisable"
          v-permission="'topologymanage_update'"
        ></CetButton>
        <CetButton
          class="ml-J2"
          v-bind="CetButton_preview"
          v-on="CetButton_preview.event"
        ></CetButton>
        <CetButton
          v-if="!editMode"
          class="ml-J2"
          v-bind="CetButton_association"
          v-on="CetButton_association.event"
          v-permission="'topologymanage_update'"
        ></CetButton>
        <CetButton
          v-if="editMode"
          class="ml-J2"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          v-if="editMode"
          class="ml-J2"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </div>
      <div class="flex-auto mt-J2" v-show="!dataEmpty">
        <BusbarsectionTable
          ref="busbarsectionTable"
          v-bind="busbarsectionTable"
          v-on="busbarsectionTable.event"
          :deleteDisable.sync="busbarsectionTable.deleteDisable"
          :showTooltip="editMode"
        />
      </div>
    </div>
    <TopologyChartPreview
      v-bind="topologyChartPreview"
      v-on="topologyChartPreview.event"
    />
    <MsgBox v-bind="msgBox" />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import BusbarsectionTree from "../components/tree.vue";
import BusbarsectionTable from "./busbarsectionTable.vue";
import TopologyChartPreview from "../components/topologyChartPreview.vue";
import omegaI18n from "@omega/i18n";
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import MsgBox from "./msgBox.vue";

export default {
  name: "busbarsectionPage",
  components: {
    BusbarsectionTree,
    BusbarsectionTable,
    TopologyChartPreview,
    MsgBox
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    connectionStatus() {
      return this.$store.state.importProgress.connectionStatus;
    },
    // ws 未连接时importing是true，所以监听的时候将未连接转为false
    importing() {
      if (!this.connectionStatus) {
        return false;
      }
      return this.$store.getters["importProgress/importing"](15);
    },
    dataEmpty() {
      return !this.checkedNodes?.length;
    },
    isLight() {
      return omegaTheme.theme === "light";
    },
    exceedLimit() {
      const data = this.$refs.busbarsectionTable?.getTableData() ?? [];
      return data?.length >= this.limit;
    },
    associationImg() {
      const currentTheme = omegaTheme.theme;
      if (["dark", "blue"].includes(currentTheme)) {
        return require("../assets/association-dark.png");
      }
      return require("../assets/association.png");
    }
  },
  data() {
    return {
      modified: false, // 是否编辑过数据
      treeStatusBack: null, // 上一次选择的关联状态
      checkedNodesBack: null, // 上一次选择的节点
      treeStatus: null, // 当前选择的关联状态
      checkedNodes: null, // 当前选择的节点
      editMode: false, // 编辑模式
      limit: 3000, // 最大限制
      deleteList: [], // 删除的数据
      addIndex: 1, // 新增数据的索引
      treeCheckNum: 0, // 节点树勾选次数
      updataTreeData: Date.now(), // 更新节点树数据
      CetButton_delete: {
        visible_in: true,
        disable_in: false,
        title: $T("批量删除配置"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_delete_statusTrigger_out
        }
      },
      CetButton_preview: {
        visible_in: true,
        disable_in: false,
        title: $T("效果预览"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preview_statusTrigger_out
        }
      },
      CetButton_association: {
        visible_in: true,
        disable_in: false,
        title: $T("拓扑配置"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_association_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置全部过滤"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      busbarsectionTable: {
        data: [],
        columns: [],
        clearFilterHandle: Date.now(),
        deleteDisable: true,
        event: {
          dataChange: () => {
            this.modified = true;
          }
        }
      },
      topologyChartPreview: {
        inputData_in: null,
        openTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        saveMode: false,
        event: {
          confirm_out: this.topologyChartPreview_confirm_out
        }
      },
      msgBox: {
        inputData_in: null,
        openTrigger_in: Date.now(),
        closeTrigger_in: Date.now()
      }
    };
  },
  watch: {
    // 进度完成之后刷新一次节点树、表格数据
    importing(val, old) {
      if (val === false && old === true) {
        this.updateData();
      }
    }
  },
  methods: {
    /**
     * 刷新节点树，并勾选住上一次所勾选的节点
     * 如果是第一次进来，并无进行节点勾选，则将节点树重新初始化即可
     */
    updateData() {
      this.editMode = false;
      if (this.treeCheckNum === 1) {
        this.updataTreeData = Date.now();
      } else {
        this.$refs.busbarsectionTree.updateTreeAndSetCheckedNodes(
          this.checkedNodes
        );
      }
    },

    /**
     * 节点树勾选输出
     * @param val 勾选节点数组
     * @param status 当前选择的关联状态
     */
    async checkedNodesOut(val, status) {
      this.treeCheckNum++;
      if (this.treeStatus !== status) {
        this.treeCheckNum = 1;
      }

      const checkedNodes =
        val?.filter(i => {
          return !["project", "room"].includes(i.modelLabel);
        }) ?? [];
      if (!checkedNodes?.length) {
        this.$message.warning($T("至少勾选一个设备节点"));
        this.checkedNodes = [];
        return;
      }

      // 如果选择节点超过最大限制，提示并回退上一次选择
      if (checkedNodes?.length > this.limit) {
        this.$message.warning($T("节点选择超出最大限制{0}", this.limit));
        this.treeCheckedGoBack(this.treeStatus, this.checkedNodes);
        return;
      }

      const canLeave = await this.abandonEditing();
      if (!canLeave) {
        this.treeCheckedGoBack(this.treeStatus, this.checkedNodes);
        return;
      }

      this.queryTable(status, checkedNodes);
    },

    async queryTable(status, checkedNodes) {
      if (this.treeStatusBack === null) {
        this.treeStatusBack = this._.cloneDeep(status);
        this.checkedNodesBack = this._.cloneDeep(checkedNodes);
      } else {
        this.treeStatusBack = this._.cloneDeep(this.treeStatus);
        this.checkedNodesBack = this._.cloneDeep(this.checkedNodes);
      }

      this.treeStatus = status;
      this.checkedNodes = checkedNodes;
      this.getTableData();
    },

    initTableData() {
      this.editMode = false;
      this.busbarsectionTable.data = [];
    },

    /**
     * 回退上一次节点勾选与状态
     */
    treeCheckedGoBack(treeStatus, checkedNodes) {
      this.$refs.busbarsectionTree.setCheckedAndTreeType(
        checkedNodes,
        treeStatus
      );
    },

    async getTableData() {
      await this.getColumn();
      this.busbarsectionTable.clearFilterHandle = Date.now();
      this.addIndex = 1;

      this.deleteList = [];
      const params = {
        energyType: 2,
        node:
          this.checkedNodes?.map(i => {
            return {
              id: i.id,
              modelLabel: i.modelLabel
            };
          }) ?? [],
        projectId: this.projectId
      };
      const res = await customApi.topologyManageConfigInfo(params);
      if (res.code !== 0) {
        // 回退一下状态与节点，避免下次点击的时候无法再回退了
        this.treeStatus = this._.cloneDeep(this.treeStatusBack);
        this.checkedNodes = this._.cloneDeep(this.checkedNodesBack);
        this.treeCheckedGoBack(this.treeStatusBack, this.checkedNodesBack);
        return;
      }
      const data = res.data || [];
      const lineFunctionType = new Map([
        [4, $T("进线")],
        [7, $T("馈线")]
      ]);
      data.forEach(item => {
        const {
          connectIds,
          lineNodeId,
          componentType,
          componentLineFunctionType
        } = item;
        item.tree_id = connectIds?.length ? connectIds.join("_") : lineNodeId;
        // 元件类型解析成文本，保存的时候再转回去
        if (componentType === "powertransformer") {
          item.componentType = $T("变压器");
        } else if (componentType === "linesegmentwithswitch") {
          item.componentType =
            lineFunctionType.get(componentLineFunctionType) || "";
        } else {
          item.componentType = "";
        }
        item.deleteStatus = false;
      });
      this.busbarsectionTable.data = data;
      this.modified = false;
    },

    async getColumn() {
      const vm = this;
      const typeList = [$T("进线"), $T("馈线"), $T("变压器")];

      const params = {
        projectId: this.projectId
      };

      const results = await Promise.all([
        customApi.topologyManageNode(params),
        customApi.topologyManageRoom(params)
      ]);
      const nodeData = results[0].data || [];
      const roomList = results[1].data || [];

      // 按不同房间生成所属母线、连接元件类型、元件所属配电房、连接设备名称的下拉选项
      const optionMap = {};
      nodeData.forEach(({ modelLabel, roomId, nameList, lineFunctionType }) => {
        if (["powertransformer", "busbarsection"].includes(modelLabel)) {
          if (!optionMap[roomId]) {
            optionMap[roomId] = {};
          }
          optionMap[roomId][modelLabel] = nameList;
        }
        if (modelLabel === "linesegmentwithswitch" && lineFunctionType === 4) {
          if (!optionMap[roomId]) {
            optionMap[roomId] = {};
          }
          optionMap[roomId].inDevice = nameList;
        }
        if (modelLabel === "linesegmentwithswitch" && lineFunctionType === 7) {
          if (!optionMap[roomId]) {
            optionMap[roomId] = {};
          }
          optionMap[roomId].outDevice = nameList;
        }
      });

      const columns = [
        {
          className: "htLeft cell-bg1",
          data: "roomName",
          type: "text",
          label: $T("所属配电房"),
          columnSorting: true,
          readOnly: true,
          minWidth: omegaI18n.locale == "en" ? 170 : 150
        },
        {
          className: "htLeft cell-bg1",
          data: "lineFunctionName",
          type: "text",
          label: $T("线功能"),
          readOnly: true
        },
        {
          className: "htLeft cell-bg1",
          data: "lineNodeName",
          type: "text",
          label: $T("管网名称"),
          readOnly: true,
          minWidth: omegaI18n.locale == "en" ? 180 : 150
        },
        {
          className: this.isLight
            ? "htLeft cell-bg2-opacity"
            : "htLeft cell-bg2",
          data: "busBarSectionName",
          type: "autocomplete",
          strict: false,
          label: $T("所属母线"),
          minWidth: omegaI18n.locale == "en" ? 170 : 150,
          readOnly: !this.editMode,
          source: function (query, process) {
            const rowData = this.instance.getSourceDataAtRow(this.row);
            const roomId = rowData.roomId;
            const nameList = optionMap[roomId]?.busbarsection ?? [];
            process(nameList);
          },
          validator: function (value, callback) {
            if (!value) {
              callback(true);
              return true;
            }

            const isValid = vm.checkText(value);
            callback(isValid);
            return isValid;
          }
        },
        {
          className: "htLeft cell-bg3",
          data: "componentType",
          type: "autocomplete",
          strict: true,
          label: $T("连接元件类型"),
          readOnly: !this.editMode,
          source: typeList,
          minWidth: omegaI18n.locale == "en" ? 230 : 150
        },
        {
          className: "htLeft cell-bg3",
          data: "componentRoomName",
          type: "autocomplete",
          strict: false,
          label: $T("元件所属配电房"),
          readOnly: !this.editMode,
          source: roomList.map(i => i.name),
          minWidth: omegaI18n.locale == "en" ? 290 : 150,
          validator: function (value, callback) {
            if (!value) {
              callback(true);
              return true;
            }

            const isValid = vm.checkText(value);
            callback(isValid);
            return isValid;
          }
        },
        {
          className: "htLeft cell-bg3",
          data: "componentName",
          type: "autocomplete",
          strict: false,
          label: $T("连接设备名称"),
          readOnly: !this.editMode,
          minWidth: omegaI18n.locale == "en" ? 240 : 150,
          source: function (query, process) {
            const rowData = this.instance.getSourceDataAtRow(this.row);
            const componentRoomName = rowData.componentRoomName;
            if (!componentRoomName) {
              process([]);
              return;
            }
            const room = roomList.find(i => i.name === componentRoomName);
            if (!room) {
              process([]);
              return;
            }
            const componentType = rowData.componentType;
            let nameList = [];
            if (componentType === $T("变压器")) {
              nameList = optionMap[room.id]?.powertransformer ?? [];
            } else if (componentType === $T("进线")) {
              nameList = optionMap[room.id]?.inDevice ?? [];
            } else if (componentType === $T("馈线")) {
              nameList = optionMap[room.id]?.outDevice ?? [];
            }
            process(nameList);
          },
          validator: function (value, callback) {
            if (!value) {
              callback(true);
              return true;
            }

            const isValid = vm.checkText(value);
            callback(isValid);
            return isValid;
          }
        }
      ];
      if (this.editMode) {
        columns.push({
          className: "htLeft",
          data: "handle",
          type: "text",
          label: $T("操作"),
          renderer: (instance, td, row, col, prop, value, cellProperties) => {
            const text = document.createElement("span");
            text.innerHTML = $T("新增关联");
            text.className = vm.exceedLimit
              ? "noHandsontableHandle"
              : "handsontableHandle";
            text.style = "cursor:pointer;width:100px;"; //鼠标移上去变手型
            // eslint-disable-next-line no-undef
            Handsontable.dom.addEvent(text, "click", function () {
              if (vm.exceedLimit) {
                return;
              }
              const row_index = cellProperties.row;
              vm.addTableItem(row_index);
            });
            // eslint-disable-next-line no-undef
            Handsontable.dom.empty(td);

            td.appendChild(text);
            td.style.textAlign = "left";
          },
          minWidth: omegaI18n.locale == "en" ? 150 : 102,
          readOnly: true
        });
      }

      this.busbarsectionTable.columns = columns;
    },

    checkText(value) {
      const checkNamePattern = common.pattern_name.pattern;
      const checkNameLength = common.check_name.max;
      const isValid =
        checkNamePattern.test(value.trim()) &&
        value.trim().length < checkNameLength;
      return isValid;
    },

    /**
     * 往表格中指定行下增加一行新数据
     * @param index 行下标
     */
    addTableItem(index) {
      const data = this.$refs.busbarsectionTable.getTableData();
      if (this.exceedLimit) {
        this.$message.warning(
          $T("超出单次编辑最大数据量限制，请先保存部分数据")
        );
        return;
      }
      const value = this._.cloneDeep(data[index]);
      value.tree_id = `new_${++this.addIndex}`;
      value.componentType = "";
      value.componentRoomName = "";
      value.componentName = "";
      value.connectIds = [];
      value.deleteStatus = false;
      data.splice(index + 1, 0, value);
      this.modified = true;
      this.busbarsectionTable.data = data;
    },

    async CetButton_preview_statusTrigger_out() {
      if (!this.ifHandsontableFilter()) return;
      this.openPreview();
    },

    CetButton_confirm_statusTrigger_out() {
      if (!this.ifHandsontableFilter()) return;
      this.openPreview(true);
    },

    /**
     * 打开预览弹框
     * @param saveMode  是否以保存模式进行预览
     */
    async openPreview(saveMode = false) {
      if (saveMode) {
        const valid = await this.$refs.busbarsectionTable.validateCells();
        if (!valid) return;
      }
      const tableData = this.$refs.busbarsectionTable.getTableData();
      const tableDataValid = await this.checkTableData();
      if (!tableDataValid) {
        return;
      }

      const params = this.convertTableData(tableData);
      const res = await customApi.topologyManagePreview(params);
      if (res.code !== 0) return;

      const dataInfo = res?.data?.dataInfo ?? [];
      const dataLink = res?.data?.dataLink ?? [];
      this.topologyChartPreview.inputData_in = {
        nodes: dataInfo.map(item => {
          return {
            id: item.name,
            label: item.nodeName,
            nodeLabel: item.nodeLabel
          };
        }),
        edges: dataLink
      };
      this.topologyChartPreview.saveMode = saveMode;
      this.topologyChartPreview.openTrigger_in = Date.now();
    },

    async topologyChartPreview_confirm_out() {
      const res = await this.saveData();
      if (!res) return;
      this.topologyChartPreview.closeTrigger_in = Date.now();
    },

    async saveData() {
      const valid = await this.$refs.busbarsectionTable.validateCells();
      if (!valid) return false;
      const params = {
        deleteData: this.convertTableData(this.deleteList),
        projectId: this.projectId,
        topologyConfigInfoVOList: []
      };

      const tableData = this.$refs.busbarsectionTable.getTableData();
      params.topologyConfigInfoVOList = this.convertTableData(tableData);
      const res = await this.saveFn(params);
      if (!res) return;
      this.editMode = false;
      return true;
    },

    /**
     * 选择元件类型、元件所属房间、设备名称填写任意一项就需要一起填写完整
     */
    async checkTableData() {
      const valid = await this.$refs.busbarsectionTable.validateCells();
      if (!valid) return false;
      const tableData = this.$refs.busbarsectionTable.getTableData();
      const indexs = [];
      tableData.forEach(
        ({ componentType, componentName, componentRoomName }, index) => {
          if (
            !(componentType && componentName && componentRoomName) &&
            !(!componentType && !componentName && !componentRoomName)
          ) {
            indexs.push(index + 1);
          }
        }
      );
      if (indexs.length) {
        this.msgBox.inputData_in = indexs;
        this.msgBox.visibleTrigger_in = Date.now();
        return false;
      }
      return true;
    },

    CetButton_delete_statusTrigger_out() {
      const data = this.$refs.busbarsectionTable.getTableData();
      const deleteList = this._.cloneDeep(data.filter(i => i.deleteStatus));
      if (!this.editMode) {
        // 查看状态下直接进行删除
        this.$confirm($T("是否确认删除?"), $T("提示"), {
          confirmButtonText: $T("确认"),
          cancelButtonText: $T("取消"),
          distinguishCancelAndClose: true,
          type: "warning"
        })
          .then(async () => {
            this.deleteData(deleteList);
          })
          .catch(() => {
            this.$message.info($T("已取消"));
          });
        return;
      } else {
        // 编辑状态下，先将删除的行存起来，最后保存的时候再统一删除
        this.deleteList.push(...deleteList);
        this.$refs.busbarsectionTable.clearDeleteData();
      }
    },

    async deleteData(deleteList) {
      const params = {
        deleteData: this.convertTableData(deleteList),
        projectId: this.projectId,
        topologyConfigInfoVOList: []
      };
      return await this.saveFn(params, true);
    },

    async saveFn(data, deleteFlag) {
      const res = await customApi.topologyManageConfig(data);
      if (res.code !== 0) return;
      this.$message.success($T("操作成功"));
      this.$store.dispatch("importProgress/noticeProgress", {
        vm: this,
        initialProcessInfo: res.data,
        deleteFlag
      });

      return true;
    },

    /**
     * 将表格数据转换为接口入参格式
     * @param list 表格数据
     */
    convertTableData(list) {
      return (
        list?.map(item => {
          let componentType, componentLineFunctionType;
          if (item.componentType === $T("变压器")) {
            componentType = "powertransformer";
            componentLineFunctionType = null;
          } else if (item.componentType === $T("进线")) {
            componentType = "linesegmentwithswitch";
            componentLineFunctionType = 4;
          } else if (item.componentType === $T("馈线")) {
            componentType = "linesegmentwithswitch";
            componentLineFunctionType = 7;
          }
          return {
            connectIds: item.connectIds,
            lineNodeName: item.lineNodeName,
            lineNodeId: item.lineNodeId,
            busBarSectionName: item.busBarSectionName,
            componentName: item.componentName,
            componentRoomName: item.componentRoomName,
            lineFunctionType: item.lineFunctionType,
            roomName: item.roomName,
            roomId: item.roomId,
            componentLineFunctionName: item.componentLineFunctionName,
            componentType,
            componentLineFunctionType
          };
        }) ?? []
      );
    },

    CetButton_association_statusTrigger_out() {
      this.editMode = true;
      this.getTableData();
    },

    async CetButton_cancel_statusTrigger_out() {
      const canLeave = await this.abandonEditing();
      if (!canLeave) return;
      this.editMode = false;
      this.getTableData();
    },

    CetButton_reset_statusTrigger_out() {
      this.busbarsectionTable.clearFilterHandle = Date.now();
    },

    /**
     * 判断表格是否存在筛选
     * @param warningText 存在过滤的提示文本
     */
    ifHandsontableFilter(warningText) {
      const handsontableFilter =
        this.$refs.busbarsectionTable.handsontableFilter;
      if (handsontableFilter) {
        this.$message.warning(warningText || $T("请点击重置全部过滤"));
        return false;
      }
      return true;
    },

    /**
     * 放弃编辑操作前的确认逻辑
     * 如果数据已被修改且处于编辑模式，弹出确认框询问用户是否保存
     * 根据用户的选择（确定、取消或关闭弹窗）返回相应的结果
     *
     * @returns {Promise<boolean>} 返回一个 Promise，解析为布尔值：
     * - `true`：表示用户可以继续执行后续操作（如离开页面或关闭弹窗）。
     * - `false`：表示用户取消了操作，阻止后续逻辑（如留在当前页面）。
     *
     * @example
     * const canLeave = await abandonEditing();
     * if (canLeave) {
     *   // 用户确认保存或取消操作，继续执行后续逻辑
     * } else {
     *   // 用户关闭了弹窗，阻止后续操作
     * }
     */
    async abandonEditing() {
      if (!this.modified || !this.editMode) {
        return true;
      }
      try {
        await this.$confirm($T("数据已修改，是否进行保存？"), $T("提示"), {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          distinguishCancelAndClose: true,
          type: "warning"
        });
        this.CetButton_confirm_statusTrigger_out();
        return false;
      } catch (action) {
        if (action === "cancel") {
          // 用户点击了取消
          return true;
        }
        // 用户关闭了弹窗
        this.$message.info($T("已取消"));
        return false;
      }
    }
  },
  async created() {
    const res = await customApi.manageLimitImportConfig();
    this.limit = res.data ?? 3000;
  }
};
</script>

<style lang="scss" scoped>
.association-img {
  width: 1120px;
}
</style>
