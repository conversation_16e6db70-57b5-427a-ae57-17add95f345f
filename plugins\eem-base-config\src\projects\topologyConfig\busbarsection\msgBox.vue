<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div slot="title">
      <i class="el-icon-warning"></i>
      <span class="titleName ml-J1">
        {{ $T("连接设备未填写完整，不能进行预览和保存！") }}
      </span>
    </div>
    <div class="content mb-J3">
      <TextKeyword
        :text_in="$T('共有{0}行未填写完整设备连接信息', inputData_in?.length)"
        :keys_in="[inputData_in?.length?.toString()]"
        :keywordClass="'text-Sta3'"
        class="mt-J1"
      />
      <div class="p-J2 bg-BG rounded-Ra mt-J1 repeatNodeNames">
        {{ $T("第{0}行", inputData_in?.join("、")) }}
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import TextKeyword from "eem-base/components/textKeyword.vue";
export default {
  props: {
    visibleTrigger_in: Number,
    inputData_in: {
      type: Array,
      default: () => []
    }
  },
  components: {
    TextKeyword
  },
  data() {
    return {
      retract: true,
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "480px",
        top: "10vh",
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("去填写"),
        type: "danger",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    }
  },
  methods: {
    CetButton_confirm_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__body) {
  padding: 0;
}
.el-icon-warning {
  @include font_color(Sta3);
  font-size: 22px;
}
.titleName {
  font-weight: bold;
  @include font_size(H3);
}
.content {
  padding: 0 50px;
  .repeatNodeNames {
    max-height: 600px;
    overflow: auto;
  }
}
</style>
