<template>
  <div
    class="w-full h-full box-border relative"
    ref="topologyChartBox"
    id="electricity-topology-config-g6"
  ></div>
</template>

<script>
import G6 from "@antv/g6";
import omegaTheme from "@omega/theme";
import DomCalc from "eem-base/utils/domCalc.js";
export default {
  name: "topologyChart",
  props: {
    selectNodeId: String,
    inputData_in: Object
  },
  computed: {
    charColor() {
      const currentTheme = omegaTheme.theme;
      let textColor = "",
        selectTextColor = "",
        selectColor = "",
        defaultSelectColor = "";
      if (["dark", "blue"].includes(currentTheme)) {
        textColor = "#E6E8EA";
        selectTextColor = "#0D86FF";
        selectColor = "#0D4691";
        defaultSelectColor = "#263159";
      } else {
        textColor = "#000000";
        selectTextColor = "#29B061";
        selectColor = "#D4EFDF";
        defaultSelectColor = "#F2F2F2";
      }
      return { textColor, selectTextColor, selectColor, defaultSelectColor };
    },
    linColor() {
      const currentTheme = omegaTheme.theme;
      if (["dark", "blue"].includes(currentTheme)) {
        return "#515A7A";
      } else {
        return "#DCDFE2";
      }
    }
  },
  data() {
    return {
      graph: null
    };
  },
  watch: {
    inputData_in: {
      handler: async function () {
        await this.$nextTick();
        this.init();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    init() {
      const data = this.getChartData();
      if (this._.isEmpty(this.graph)) this.initGraphConf();
      this.graph.data(data);
      this.graph.render();
      this.graph.zoomTo(1);
      this.graph.fitCenter();
    },

    getChartData() {
      if (this._.isEmpty(this.inputData_in)) {
        return { nodes: [], edges: [] };
      }
      return this.inputData_in;
    },

    initGraphConf() {
      if (this.graph) return;
      const width = this.$refs.topologyChartBox.scrollWidth;
      const height = this.$refs.topologyChartBox.scrollHeight;
      this.graph = new G6.Graph({
        container: "electricity-topology-config-g6",
        width: width,
        height: height,
        modes: {
          default: [
            "drag-canvas",
            "zoom-canvas",
            {
              type: "tooltip"
            }
          ]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          nodesep: 25,
          ranksep: 60,
          controlPoints: true // 是否保留布局连线的控制点
        },
        defaultNode: {
          type: "electricityTopologyConfigCustomCircle"
        },
        defaultEdge: {
          size: 2,
          type: "line",
          color: this.linColor
        }
      });
    },

    createCustomNode() {
      const vm = this;
      G6.registerNode("electricityTopologyConfigCustomCircle", {
        drawShape(cfg, group) {
          group.addShape("rect", {
            attrs: {
              x: -16,
              y: -27,
              width: 32,
              height: 32,
              stroke:
                vm.selectNodeId === cfg.id
                  ? vm.charColor.selectColor
                  : vm.charColor.defaultSelectColor,
              fill:
                vm.selectNodeId === cfg.id
                  ? vm.charColor.selectColor
                  : vm.charColor.defaultSelectColor,
              radius: 4
            }
          });
          group.addShape("image", {
            attrs: {
              x: -16,
              y: -27,
              width: 32,
              height: 32,
              img: vm.queyNodeImage(cfg)
            },
            draggable: true
          });

          group.addShape("text", {
            attrs: {
              textAlign: "center",
              x: 0,
              y: 28,
              fontSize: 12,
              fill:
                vm.selectNodeId === cfg.id
                  ? vm.charColor.selectTextColor
                  : vm.charColor.textColor,
              text: vm.fittingString(cfg.label, 190, 12)
            }
          });
          return group;
        },
        getAnchorPoints: function getAnchorPoints() {
          return [
            [0.5, 0.3],
            [0.5, 0.3]
          ];
        }
      });
    },

    queyNodeImage(node) {
      let currentTheme = "light";
      if (["dark", "blue"].includes(omegaTheme.theme)) {
        currentTheme = "dark";
      }
      let nodeImage;
      try {
        nodeImage = require(`./assets/device/${currentTheme}/${node.nodeLabel}.png`);
      } catch (error) {
        nodeImage = require(`./assets/device/${currentTheme}/general.png`);
      }
      return nodeImage;
    },

    fittingString(str, maxWidth, fontSize) {
      if (!str) return;
      const ellipsis = "...";
      const ellipsisLength = DomCalc.calcTextWidthPad(ellipsis, 0, fontSize);
      let currentWidth = 0;
      let res = str;
      const pattern = new RegExp("[\u4E00-\u9FA5]+"); // distinguish the Chinese charactors and letters
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth - ellipsisLength) return;
        if (pattern.test(letter)) {
          // Chinese charactors
          currentWidth += fontSize;
        } else {
          // get the width of single letter according to the fontSize
          currentWidth += DomCalc.calcTextWidthPad(letter, 0, fontSize);
        }
        if (currentWidth > maxWidth - ellipsisLength) {
          res = `${str.substr(0, i)}${ellipsis}`;
        }
      });
      return res;
    }
  },
  mounted() {
    this.createCustomNode();
  }
};
</script>

<style lang="scss" scoped>
#electricity-topology-config-g6 :deep(.g6-tooltip) {
  white-space: nowrap;

  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  background-color: rgba(50, 50, 50, 0.7);
  font-size: 14px;
  border-radius: 4px;
  color: rgb(255, 255, 255);
  padding: 10px;
  pointer-events: none;
  transform: translate(10px, 10px);
}
</style>
