<template>
  <CetDialog v-bind="CetDialog_1" class="cet-dialog">
    <div class="wrap bg-BG1 p-J2 box-border rounded-Ra1">
      <el-empty
        class="w-full h-full"
        v-if="topologyIsEmpty"
        :image="emptyImage"
        :description="$T('暂无拓扑图')"
        :image-size="432"
      ></el-empty>
      <TopologyChart v-else :inputData_in="inputData_in" />
    </div>
    <template v-slot:footer>
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
        :title="saveMode ? $T('取消') : $T('关闭')"
      ></CetButton>
      <CetButton
        v-if="saveMode"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </template>
  </CetDialog>
</template>

<script>
import TopologyChart from "./topologyChart.vue";
import emptyImage from "../assets/u1104.png";
export default {
  name: "topologyChartPreview",
  components: {
    TopologyChart
  },
  props: {
    openTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    saveMode: Boolean
  },
  computed: {
    topologyIsEmpty() {
      const nodes = this.inputData_in?.nodes ?? [];
      const edges = this.inputData_in?.edges ?? [];
      return !nodes.length || !edges.length;
    }
  },
  data() {
    return {
      emptyImage,
      CetDialog_1: {
        title: $T("效果预览"),
        openTrigger_in: Date.now(),
        closeTrigger_in: Date.now(),
        width: "1200px",
        top: "36px",
        showClose: true
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      }
    };
  },
  watch: {
    openTrigger_in() {
      this.CetDialog_1.openTrigger_in = Date.now();
    },
    closeTrigger_in() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_confirm_statusTrigger_out() {
      this.$emit("confirm_out");
    }
  }
};
</script>

<style lang="scss" scoped>
.cet-dialog {
  :deep() {
    .el-dialog__body {
      @include background_color(BG);
      @include padding(J1);
    }
  }
}
.wrap {
  height: 672px;
}
</style>
