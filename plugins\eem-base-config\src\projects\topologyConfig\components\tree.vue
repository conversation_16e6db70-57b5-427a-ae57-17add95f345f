<template>
  <div class="w-full h-full flex flex-col">
    <CustomElSelect
      v-model="ElSelect_1.value"
      v-bind="ElSelect_1"
      v-on="ElSelect_1.event"
      class="w-full mb-J2"
      :prefix_in="$T('关联状态')"
    >
      <ElOption
        v-for="item in ElOption_1.options_in"
        :key="item[ElOption_1.key]"
        :label="item[ElOption_1.label]"
        :value="item[ElOption_1.value]"
        :disabled="item[ElOption_1.disabled]"
      ></ElOption>
    </CustomElSelect>
    <ElInput
      class="mb-J2"
      v-model.trim="ElInput_1.value"
      v-bind="ElInput_1"
      v-on="ElInput_1.event"
    ></ElInput>
    <div class="flex-auto" ref="tree">
      <CetVirtualTree
        class="cetVirtualTree"
        v-if="showTree"
        v-bind="CetVirtualTree_1"
        v-on="CetVirtualTree_1.event"
      ></CetVirtualTree>
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import { CustomElSelect } from "eem-base/components";
export default {
  name: "busbarsectionTree",
  components: {
    CustomElSelect
  },
  props: {
    apiName: String,
    updataTreeData: Number
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      showTree: true,
      ElSelect_1: {
        value: 1,
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            value: 0,
            label: $T("全部")
          },
          {
            value: 1,
            label: $T("未配置")
          },
          {
            value: 2,
            label: $T("已配置")
          }
        ],
        key: "value",
        value: "value",
        label: "label",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入关键字搜索"),
        "suffix-icon": "el-icon-search",
        event: {
          change: this.ElInput_1_change_out
        }
      },
      CetVirtualTree_1_TreeV2: null,
      CetVirtualTree_1: {
        filterNodes_in: [],
        inputData_in: [],
        attribute: {
          data: [],
          props: {
            value: "tree_id",
            label: "name",
            children: "children"
          },
          emptyText: $T("暂无数据"),
          nodeKey: "tree_id",
          defaultCheckedKeys: [],
          defaultExpandedKeys: [],
          showCheckbox: true,
          highlightCurrent: true,
          height: 420,
          filterMethod: (value, data) => {
            return data.name.includes(value);
          }
        },
        event: {
          onCreate: this.CetVirtualTree_1_onCreate,
          onCheck: this.CetVirtualTree_1_onCheck
        }
      }
    };
  },
  watch: {
    updataTreeData() {
      this.queryTreeData();
    }
  },
  methods: {
    init() {
      this.ElSelect_1.value = 1;
      this.ElInput_1.value = "";
      this.queryTreeData();
    },

    ElSelect_1_change_out() {
      this.queryTreeData();
    },

    async queryTreeData(defaultCheckedKeys) {
      const params = {
        projectId: this.projectId,
        nodeConnectStatus: this.ElSelect_1.value
      };
      const res = await customApi[this.apiName](params);
      const treeData = res?.data || [];

      let checkedKeys = [];
      if (defaultCheckedKeys?.length) {
        checkedKeys = defaultCheckedKeys;
      } else {
        const selectNode = this.findSelectNode(treeData);
        checkedKeys = selectNode ? [selectNode.tree_id] : [];
      }

      await this.$nextTick();
      this.CetVirtualTree_1_TreeV2?.setData(treeData);

      this.CetVirtualTree_1_TreeV2?.setCheckedKeys(checkedKeys);
      await this.$nextTick();
      const checkedNodes = this.CetVirtualTree_1_TreeV2?.getCheckedNodes();
      this.CetVirtualTree_1_onCheck(null, { checkedNodes });
      if (!checkedNodes?.length) {
        this.$emit("initTableData");
      }
    },

    /**
     * 进入当前页面默认选择的节点第三层第一个节点没有就第一个房间节点，最后选项目节点
     * @param treeData
     */
    findSelectNode(treeData) {
      if (!treeData?.length) return;
      const rooms = treeData[0].children || [];
      const room = rooms.find(i => i.children?.length);
      return room?.children[0] ?? (rooms?.[0] || treeData[0]);
    },

    CetVirtualTree_1_onCreate(event) {
      this.CetVirtualTree_1_TreeV2 = event;
    },

    CetVirtualTree_1_onCheck(data, { checkedNodes }) {
      this.$emit("checkedNodesOut", checkedNodes, this.ElSelect_1.value);
    },

    ElInput_1_change_out(val) {
      this.CetVirtualTree_1_TreeV2.filter(val);
    },

    async setTreeHeight() {
      this.CetVirtualTree_1.attribute.height = this.$refs.tree.clientHeight;
      this.showTree = false;
      await this.$nextTick();
      this.showTree = true;
    },

    /**
     * 回退节点状态选择和节点勾选
     * @param nodes 节点
     * @param type 状态
     */
    async setCheckedAndTreeType(nodes, type) {
      const keys = nodes?.map(i => i.tree_id) ?? [];
      const status =
        this.ElOption_1.options_in.find(i => i.value === type)?.value ?? 1;
      this.ElSelect_1.value = status;
      await this.$nextTick();
      this.CetVirtualTree_1_TreeV2?.setCheckedKeys(keys);
    },

    /**
     * 更新节点树 并设置默认勾选节点
     * @param nodes
     */
    updateTreeAndSetCheckedNodes(nodes) {
      const keys = nodes?.map(i => i.tree_id) ?? [];
      this.queryTreeData(keys);
    }
  },
  mounted() {
    this.setTreeHeight();
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.cetVirtualTree {
  :deep(.el-vl__window.el-tree-virtual-list::-webkit-scrollbar-track) {
    background-color: transparent;
  }
  :deep(.el-virtual-scrollbar) {
    display: none;
  }
  :deep(.el-tree-node__expand-icon) {
    min-width: 12px;
  }
}
</style>
