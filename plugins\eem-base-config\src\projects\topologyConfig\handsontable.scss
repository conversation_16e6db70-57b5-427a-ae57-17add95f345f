:deep() .handsontable th {
  text-align: left;
}
:deep(.handsontableHandle) {
  @include font_color(ZS);
}
:deep(.noHandsontableHandle) {
  cursor: not-allowed;
  @include font_color(T6);
}
.table-border {
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
}
:deep() tr td:first-child {
  border-left: none;
}
:deep() .ht_master.handsontable tr td:last-child {
  border-right: none;
}
:deep() tr th:first-child {
  border-left: none;
}

::v-deep .ht_clone_top.handsontable tr th:last-child {
  border-right: none;
}
:deep() table thead tr th {
  height: 30px;
  line-height: 30px;
  @include background_color(BG);
  @include font_color(T1);
  @include border_color(B1);
}
:deep() table tbody tr th {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
:deep() table tbody tr td {
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
:deep() .handsontableHandle {
  @include font_color(Sta3);
}
:deep() .required {
  @include font_color(Sta3);
}
:deep() .autocompleteEditor.handsontable {
  padding-right: 0;
}

:deep(table thead tr th) {
  height: 30px;
  line-height: 30px;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
:deep(.handsontable th) {
  text-align: left;
}
:deep(table tbody tr th) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

:deep(table tbody tr td) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

:deep(table thead tr th .changeType) {
  margin-top: 8px;
}
:deep(table thead tr th .colHeader) {
  font-weight: 700;
}
:deep(.handsontable .changeType) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
:deep(.handsontable .changeType:hover) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
:deep(.handsontableHandle) {
  @include font_color(ZS);
}

:deep(.ht_clone_top_left_corner.handsontable .htCore tr > th:nth-child(2)) {
  .changeType {
    display: none;
  }
  span.colHeader.columnSorting:before {
    display: none;
  }
}

.boxShadow :deep() {
  .ht_clone_left.handsontable .wtHolder .wtHider .wtSpreader .htCore tbody {
    @include box_shadow(S3);
  }
}
:deep() .handsontable tr {
  @include background_color(BG1);
}
:deep() .columnSorting.sortAction:hover {
  text-decoration: initial;
}

:deep(.ht_clone_left.handsontable) {
  padding-right: 10px;
}
:deep() {
  .handsontableInputHolder .ht_master.handsontable .htCore tbody tr td {
    height: 22px;
    line-height: 22px;
  }
}
:deep() table tbody tr td {
  &.cell-bg1 {
    background-color: rgba(#24ed8d, 0.1);
  }
  &.cell-bg2-opacity {
    background-color: rgba(#1e2559, 0.1);
  }
  &.cell-bg2 {
    background-color: #182559;
  }
  &.cell-bg3 {
    background-color: rgba(#ffc531, 0.1);
  }
}
