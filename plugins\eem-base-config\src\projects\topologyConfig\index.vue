<template>
  <el-tabs
    v-model="activeName"
    class="eltabs bg-BG1 h-full w-full rounded-Ra flex flex-col"
    :before-leave="tabsBeforeChange"
  >
    <el-tab-pane :label="$T('查询拓扑')" class="h-full" name="topologyQuery">
      <TopologyQuery v-if="activeName === 'topologyQuery'" />
    </el-tab-pane>
    <el-tab-pane :label="$T('拓扑配置')" class="h-full" name="busbarsection">
      <Busbarsection
        v-if="activeName === 'busbarsection'"
        ref="busbarsection"
      />
    </el-tab-pane>
    <el-tab-pane :label="$T('母联配置')" class="h-full" name="busbarconnector">
      <Busbarconnector
        v-if="activeName === 'busbarconnector'"
        ref="busbarconnector"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import TopologyQuery from "./topologyQuery/index.vue";
import Busbarsection from "./busbarsection/index.vue";
import Busbarconnector from "./busbarconnector/index.vue";
export default {
  name: "topologyConfig",
  async beforeRouteLeave(to, from, next) {
    if (!["busbarsection", "busbarconnector"].includes(this.activeName)) {
      next();
      return;
    }

    const canLeave = await this.$refs[this.activeName].abandonEditing();
    if (canLeave) {
      next();
    }
  },
  components: {
    TopologyQuery,
    Busbarsection,
    Busbarconnector
  },
  data() {
    return {
      activeName: "topologyQuery"
    };
  },
  watch: {},
  methods: {
    tabsBeforeChange(activeName, oldActiveName) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        if (
          (activeName !== "busbarsection" &&
            oldActiveName === "busbarsection") ||
          (activeName !== "busbarconnector" &&
            oldActiveName === "busbarconnector")
        ) {
          const canLeave = await this.$refs[oldActiveName].abandonEditing();
          if (!canLeave) {
            reject();
            return;
          }
        }
        resolve();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.eltabs {
  :deep() {
    .el-tabs__header {
      margin-bottom: 0;
      line-height: 46px;
    }
    .el-tabs__nav-scroll {
      padding-left: var(--J4);
    }
    .el-tabs__content {
      flex: 1;
      min-height: 0;
    }
  }
}
</style>
