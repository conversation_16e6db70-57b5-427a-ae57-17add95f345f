<template>
  <div class="w-full h-full flex flex-row">
    <div
      class="tree-box w-[315px] p-[24px] border-solid border-t-0 border-l-0 border-b-0 border-r-[2px] border-B1 flex flex-col box-border mr-J2"
    >
      <ElInput
        class="mb-J2"
        v-model.trim="ElInput_1.value"
        v-bind="ElInput_1"
        v-on="ElInput_1.event"
      ></ElInput>
      <div class="flex-auto" ref="tree">
        <CetVirtualTree
          class="cetVirtualTree"
          v-if="showTree"
          v-bind="CetVirtualTree_1"
          v-on="CetVirtualTree_1.event"
        ></CetVirtualTree>
      </div>
    </div>
    <div class="content-box flex-auto p-[24px] box-border">
      <el-empty
        class="w-full h-full"
        v-if="topologyIsEmpty"
        :image="emptyImage"
        :description="$T('暂无拓扑图')"
        :image-size="432"
      ></el-empty>
      <TopologyChart v-else v-bind="topologyChart" />
    </div>
  </div>
</template>

<script>
import customApi from "@/api/custom";
import emptyImage from "../assets/u1104.png";
import TopologyChart from "../components/topologyChart.vue";
export default {
  name: "topologyQuery",
  components: {
    TopologyChart
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    topologyIsEmpty() {
      const nodes = this.topologyChart.inputData_in?.nodes ?? [];
      const edges = this.topologyChart.inputData_in?.edges ?? [];
      return !this.selectNode?.id || !nodes.length || !edges.length;
    }
  },
  data() {
    return {
      showTree: true,
      emptyImage,
      ElInput_1: {
        value: "",
        placeholder: $T("请输入关键字搜索"),
        "suffix-icon": "el-icon-search",
        event: {
          change: this.ElInput_1_change_out
        }
      },
      selectNode: null,
      CetVirtualTree_1_TreeV2: null,
      CetVirtualTree_1: {
        filterNodes_in: [],
        inputData_in: [],
        attribute: {
          data: [],
          props: {
            value: "tree_id",
            label: "name",
            children: "children"
          },
          emptyText: $T("暂无数据"),
          nodeKey: "tree_id",
          defaultCheckedKeys: [],
          defaultExpandedKeys: [],
          showCheckbox: false,
          highlightCurrent: true,
          height: 420,
          filterMethod: (value, data) => {
            return data.name.includes(value);
          }
        },
        event: {
          onCreate: this.CetVirtualTree_1_onCreate,
          onNodeClick: this.CetVirtualTree_1_onNodeClick
        }
      },
      topologyChart: {
        inputData_in: null,
        selectNodeId: null
      }
    };
  },
  methods: {
    init() {
      this.queryTreeData();
    },

    async queryTreeData() {
      const params = {
        projectId: this.projectId,
        energyType: 2
      };
      const res = await customApi.topologyManageTree(params);
      const treeData = res?.data || [];
      const selectNode = treeData?.[0]?.children?.[0];
      await this.$nextTick();
      this.CetVirtualTree_1_TreeV2?.setData(treeData);
      if (!selectNode) {
        return;
      }

      this.CetVirtualTree_1_TreeV2?.setCurrentKey(selectNode.tree_id);
      await this.$nextTick();
      this.CetVirtualTree_1_TreeV2?.setExpandedKeys([treeData[0].tree_id]);
      this.CetVirtualTree_1_onNodeClick(selectNode);
    },

    CetVirtualTree_1_onNodeClick(val) {
      this.selectNode = val;
      this.queryTopologyData(val);
    },

    CetVirtualTree_1_onCreate(event) {
      this.CetVirtualTree_1_TreeV2 = event;
    },

    ElInput_1_change_out(val) {
      this.CetVirtualTree_1_TreeV2.filter(val);
    },

    async queryTopologyData(val) {
      const params = {
        projectId: this.projectId,
        energyType: 2,
        node: {
          id: val.id,
          modelLabel: val.modelLabel
        }
      };
      const res = await customApi.topologyManageInfo(params);

      const dataInfo = res?.data?.dataInfo ?? [];
      const dataLink = res?.data?.dataLink ?? [];
      this.topologyChart.selectNodeId = `${val.modelLabel}$${val.id}`;
      this.topologyChart.inputData_in = {
        nodes: dataInfo.map(item => {
          return {
            id: item.name,
            label: item.nodeName,
            nodeLabel: item.nodeLabel
          };
        }),
        edges: dataLink
      };
    },

    async setTreeHeight() {
      await this.$nextTick();
      this.CetVirtualTree_1.attribute.height = this.$refs.tree.clientHeight;
      this.showTree = false;
      await this.$nextTick();
      this.showTree = true;
    }
  },
  async mounted() {
    await this.setTreeHeight();
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.cetVirtualTree {
  :deep(.el-vl__window.el-tree-virtual-list::-webkit-scrollbar-track) {
    background-color: transparent;
  }
  :deep(.el-virtual-scrollbar) {
    display: none;
  }
  :deep(.el-tree-node__expand-icon) {
    min-width: 12px;
  }
}
</style>
