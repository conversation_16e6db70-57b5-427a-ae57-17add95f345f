/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "/cloudProjectConfig",
        component: () => import("@/projects/cloudProjectConfig/index.vue")
      },
      {
        path: "/networkCloudProjectConfig",
        component: () =>
          import("@/projects/cloudProjectConfig/indexNetwork.vue")
      },
      {
        path: "/projectBatchConfig",
        component: () => import("@/projects/projectBatchConfig/index.vue")
      },
      {
        path: "/topologyConfig",
        component: () => import("@/projects/topologyConfig/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
