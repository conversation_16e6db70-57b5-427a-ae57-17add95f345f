export default {
  namespaced: true,
  state() {
    return {
      // 处于正在导入中的任务
      processList: [
        // {
        //   type: 1, // 枚举taskoperationtype
        //   logType: 719,
        //   processInfo: {
        //     name: "某某文件",
        //     ratio: 0,
        //     status: 1,//1:未开始；2:导入中；3:成功
        //     filePath: ""
        //   }
        // }
      ],
      // `当前正在${},禁止操作`;枚举taskoperationtype的解析，提供给用户看的要前端再定义一遍，因为枚举的不准张壮说的
      taskoperationtype: [
        {
          id: 1,
          name: $T("生成管网配置")
        },
        {
          id: 2,
          name: $T("生成层级配置")
        },
        {
          id: 3,
          name: $T("导入实际产量数据")
        },
        {
          id: 4,
          name: $T("导入计划产量数据")
        },
        {
          id: 5,
          name: $T("生成层级配置")
        },
        {
          id: 7,
          name: $T("能耗同环比数据导出")
        },
        {
          id: 8,
          name: $T("能耗分时数据导出")
        },
        {
          id: 9,
          name: $T("能耗详情数据导出")
        },
        {
          id: 10,
          name: $T("配置维度标签赋值")
        },
        {
          id: 11,
          name: $T("导出报表")
        },
        {
          id: 12,
          name: $T("告警收敛事件导出")
        },
        {
          id: 14,
          name: $T("批量配置-刷新节点树")
        },
        {
          id: 15,
          name: $T("拓扑配置")
        }
      ],
      // 删除的logType需要特殊处理
      deleteLogTypes: [722],
      // 导出的通知类型
      exportLogTypes: [728, 729, 730, 733, 734],
      connectionStatus: false
    };
  },
  actions: {},
  mutations: {
    setConnectionStatus(state, val) {
      state.connectionStatus = val;
    }
  },
  getters: {
    importing(state) {
      return type => {
        if (!state.connectionStatus) return false;
        const processList = state.processList;
        const item = processList.find(i => i.type === type);
        const status = item?.processInfo?.status;
        return status === 2;
      };
    },
    importingStr(state) {
      return id => {
        if (!state.connectionStatus) return $T(`websokcet未连接,禁止操作`);
        const taskoperationtype = state.taskoperationtype;
        const item = taskoperationtype.find(i => i.id === id);
        if (!item) return;
        return $T(`当前正在{0},禁止操作`, item.name);
      };
    }
  }
};
