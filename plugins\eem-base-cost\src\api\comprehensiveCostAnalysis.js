import fetch from "eem-base/utils/fetch";
const version = "v1";

// 综合成本趋势
export function queryComprehensiveTrend(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costTotalValue`,
    method: "POST",
    data
  });
}

// 综合成本概览
export function queryComprehensiveCost(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costByNode`,
    method: "POST",
    data
  });
}

// 分类成本占比
export function queryEnergyCostValue(data) {
  return fetch({
    url: `/eem-service/${version}/costanalysis/costValue`,
    method: "POST",
    data
  });
}

// 能源成本核算
export function queryEnergycostcheck(data) {
  return fetch({
    url: `/eem-service/${version}/costcaculating/energycostcheckbasic`,
    method: "POST",
    data
  });
}
