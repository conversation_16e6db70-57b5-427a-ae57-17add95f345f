//能管基础
import commonApi from "eem-base/api";
import * as energy from "./energy";
import * as rateadministration from "./rateadministration";
import * as accountingscheme from "./accountingscheme";
import * as newCostAnalysis from "./newCostAnalysis";
import * as tree from "./tree";
import * as electricityCostAnalysis from "./electricityCostAnalysis";
import * as unitCostAnalysis from "./unitCostAnalysis";
import * as comprehensiveCostAnalysis from "./comprehensiveCostAnalysis";

export default {
  ...commonApi,
  ...energy,
  ...rateadministration,
  ...accountingscheme,
  ...newCostAnalysis,
  ...tree,
  ...electricityCostAnalysis,
  ...unitCostAnalysis,
  ...comprehensiveCostAnalysis
};
