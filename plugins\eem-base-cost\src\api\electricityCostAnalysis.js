import fetch from "eem-base/utils/fetch";
const version = "v1";

// 用电分析过滤后的节点树
export function queryElectricityTree(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/nodeTree`,
    method: "POST",
    data
  });
}

// 分时方案列表
export function queryTimeShareList(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/timeShareList`,
    method: "POST",
    data
  });
}

// 用电成本和平均电价趋势
export function queryElectricityCostValueTrend(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/trend/extend`,
    method: "POST",
    data
  });
}

// 平均电价分析
export function queryAverageElectricityPrice(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/averageElectricityPrice`,
    method: "POST",
    data
  });
}

// 分时用电成本
export function queryTsObjectCost(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/tsObjectCost`,
    method: "POST",
    data
  });
}
