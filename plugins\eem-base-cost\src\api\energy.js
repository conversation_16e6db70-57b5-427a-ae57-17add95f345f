import fetch from "eem-base/utils/fetch";

//获取项目能源类型
export function getProjectEnergy(projectId) {
  return fetch({
    url: `/eem-service/v1/project/projectEnergy`,
    method: "GET",
    params: {
      projectId: projectId
    }
  });
}

// 返回需要去掉的能源类型列表
export function organizationConfigFilterEnergyType() {
  return fetch({
    url: `/eem-service/v1/organization/config/filter/energyType`,
    method: "POST"
  });
}
