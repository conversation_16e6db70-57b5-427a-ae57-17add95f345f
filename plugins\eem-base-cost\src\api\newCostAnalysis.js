import fetch from "eem-base/utils/fetch";
const version = "v1";

// 同环比趋势查询
export function realtimeCostRadioTrend(data) {
  return fetch({
    url: `/eem-service/${version}/realtime-cost/cost-radio-trend`,
    method: "POST",
    data
  });
}

// 构成项趋势分析
export function realtimeCostCheckItemTrend(data) {
  return fetch({
    url: `/eem-service/${version}/realtime-cost/check-item/cost/trend`,
    method: "POST",
    data
  });
}

// 综合成本概览同环比
export function realtimeCostRadio(data) {
  return fetch({
    url: `/eem-service/${version}/realtime-cost/cost-radio`,
    method: "POST",
    data
  });
}

// 构成项占比
export function realtimeCostCheckItemCost(data) {
  return fetch({
    url: `/eem-service/${version}/realtime-cost/check-item/cost`,
    method: "POST",
    data
  });
}

// 分类成本占比
export function realtimeCostGroupByEnergyType(data) {
  return fetch({
    url: `/eem-service/${version}/realtime-cost/cost-group-by-energy-type`,
    method: "POST",
    data
  });
}

// 分类成本占比
export function realtimeCostGroupByNode(data) {
  return fetch({
    url: `/eem-service/${version}/realtime-cost/cost-group-by-node`,
    method: "POST",
    data
  });
}

// 节点树
export function electricityCostValueNodeTreeAll(data) {
  return fetch({
    url: `/eem-service/${version}/electricityCostValue/nodeTree/all`,
    method: "POST",
    data
  });
}
