{"一": "One", "三": "Three", "上年同月成本": "Last Year's Cost", "上年成本": "Last Year's Cost", "上月成本": "Last Month's Cost", "上限比例": "Upper Limit Ratio", "不保存": "Do not save", "二": "Two", "产品类型": "Product Type", "今年": "This Year", "保存为图片": "Save as Picture", "偏差费用=（实际最大需量-计划需量*上限比例）*需量电价*惩罚系数": "Deviation Cost = (Actual Max Demand - Plan Demand * Upper Limit Ratio) * Demand Tariff * Penalty Factor", "元": "CNY", "元/kVA": "CNY/kVA", "元/kW": "CNY/kW", "关联对象": "Associated Object", "关联节点": "Associated Node", "关联节点状态": "Associated Node Status", "分摊": "Share", "分时方案": "Time-of-Use Scheme", "分时用电成本": "Time-of-Use Electricity Cost", "分时费率": "Time-of-Use Rate", "分析类型": "Analysis Type", "分类成本占比": "Energy Cost Proportions by Type", "力率": "Power Factor", "力率和调整电费不能为空": "Power factor adjustment rate and adjustment of electricity charges cannot be empty", "力率有重复": "Duplication power factor adjustment rate", "力调费率": "Power Factor Adjustment Rate", "力调费率考核标准": "Power Factor Adjustment Rate Evaluation Criteria", "区域成本TOP5": "Top 5 Regional Costs", "区域成本占比": "Regional Proportion Energy Cost", "单一费率": "Single Rate", "单位": "Unit", "单位成本": "Unit Cost", "单位成本占比": "Unit Cost Proportion", "单位成本概览": "Unit Cost Overview", "单位成本趋势": "Unit Cost Trend", "占比": "Proportion", "合计": "Total", "同比": "YoY", "同环比分析": "Comparative Analysis", "固定管理层级": "Fixed Management Hierarchy", "天数": "Days", "实际成本": "Actual Cost", "实际最大需量<计划需量*上限比例时，是否计算负偏差，若不计算负偏差费用为0": "When actual max demand < plan demand * upper limit ratio, whether to calculate negative deviation. If not calculated, the cost is 0.", "容量计费": "Capacity Billing", "容量费率": "Capacity Rate", "导入成功": "Import Successfully", "导入数据": "Import Data", "导出": "Export", "峰": "High Period", "已关联": "Associated", "已过期": "Expired", "已选节点预览": "Preview Selected Nodes", "已选节点：{0} 个": "Selected Nodes:{0}", "平均电价": "Avg. Elec. Price", "平均电价({0})": "Avg. Elec. Price({0})", "平均电价分析": "Avg. Elec. Price Analysis", "平均电价最大值": "Max Avg. Elec. Price", "平均电价最小值": "Min Avg. Elec. Price", "序号": "No.", "当前状态": "Current Status", "当前生效费率": "Current Effective Rate", "当前节点未关联成本核算方案！": "The current node is not associated with cost accounting scheme", "当年成本": "Cur. Year Cost", "当月成本": "Cur. Month Cost", "录入费率": "Entry Rates", "总用能成本": "Total Energy Consumption Cost", "惩罚系数": "Penalty Factor", "成本({0})": "Cost({0})", "成本值": "Cost Value", "成本构成": "Cost Components", "成本核算": "Cost Accounting", "成本计算项": "Costing Items", "成本趋势分析": "Cost Trend Analysis", "成本项名称": "Cost Item Name", "成本项名称重复": "Cost Item Name Duplicate", "损耗": "Loss", "新增方案": "New Scheme", "新增费率方案": "New Rate Plan", "新建费率方案": "New Rate Plan", "方案名称": "Scheme Name", "方案详情": "Scheme Details", "日峰平谷电度电费之和/日峰平谷总电量": "Daily Total Time-of-Use Electricity Fees", "时段": "Period", "时段区间": "Time Interval", "时段名称": "Period Name", "时段方案": "Time Period Scheme", "时段费率": "Period Rate", "时间": "Time", "是否要保存当前维度关联节点": "Do you want to save the current dimension associated nodes", "月份": "Month", "月峰平谷电度电费之和/月峰平谷总电量": "Monthly Total Time-of-Use Electricity Fees", "未关联": "Unassociated", "未生效": "Inactive", "构成项分析": "Component Analysis", "构成项占比": "Composition Item Proportion", "构成项趋势": "Composition Item Trend", "查看": "View", "核算周期": "Accounting Cycle", "核算方案名称": "Accounting Scheme Name", "核算统计": "Accounting Statistics", "正数：在电费总额上增收": "Positive: increase in total electricity bill", "没有该节点权限": "No permission for this node", "没有该节点权限，该节点没有关联当前方案": "No permission for this node, the node is not associated with the current plan", "添加成本构成": "Adding Cost Components", "环比": "MoM", "生效中": "Effective", "生效时间": "Effective Date", "用电成本和平均电价趋势": "Trend of Electricity Cost and Avg. Elec. Price", "用能量": "Energy Usage", "电": "Electricity", "确定要删除所选项吗？": "Are you sure you want to delete the selected item?", "第": "No.", "第一阶梯": "First Step", "第一阶梯用量": "First Tiers Usage Calculation", "第三阶梯": "Third Step", "第三阶梯用量": "Third Tiers Usage Calculation", "第二阶梯": "Second Step", "第二阶梯用量": "Second Tiers Usage Calculation", "第四阶梯": "Fourth Step", "综合成本": "Comprehensive Cost", "综合成本概览": "Comprehensive Cost Overview", "综合成本趋势": "Comprehensive Cost Trend", "编辑成本构成": "Editing Cost Structure", "编辑方案": "Edit Scheme", "编辑费率方案": "Edit Rate Plan", "能源成本核算": "Energy Cost Accounting", "能源类型": "Energy Type", "能耗": "Energy Consumption", "至少选择一项计算项": "Select at least one calculation item", "节点树名称": "Tree Name", "节点树类型": "Node Tree Type", "计算负偏差": "Calculate Negative Deviation", "请先保存费率": "Please save the rates first", "请先保存费率表": "Please save the rate sheet first", "请填写正确阶梯": "Please enter correct step", "请输入关键字搜索": "Please enter", "请输入容量费率值": "Please enter capacity rate value", "请输入成本项名称": "Please enter cost item name", "请输入方案名称": "Please enter scheme name", "请输入费率值": "Please enter rate value", "请输入费率方案以检索": "Please enter rate plan to retrieve", "请输入需量费率值": "Please enter demand rate value", "请选择力调费率": "Please select power factor adjustment rate", "请选择生效时间": "Please select effective time", "请选择能源类型": "Please select energy type", "请选择费率方案": "Please select rate scheme", "请选择费率类型": "Please select rate type", "请选择费用类型": "Please select fee type", "调整历史": "Adjustment History", "调整电费": "Adjust Electricity Bill", "负数：在电费总额上减收": "Negative: decrease in total electricity bill", "费率": "Rate", "费率值": "Rate Value", "费率方案": "Rate Plan", "费率方案详情": "Rate Plan Details", "费率标准（元/{0}）": "Rate Rates(CNY/{0})", "费率类型": "Rate Type", "费率表": "Rate Schedule", "费率表详情": "Rate Schedule Details", "费率调整": "Rate Adjustment", "费率（元/kVA）": "Rate(CNY/kVA)", "费率（元/kW）": "Rate(CNY/kW)", "费率（元/{0}）": "Rate(CNY/{0})", "费用类型": "Fee Type", "输入关键字搜索": "Please enter", "选择日期": "Select Date", "选择维度": "Select Dimension", "选择计算项": "Select Calculation Item", "阶梯": "Tiers", "阶梯费率": "Tiers Rate", "需量计费": "Demand Billing", "默认选中子节点": "Default Select Child Node", "（14：00~22：00)": "(14:00~22:00)", "（{0}）": "({0})"}