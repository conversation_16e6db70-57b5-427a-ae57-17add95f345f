<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <CetForm
      :data.sync="CetForm_pagedialog.data"
      v-bind="CetForm_pagedialog"
      v-on="CetForm_pagedialog.event"
    >
      <div class="h-full">
        <div class="mb-J3">
          <el-row :gutter="16">
            <el-col :span="8">
              <customElSelect
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
                :prefix_in="$T('能源类型')"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </customElSelect>
            </el-col>
          </el-row>
          <el-row :gutter="16" class="mt-J3">
            <el-col :span="8">
              <customElSelect
                v-model="ElSelect_2.value"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
                :prefix_in="$T('费用类型')"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </customElSelect>
            </el-col>
            <el-col :span="8">
              <el-form-item class="constName" label="" prop="constName">
                <customElInput
                  v-model.trim="CetForm_pagedialog.data.constName"
                  v-bind="ElInput_1"
                  v-on="ElInput_1.event"
                  :prefix_in="$T('成本项名称')"
                ></customElInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <ElInput
                class="w-full"
                v-model="ElInput_2.value"
                v-bind="ElInput_2"
                v-on="ElInput_2.event"
              ></ElInput>
            </el-col>
          </el-row>
        </div>
        <div class="h-[400px] flex flex-col">
          <el-table
            class="flex-auto"
            ref="CetTable"
            :data="schemeData"
            tooltip-effect="light"
            border
            stripe
            @select="handleSelect"
          >
            <template v-for="item in Columns_scheme">
              <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
            </template>
          </el-table>
          <div v-if="showCheckboxGroup_1" class="mt-J1">
            {{ $T("选择计算项") }}：
            <ElCheckboxGroup
              v-model="ElCheckboxGroup_1.value"
              v-bind="ElCheckboxGroup_1"
              v-on="ElCheckboxGroup_1.event"
            >
              <ElCheckbox
                v-for="item in ElCheckboxList_1.options_in"
                :key="item[ElCheckboxList_1.key]"
                :label="item[ElCheckboxList_1.label]"
                :disabled="item[ElCheckboxList_1.disabled]"
              >
                {{ item[ElCheckboxList_1.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </div>
      </div>
    </CetForm>
    <template v-slot:footer>
      <span>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </template>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import commonApi from "@/api/custom";
import { httping } from "@omega/http";
import customElInput from "./customElInput.vue";
export default {
  name: "AddConstComposition",
  components: { customElInput },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    basicfeerateFlag: {
      type: Boolean
    },
    powertarifffeerateFlag: {
      type: Boolean
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      tableData: [],
      currentFeescheme: null,
      showCheckboxGroup_1: false,
      CetDialog_1: {
        title: "",
        "show-close": true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {}
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {
          constName: [
            {
              required: true,
              message: $T("请输入成本项名称"),
              trigger: ["blur"]
            },
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          saveData_out: this.CetForm_pagedialog_saveData_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElSelect_1: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_2: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [],
        key: "feeRateType",
        value: "feeRateType",
        label: "feeRateTypeName",
        disabled: "disabled"
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "100%"
        },
        size: "small",
        event: {}
      },
      ElInput_2: {
        value: "",
        "suffix-icon": "el-icon-search",
        placeholder: $T("请输入费率方案以检索"),
        style: {
          // width:"200px"
        },
        event: {
          change: this.ElInput_2_change_out
        }
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {
          display: "inline-block"
          // width:"300px"
        },
        disabled: false,
        event: {}
      },
      ElCheckboxList_1: {
        options_in: [
          {
            id: 1,
            text: $T("能耗")
          },
          {
            id: 2,
            text: $T("损耗")
          },
          {
            id: 3,
            text: $T("分摊")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      schemeData: [], // 费率表格数据
      Columns_scheme: [
        {
          type: "selection", // selection 勾选 index 序号
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "60",
          selectable: this.checkSelectable
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("费率方案"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "feesubratetype$text", // 支持path a[0].b
          label: $T("费率类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        }
      ],
      multipleSelection: [], // 费率方案勾选项
      // 基本电费的费率类型列表
      rateTypeList1: [
        {
          id: 1,
          text: $T("容量计费")
        },
        {
          id: 2,
          text: $T("需量计费")
        }
      ],
      // 电度电费和附加费的费率类型列表
      rateTypeList2: [
        {
          id: 1,
          text: $T("单一费率")
        },
        {
          id: 2,
          text: $T("分时费率")
        },
        {
          id: 3,
          text: $T("阶梯费率")
        }
      ],
      allTypeList: [] // 所有能源类型、费用类型、费率类型的列表
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    async init() {
      this.ElSelect_2.value = null;
      this.ElInput_2.value = "";
      this.ElCheckboxGroup_1.value = [];
      this.showCheckboxGroup_1 = false;

      await this.getEnergytype();
      await this.querySchemeConfig();
      const { edit } = this.inputData_in;
      this.CetDialog_1.title = edit ? $T("编辑成本构成") : $T("添加成本构成");
      this.CetForm_pagedialog.resetTrigger_in = new Date().getTime();

      const selectData = this.ElOption_1.options_in || [];

      this.ElSelect_1.disabled = !!edit;
      this.ElSelect_2.disabled = !!edit;

      if (edit) {
        this.CetForm_pagedialog.data = this._.cloneDeep(this.inputData_in);
        const { feeratetype, feeratesubtype, costcheckitem } =
          this.inputData_in;
        if (feeratetype === 2 || feeratetype === 4) {
          this.showCheckboxGroup_1 = true;
          this.ElCheckboxList_1.options_in = [
            {
              id: 1,
              text: $T("能耗"),
              disabled: feeratesubtype === 3
            },
            {
              id: 2,
              text: $T("损耗"),
              disabled: feeratesubtype === 3
            },
            {
              id: 3,
              text: $T("分摊"),
              disabled: feeratesubtype === 3
            }
          ];
          if (feeratesubtype !== 3) {
            this.ElCheckboxGroup_1.disabled = false;
          }
          this.ElCheckboxGroup_1.value =
            JSON.parse(costcheckitem).calculateitem;
        }
        this.ElSelect_1.value = this.inputData_in.energytype;
        this.ElSelect_1_change_out(this.inputData_in.energytype);
      } else {
        this.CetForm_pagedialog.data = {};
        this.ElSelect_1.value = selectData[0]?.id;
        this.ElSelect_1_change_out(selectData[0]?.id);
      }
    },
    //获取能源类型
    async getEnergytype() {
      var vm = this;
      vm.ElOption_1.options_in = [];
      const response = await commonApi.getProjectEnergy(this.projectId);
      if (response.code !== 0) {
        return;
      }

      const filterEnergyType = await this.getFilterEnergyType();

      const data = response.data || [];

      const selectData = data.reduce((list, cur) => {
        if (!filterEnergyType.includes(cur.energyType)) {
          list.push({
            id: cur.energytype,
            text: cur.name,
            symbol: common.formatSymbol(cur.symbol) || "--"
          });
        }
        return list;
      }, []);
      this.ElOption_1.options_in = selectData;
    },
    // 查询所有能源类型、费用类型、费率类型
    async querySchemeConfig() {
      const selectData = this.ElOption_1.options_in || [];
      const energytypes = selectData.map(i => i.id);
      const res = await commonApi.queryEnergySchemeConfig(energytypes);
      if (res.code !== 0) {
        return;
      }

      this.allTypeList = this._.cloneDeep(res.data);
    },
    /**
     * 获取需要过滤能源类型
     */
    async getFilterEnergyType() {
      const res = commonApi.organizationConfigFilterEnergyType();
      return res.data || [];
    },
    /**
     * 能源类型选择
     * @param val 能源类型id
     */
    ElSelect_1_change_out(val) {
      if (!val) {
        return;
      }
      const target = this.allTypeList.find(item => item.energyType === val);
      if (!target) return;
      const { feeratetype, edit } = this.inputData_in;
      if (val === 2) {
        //电
        const filterList = [];
        this.basicfeerateFlag && feeratetype !== 1 && filterList.push(1);
        this.powertarifffeerateFlag && feeratetype !== 3 && filterList.push(3);

        this.ElOption_2.options_in = target.feeRateTypesList.filter(
          item => !filterList.includes(item.feeRateType)
        );

        if (edit) {
          this.ElSelect_2.value = feeratetype;
        } else {
          const hasType = target.feeRateTypesList.find(
            item => item.feeRateType === 2
          );
          this.ElSelect_2.value = hasType
            ? 2
            : this.ElOption_2.options_in[0]?.feeRateType;
        }
      } else {
        this.ElOption_2.options_in = this._.cloneDeep(target.feeRateTypesList);
        this.ElSelect_2.value = 2;
      }
      this.ElSelect_2_change_out(this.ElSelect_2.value);
    },
    /**
     * 费用类型选择
     * @param val 费用类型id
     */
    ElSelect_2_change_out(val) {
      if (!val) {
        return;
      }
      if (val === 2 || val === 4) {
        this.showCheckboxGroup_1 = true;
        if (!this.inputData_in.edit) {
          this.ElCheckboxGroup_1.value = [];
        }
      } else {
        if (!this.inputData_in.edit) {
          this.ElCheckboxGroup_1.value = [1];
        }
        this.showCheckboxGroup_1 = false;
      }
      this.getFeerateList();
    },

    // 获取费率列表
    async getFeerateList() {
      if (!this.ElSelect_1.value || !this.ElSelect_2.value) {
        return;
      }
      // 成本项名称 ElInput_1.value
      if (this.AjaxFlag) {
        return;
      }
      this.AjaxFlag = true;
      this.schemeData = [];

      const response = await commonApi.getSchemeConfigFeeScheme(
        this.projectId,
        {
          energyType: this.ElSelect_1.value
        }
      );
      this.AjaxFlag = false;
      if (response.code !== 0) {
        return;
      }
      response.data.forEach(item => {
        const feeratetypeTarget = this.ElOption_2.options_in.find(
          item => item.feeRateType === this.ElSelect_2.value
        );
        item.feeratetype$text =
          feeratetypeTarget && feeratetypeTarget.feeRateTypeName;
        // 费率类型显示
        let target;
        if (item.feeratetype === 1) {
          // 基本电费
          target = this._.find(this.rateTypeList1, ["id", item.feeratesubtype]);
        } else if ([2, 4].includes(item.feeratetype)) {
          // 电度电费、附加费
          target = this._.find(this.rateTypeList2, ["id", item.feeratesubtype]);
        }
        // 费率类型
        item.feesubratetype$text = target && target.text;
      });

      this.tableData = response.data;
      this.ElInput_2.value = "";
      this.ElInput_2_change_out();

      if (!this.inputData_in?.edit) {
        return;
      }

      const data = response.data.find(
        item => item.id === this.inputData_in.feescheme_id
      );

      await this.$nextTick();

      this.$refs.CetTable.setCurrentRow(data);
      this.$refs.CetTable.toggleRowSelection(data, true);
      this.multipleSelection = [data];
    },
    ElInput_2_change_out(val) {
      this.ElInput_2.value = val;
      this.filterTab();
    },
    filterTab() {
      if (this.tableData.length === 0 || !this.ElSelect_2.value) {
        this.schemeData = [];
        return;
      }
      let tableData = this.tableData.filter(item => {
        return item.feeratetype === this.ElSelect_2.value;
      });

      if (this.ElInput_2.value) {
        tableData = tableData.filter(item => {
          return item.name.indexOf(this.ElInput_2.value) !== -1;
        });
      }
      tableData = this._.sortBy(tableData, ["feeratesubtype", "id"]);
      this.schemeData = tableData;
    },
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_pagedialog.localSaveTrigger_in = this._.cloneDeep(val);
    },
    //勾选单行操作
    handleSelect(selection, row) {
      this.multipleSelection = []; //清空已选
      // 选择项大于1时
      if (selection.length > 1) {
        const delRow = selection.shift();
        this.$refs.CetTable.toggleRowSelection(delRow, false);
      }
      this.multipleSelection.push(selection[0]);
      if (this.multipleSelection[0]) {
        this.CetTable_1_record_out(this.multipleSelection[0]);
      }
    },
    CetTable_1_record_out(val) {
      this.currentFeescheme = val;
      // 选择阶梯费率分摊置灰
      if (
        (val.feeratetype === 2 && val.feeratesubtype === 3) ||
        val.feeratetype === 4
      ) {
        if (!this.inputData_in.edit) {
          this.ElCheckboxGroup_1.value = [1];
        }
        this.ElCheckboxGroup_1.disabled = true;
        this.ElCheckboxList_1.options_in = [
          {
            id: 1,
            text: $T("能耗"),
            disabled: true
          },
          {
            id: 2,
            text: $T("损耗"),
            disabled: true
          },
          {
            id: 3,
            text: $T("分摊"),
            disabled: true
          }
        ];
      } else {
        if (!this.inputData_in.edit) {
          this.ElCheckboxGroup_1.value = [];
        }
        this.ElCheckboxGroup_1.disabled = false;
        this.ElCheckboxList_1.options_in = [
          {
            id: 1,
            text: $T("能耗"),
            disabled: false
          },
          {
            id: 2,
            text: $T("损耗"),
            disabled: false
          },
          {
            id: 3,
            text: $T("分摊"),
            disabled: false
          }
        ];
      }
      // 修改时保持选择当前方案
      if (this.inputData_in && this.inputData_in.edit) {
        var data = this.tableData.filter(
          item => item.id === this.inputData_in.feescheme_id
        )[0];
        this.$nextTick(() => {
          this.$refs.CetTable.setCurrentRow(data);
        });
      }
    },
    // 编辑时保持选择当前方案
    checkSelectable() {
      if (this.inputData_in.edit) {
        const target = this.tableData.find(
          item => item.id === this.inputData_in.feescheme_id
        );
        this.multipleSelection = [target];
      }
      return !this.inputData_in.edit;
    },
    CetForm_pagedialog_saveData_out(val) {
      if (!this.multipleSelection[0]) {
        return this.$message({
          message: $T("请选择费率方案"),
          type: "warning"
        });
      }
      // 必须选择一项计费项
      if (
        this.showCheckboxGroup_1 &&
        this.ElCheckboxGroup_1.value.length === 0
      ) {
        return this.$message({
          message: $T("至少选择一项计算项"),
          type: "warning"
        });
      }
      const obj = {
        calculateitem: this.ElCheckboxGroup_1.value
      };
      const { edit } = this.inputData_in;
      const saveData = {
        edit: !!edit,
        energytype: this.ElSelect_1.value,
        feeratetype: this.ElSelect_2.value,
        feescheme_id: this.multipleSelection[0].id,
        name: this.multipleSelection[0].name,
        costcheckitem: JSON.stringify(obj),
        costcheckitem$text:
          this.ElCheckboxGroup_1.value
            .join("、")
            .replace(1, $T("能耗"))
            .replace(2, $T("损耗"))
            .replace(3, $T("分摊")) || "--",
        constName: this.CetForm_pagedialog.data.constName
      };
      if (edit) {
        saveData.id = this.inputData_in.id;
      }
      this.$emit("finishData_out", saveData);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    }
  }
};
</script>
<style lang="scss" scoped>
.constName {
  margin: 0px;
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
:deep(.is-error .el-input__inner) {
  @include border_color(B1);
}
:deep(.el-input__inner:focus) {
  @include border_color(B1);
}
</style>
