<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog
      v-bind="CetDialog_pagedialog"
      v-on="CetDialog_pagedialog.event"
      class="CetDialog"
    >
      <template v-slot:footer>
        <span>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
          <!-- preserve按钮组件 -->
          <CetButton
            class="ml-J0"
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
        </span>
      </template>
      <div class="flex-row flex" style="height: 672px">
        <div
          class="pJ4"
          style="width: 446px; box-sizing: border-box"
          v-show="multidimensional"
        >
          <div class="title">{{ $T("选择维度") }}</div>
          <CetTable
            ref="cetTable"
            :data.sync="CetTable_1.data"
            :dynamicInput.sync="CetTable_1.dynamicInput"
            v-bind="CetTable_1"
            v-on="CetTable_1.event"
            class="mt-J3"
            style="height: calc(100% - 38px)"
          >
            <ElTableColumn width="40">
              <template slot-scope="scope">
                <el-radio v-model="radio" :label="scope.row.id"></el-radio>
              </template>
            </ElTableColumn>
            <ElTableColumn
              type="index"
              :label="$T('序号')"
              width="45"
            ></ElTableColumn>
            <ElTableColumn
              :label="$T('节点树名称')"
              prop="name"
              show-overflow-tooltip
            ></ElTableColumn>
            <ElTableColumn :label="$T('关联节点状态')" prop="relateNode">
              <template slot-scope="scope">
                <el-tag :type="scope.row.relateNode ? 'warning' : 'info'">
                  {{ $T(scope.row.relateNode ? $T("已关联") : $T("未关联")) }}
                </el-tag>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
        <div class="ml-J1 pJ4" style="width: 0; flex: 1">
          <div class="title">{{ $T("关联节点") }}</div>
          <div class="mt-J3 flex-row flex" style="height: calc(100% - 38px)">
            <div class="leftTreeBox flex-col flex" style="flex: 1; width: 0">
              <div class="flex-col flex warp">
                <div class="title">
                  {{ $T("选择节点") }}
                </div>
                <el-checkbox
                  v-model="checked"
                  @change="checkedChange"
                  class="w-full text-right mb-J3"
                >
                  {{ $T("默认选中子节点") }}
                </el-checkbox>
              </div>
              <CetForm
                :data.sync="CetForm_pagedialog.data"
                v-bind="CetForm_pagedialog"
                v-on="CetForm_pagedialog.event"
                class="p0 flex-auto"
              >
                <el-main
                  class="flex-col flex"
                  style="height: 100%; padding: 0px"
                >
                  <CetGiantTree
                    class="giantTree flex-auto"
                    ref="giantTree1"
                    v-show="!checked"
                    v-bind="CetGiantTree_1"
                    v-on="CetGiantTree_1.event"
                  ></CetGiantTree>
                  <CetGiantTree
                    class="giantTree flex-auto"
                    ref="giantTree2"
                    v-show="checked"
                    v-bind="CetGiantTree_2"
                    v-on="CetGiantTree_2.event"
                  ></CetGiantTree>
                  <div class="text-right">
                    <CetButton
                      v-bind="CetButton_reset"
                      v-on="CetButton_reset.event"
                    ></CetButton>
                    <CetButton
                      class="ml-J3"
                      v-bind="CetButton_review"
                      v-on="CetButton_review.event"
                    ></CetButton>
                  </div>
                </el-main>
              </CetForm>
            </div>
            <div class="rightTreeBox ml-J3" style="flex: 1; width: 0">
              <div class="flex-col flex warp">
                <div class="title">{{ $T("已选节点预览") }}</div>
                <div class="selectNum text-right">
                  {{
                    $T(
                      "已选节点：{0} 个",
                      CetVirtualTree_2.filterNodes_in?.length ?? 0
                    )
                  }}
                </div>
              </div>
              <ElInput
                class="mb-J3 search mt-J3"
                v-model.trim="ElInput_2.value"
                v-bind="ElInput_2"
                v-on="ElInput_2.event"
              ></ElInput>
              <CetVirtualTree
                class="cetVirtualTree flex-auto"
                v-bind="CetVirtualTree_2"
                v-on="CetVirtualTree_2.event"
              ></CetVirtualTree>
            </div>
          </div>
        </div>
      </div>
    </CetDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom.js";
import Vue from "vue";
import { httping } from "@omega/http";

export default {
  name: "relatedObjDialog",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      var vm = this;
      var projectId = 0;
      if (vm.$store.state.projectId) {
        projectId = Number(vm.$store.state.projectId);
      } else {
        if (!window.sessionStorage) {
          return false;
        } else {
          var storage = window.sessionStorage;
          projectId = Number(storage.getItem("projectId"));
        }
      }
      return projectId;
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    },
    currentTabItem: {
      type: Object
    }
  },
  data() {
    return {
      saveFlag: true, // 切换表格选中当前行时是否需要提示保存
      radio: "",
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("关联对象"),
        "show-close": true,
        width: "1200px",
        top: "5vh",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },
      currentRow: {},
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this._.debounce(this.CetTable_1_record_out, 300)
        }
      },
      // pagedialog表单组件
      CetForm_pagedialog: {
        dataMode: "static", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "80px",
        rules: {},
        event: {
          currentData_out: this.CetForm_pagedialog_currentData_out,
          saveData_out: this.CetForm_pagedialog_saveData_out,
          finishData_out: this.CetForm_pagedialog_finishData_out,
          finishTrigger_out: this.CetForm_pagedialog_finishTrigger_out
        }
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      CetButton_review: {
        visible_in: true,
        disable_in: false,
        title: $T("预览"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_review_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      checked: false,
      treeCheckedNodes: [],
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: this.setNodeClasses
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      ElInput_2: {
        value: "",
        placeholder: $T("请输入关键字搜索"),
        "suffix-icon": "el-icon-search",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_2_change_out
        }
      },
      CetVirtualTree_2_TreeV2: null,
      CetVirtualTree_2: {
        filterNodes_in: [],
        inputData_in: [],
        attribute: {
          data: [],
          props: {
            value: "tree_id",
            label: "name",
            children: "children",
            disabled: "id"
          },
          emptyText: $T("暂无数据"),
          nodeKey: "tree_id",
          defaultCheckedKeys: [],
          defaultExpandedKeys: [],
          showCheckbox: true,
          highlightCurrent: true,
          checkStrictly: true,
          height: 420,
          filterMethod: this.hideNode
        },
        event: {
          onCreate: this.CetVirtualTree_2_onCreate
        }
      },
      filterCheckData: []
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
      this.checked = false;
      this.saveFlag = true;
      this.ElInput_2.value = "";
      await this.getTableData();
      await this.$nextTick();
      this.$refs.cetTable.$refs.cetTable.setCurrentRow(
        this.CetTable_1.data?.[0]
      );
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = val;
    },
    queryId_in(val) {
      this.CetForm_pagedialog.queryId_in = this._.cloneDeep(val);
    },
    inputData_in(val) {
      this.CetForm_pagedialog.inputData_in = this._.cloneDeep(val);
    }
  },
  methods: {
    async getTableData() {
      const queryData = {
        projectId: this.projectId,
        status: true
      };
      const res = await commonApi.queryCostcheckplanMultitreeConfig(queryData);
      this.CetTable_1.data = res?.data || [];
    },
    setNodeClasses(treeId, treeNode) {
      return treeNode.disabledClass
        ? { add: ["disabledClass"] }
        : { remove: ["disabledClass"] };
    },
    checkedChange() {
      const vm = this;
      let treeCheckedNodes = vm._.cloneDeep(vm.treeCheckedNodes);
      setTimeout(() => {
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        if (vm.checked) {
          vm.CetGiantTree_2.checkedNodes = treeCheckedNodes;
          if (!treeCheckedNodes.length) {
            vm.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
          }
        } else {
          vm.CetGiantTree_1.checkedNodes = treeCheckedNodes;
          if (!treeCheckedNodes.length) {
            vm.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
          }
        }
      }, 0);
    },
    async CetTable_1_record_out(val) {
      if (this.giveUpOut) {
        this.giveUpOut = false;
        return;
      }
      this.CetVirtualTree_2_TreeV2?.setCheckedKeys([]);
      this.checked = false;
      if (this.saveFlag) {
        this.TableClickNextDo(val);
        return;
      }
      this.$confirm($T("是否要保存当前维度关联节点"), $T("提示"), {
        confirmButtonText: $T("保存"),
        cancelButtonText: $T("不保存"),
        type: "warning"
      })
        .then(async () => {
          this.giveUpOut = true;
          const nextFlag = await this.saveData(false);
          if (nextFlag === false) return;
          this.TableClickNextDo(val);
        })
        .catch(() => {
          this.TableClickNextDo(val);
        });
    },
    async TableClickNextDo(val) {
      this.currentRow = this._.cloneDeep(val);
      this.radio = val.id;
      if (val.id === -1 && !val.modelLabel) {
        this.CetGiantTree_1.inputData_in = [];
        this.CetGiantTree_2.inputData_in = [];
        this.CetVirtualTree_2_TreeV2?.setData([]);
        return;
      }
      this.saveFlag = false;
      val.id === -1 ? await this.getTreeData1() : await this.getTreeData2();
      this.setShowNodes();
      const treeData = this.checked
        ? this.CetGiantTree_2.inputData_in
        : this.CetGiantTree_1.inputData_in;
      this.getSchemeObj(treeData);
    },
    CetForm_pagedialog_currentData_out(val) {
      this.$emit("currentData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_saveData_out(val) {
      this.$emit("saveData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishData_out(val) {
      this.$emit("finishData_out", this._.cloneDeep(val));
    },
    CetForm_pagedialog_finishTrigger_out(val) {
      this.$emit("finishTrigger_out", val);
    },
    CetDialog_pagedialog_openTrigger_out(val) {
      this.CetForm_pagedialog.queryTrigger_in = this._.cloneDeep(val);
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_reset_statusTrigger_out() {
      const treeData = this.checked
        ? this.CetGiantTree_2.inputData_in
        : this.CetGiantTree_1.inputData_in;
      this.getSchemeObj(treeData);
    },
    CetButton_review_statusTrigger_out() {
      // 过滤掉禁用的节点
      this.setShowNodes();
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.saveData();
    },
    async saveData(closeDialog = true) {
      // 过滤掉禁用的节点
      const filterData = this.treeCheckedNodes.filter(
        item =>
          !this.filterCheckData.some(
            ite =>
              item.id === ite.objectid && item.modelLabel === ite.objectlabel
          )
      );
      const nodesData = filterData.map(item => {
        return {
          id: item.id,
          modelLabel: item.modelLabel,
          name: item.name
        };
      });
      const data = {
        costCheckPlanId: this.currentTabItem.id,
        projectId: this.projectId,
        dimTreeConfigId: this.currentRow.id,
        nodes: nodesData
      };
      const res = await commonApi.saveCostcheckplanCostCheckNodeConfig(data);
      if (res?.code !== 0) return;
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });

      if (closeDialog) {
        this.CetDialog_pagedialog.closeTrigger_in = new Date().getTime();
        this.$emit("refreshData");
      } else {
        this.saveFlag = true;
        await this.getTableData();
        await this.$nextTick();
        this.saveFlag = false;
      }
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.$emit("refreshData");
      this.CetDialog_pagedialog.closeTrigger_in = val;
    },
    CetGiantTree_1_checkedNodes_out(val) {
      if (!this.checked) {
        this.treeCheckedNodes = this._.cloneDeep(val);
      }
    },
    CetGiantTree_2_checkedNodes_out(val) {
      if (this.checked) {
        this.treeCheckedNodes = this._.cloneDeep(val);
      }
    },
    // 获取节点树
    getTreeData1() {
      var _this = this;
      var data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: null,
                  operator: "EQ",
                  prop: "roomtype",
                  tagid: 2
                }
              ]
            },
            modelLabel: "room"
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "airconditioner" },
          { modelLabel: "civicpipe" },
          { modelLabel: "virtualbuildingnode" },
          { modelLabel: "virtualdevicenode" }
        ],
        treeReturnEnable: true,
        // 按照3.5迭代骆海瑞要求添加该字段进行处理权限只返回一个项目节点的情况
        filterNoAuthEndNode: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0) {
          _this.getSchemeObj(res.data);
        }
      });
    },
    // 获取维度节点树
    async getTreeData2() {
      const queryData = {
        dimTreeConfigId: this.currentRow.id
      };
      const res = await commonApi.getAttributeDimensionTreeNodetree(queryData);
      const treeData = this._.get(res, "data", []);
      this.CetGiantTree_1.inputData_in = treeData;
      this.CetGiantTree_2.inputData_in = treeData;
      this.CetVirtualTree_2_TreeV2.setData(this.setReviewTreeData(treeData));
      this.CetVirtualTree_2_TreeV2.setCheckedKeys([]);
    },
    setReviewTreeData(data) {
      if (!data.length) return [];
      data.forEach(item => {
        this.$set(item, "disabled", true);
        if (item.children?.length) this.setReviewTreeData(item.children);
      });
      return data;
    },
    // 获取所有已关联的节点
    getSchemeObjAll(fn) {
      httping({
        url: `/eem-service/v2/cost-check-plan/cost-check-node-config/project/${this.projectId}?dimTreeConfigId=${this.currentRow.id}`,
        method: "GET"
      }).then(response => {
        if (response.code === 0 && response.data.length > 0) {
          fn(response.data);
        } else {
          fn([]);
        }
      });
    },
    // 获取方案的关联对象
    getSchemeObj(treeData) {
      this.treeCheckedNodes = [];
      this.getSchemeObjAll(data => {
        httping({
          url: `/eem-service/v2/cost-check-plan/cost-check-node-config/${this.currentTabItem.id}?dimTreeConfigId=${this.currentRow.id}`,
          method: "GET"
        }).then(response => {
          if (response.code === 0 && response.data.length > 0) {
            const nodes = response.data
              .filter(
                item =>
                  item.costcheckplan_id === this.currentTabItem.id &&
                  item.dimtreeconfigid === this.currentRow.id
              )
              .map(item => {
                return {
                  id: item.objectid,
                  modelLabel: item.objectlabel,
                  tree_id: item.objectlabel + "_" + item.objectid
                };
              });
            this.CetGiantTree_1.checkedNodes = this._.cloneDeep(nodes);
            this.CetGiantTree_2.checkedNodes = this._.cloneDeep(nodes);
            this.treeCheckedNodes = nodes;
            setTimeout(() => {
              this.expandNode(nodes, "tree_id", this.$refs.giantTree1.ztreeObj);
              this.expandNode(nodes, "tree_id", this.$refs.giantTree2.ztreeObj);
            }, 0);

            // 设置节点禁用
            var newData = data.filter(
              item =>
                !response.data.some(
                  ite =>
                    item.objectid === ite.objectid &&
                    item.objectlabel === ite.objectlabel
                )
            );
            this.filterCheckData = this._.cloneDeep(newData);
            this.setTreeLeaf(treeData, newData);
          } else {
            this.filterCheckData = this._.cloneDeep(data);
            this.CetGiantTree_1.checkedNodes = [];
            this.CetGiantTree_2.checkedNodes = [];
            this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
            this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
            this.treeCheckedNodes = [];
            this.setTreeLeaf(treeData, data);
          }
          this.CetGiantTree_1.inputData_in = treeData;
          this.CetGiantTree_2.inputData_in = treeData;
          this.CetVirtualTree_2_TreeV2?.setData(treeData);
          this.setShowNodes();
        });
      });
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      nodes.forEach(item => {
        let node = ztreeObj.getNodeByParam(key, item[key]);
        let parentNodes = [],
          parentNode = node && node.getParentNode();
        while (parentNode) {
          parentNodes.push(parentNode);
          parentNode = parentNode.getParentNode();
        }
        parentNodes.forEach(i => {
          ztreeObj.expandNode(i, true);
        });
      });
      $(this.$refs.giantTree1.$el).find("#giantTree").scrollTop(0);
      $(this.$refs.giantTree2.$el).find("#giantTree").scrollTop(0);
    },
    setTreeLeaf(nodesAll, nodes) {
      if (!this._.isArray(nodesAll)) return;
      nodesAll.forEach(item => {
        var flag = false;
        nodes.forEach(ite => {
          if (item.id === ite.objectid && item.modelLabel === ite.objectlabel) {
            flag = true;
          }
        });
        if (flag) {
          Vue.set(item, "disabledClass", true);
        } else {
          Vue.set(item, "disabledClass", false);
        }
        this.setTreeLeaf(this._.get(item, "children", []) || [], nodes);
      });
    },
    CetVirtualTree_2_onCreate(event) {
      this.CetVirtualTree_2_TreeV2 = event;
    },
    ElInput_2_change_out(val) {
      this.CetVirtualTree_2_TreeV2.filter(val);
    },
    // 获取需要展示的节点
    async setShowNodes() {
      await this.$nextTick();
      const filterData = this.treeCheckedNodes.filter(
        item =>
          !this.filterCheckData.some(
            ite =>
              item.id === ite.objectid && item.modelLabel === ite.objectlabel
          )
      );
      const showTreeKeys = [];
      filterData.forEach(item => {
        const tree_id = item.tree_id;
        if (!showTreeKeys.includes(tree_id)) {
          showTreeKeys.push(tree_id);
        }
      });
      this.CetVirtualTree_2_TreeV2.setCheckedKeys(
        filterData.map(i => i.tree_id)
      );
      this.CetVirtualTree_2.filterNodes_in = showTreeKeys;
      this.CetVirtualTree_2_TreeV2.filter(this.ElInput_2.value);
    },
    hideNode(value, data) {
      const showTreeKeys = this.CetVirtualTree_2.filterNodes_in ?? [];
      if (!showTreeKeys.includes(data.tree_id)) return false;
      if (!value) return true;
      return data.name.includes(value);
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.giantTree {
  :deep(.disabledClass) {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      background: #969696 no-repeat center center;
      background-size: 100% 100%;
      height: 14px;
      left: -22px;
      top: 4px;
      width: 14px;
      z-index: 1;
      cursor: no-drop;
    }
    .node_name {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        background: #969696 no-repeat center center;
        background-size: 100% 100%;
        height: 14px;
        left: -22px;
        top: 5px;
        width: 14px;
        cursor: no-drop;
        z-index: 1;
      }
    }
  }
  :deep(.device-search) {
    margin-top: 0;
  }
}
.CetDialog {
  :deep(.el-dialog__body) {
    @include padding(J1);
    @include background_color(BG);
  }
  .title {
    @include font_size(H3);
  }
  :deep() {
    .el-radio__label {
      display: none;
    }
  }
  .el-tag {
    height: 30px;
    line-height: 30px;
  }
  .el-tag--warning {
    @include border_color(Sta2);
  }
  .el-tag--info {
    border: 1px solid;
    @include border_color(B1);
    @include font_color(T6, !important);
    @include background_color(BG2);
  }
  .leftTreeBox .gianttree {
    height: calc(100% - 70px);
  }
  .leftTreeBox,
  .rightTreeBox {
    border: 1px solid;
    @include border_color(B1);
    border-radius: var(--Ra);
    @include padding(J4);
  }
  .cetVirtualTree {
    :deep(.el-vl__window.el-tree-virtual-list::-webkit-scrollbar-track) {
      background-color: transparent;
    }
    :deep(.el-virtual-scrollbar) {
      display: none;
    }
  }
}
</style>
