<template>
  <div class="fullheight">
    <div class="fullheight flex-row flex">
      <div
        class="fullheight flex-col flex w-[500px] bg-BG1 p-J4 rounded-Ra box-border"
      >
        <div class="mb-J3 flex items-center justify-between">
          <ElInput
            v-model="ElInput_1.value"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
          <div>
            <CetButton
              :disable_in="currentTabItem ? false : true"
              class="ml-J1"
              v-bind="CetButton_relatedObj"
              v-on="CetButton_relatedObj.event"
              v-permission="'costcheckitem_associate'"
            ></CetButton>
            <CetButton
              class="ml-J1"
              v-bind="CetButton_5"
              v-on="CetButton_5.event"
              v-permission="'costcheckitem_edit'"
            ></CetButton>
          </div>
        </div>
        <CetTable
          style="height: calc(100% - 48px)"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          ref="schemeTable"
        >
          <ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_operate">
            <template slot-scope="scope">
              <span
                class="handel fl mr-J3"
                @click="handleEdit(scope.$index, scope.row)"
                v-permission="'costcheckitem_edit'"
              >
                {{ $T("编辑") }}
              </span>
              <span
                class="handel fl mr-J1 delete"
                :class="{
                  clickdisable: scope.row.bindNode
                }"
                @click.stop="handleDelete(scope.row)"
                v-permission="'costcheckitem_edit'"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
      <div class="flex-auto fullheight ml-J3 bg-BG1 p-J4 rounded-Ra box-border">
        <CostComposition
          :currentSchemeItem="currentTabItem"
          :allTypeList="allTypeList"
          @setCostcheckitem="setCostcheckitem"
        ></CostComposition>
      </div>
    </div>
    <addOrEditScheme
      v-bind="addOrEditScheme"
      v-on="addOrEditScheme.event"
    ></addOrEditScheme>
    <relatedObjDialog
      v-bind="relatedObjDialog"
      v-on="relatedObjDialog.event"
      :currentTabItem="currentTabItem"
    ></relatedObjDialog>
  </div>
</template>
<script>
import commonApi from "@/api/custom";
import addOrEditScheme from "./dialog/addOrEditScheme.vue";
import CostComposition from "./CostComposition.vue";
import relatedObjDialog from "./dialog/relatedObjDialog.vue";

export default {
  name: "accountingschemePage",
  components: {
    addOrEditScheme,
    CostComposition,
    relatedObjDialog
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      tabData: [],
      currentTabItem: null,
      allTypeList: [], // 所有能源类型、费用类型、费率类型的列表
      ElInput_1: {
        value: "",
        placeholder: $T("输入关键字搜索"),
        "suffix-icon": "el-icon-search",
        style: {
          width: "200px"
        },
        size: "small",
        event: {
          change: this.ElInput_1_change_out
        }
      },
      CetButton_relatedObj: {
        visible_in: true,
        title: $T("关联对象"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_relatedObj_statusTrigger_out
        }
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      ElTableColumn_index: {
        type: "index", // selection 勾选 index 序号
        label: $T("序号"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "70" //绝对宽度
      },
      // name组件
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("核算方案名称"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumn_operate: {
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "120" //绝对宽度
      },
      CetButton_5: {
        visible_in: true,
        disable_in: false,
        title: $T("新增方案"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_5_statusTrigger_out
        }
      },
      addOrEditScheme: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: {},
        isEdit: false, // 编辑还是新增
        event: {
          saveData_out: this.saveSchemeName
        }
      },
      relatedObjDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: {},
        event: {
          refreshData: this.getTabData
        }
      },
      costcheckitem: [] // 某个核算方案的成本构成
    };
  },

  methods: {
    CetButton_relatedObj_statusTrigger_out() {
      this.relatedObjDialog.openTrigger_in = new Date().getTime();
    },
    handleEdit(index, row) {
      this.addOrEditScheme.isEdit = true;
      this.addOrEditScheme.inputData_in = row;
      this.addOrEditScheme.openTrigger_in = new Date().getTime();
    },
    handleDelete(row) {
      if (row.bindNode) return;
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            const response = await commonApi.costCheckPlanDelete(row.id);
            if (response.code !== 0) {
              return;
            }
            this.$message({
              message: $T("删除成功"),
              type: "success"
            });
            this.getTabData();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    // 查询所有能源类型、费用类型、费率类型
    async getEnergytype() {
      const res = await commonApi.queryEnergySchemeConfig([]);
      if (res.code !== 0) {
        return;
      }
      this.allTypeList = res.data;
    },
    // 获取列表数据
    async getTabData() {
      const response = await commonApi.costCheckPlanList(this.projectId);
      if (!response.data?.length) {
        this.tabData = [];
        this.CetTable_1.data = [];
        return;
      }
      this.tabData = response.data;
      this.filterTabData();
    },
    filterTabData() {
      if (this.tabData?.length && this.ElInput_1.value) {
        this.CetTable_1.data = this.tabData.filter(item => {
          return item.name.indexOf(this.ElInput_1.value) !== -1;
        });
      } else {
        this.CetTable_1.data = this._.cloneDeep(this.tabData);
      }
      if (this.currentTabItem) {
        this.$refs.schemeTable.$refs.cetTable.setCurrentRow(
          this.currentTabItem
        );
      }
    },
    // 新建核算方案
    CetButton_5_statusTrigger_out() {
      this.addOrEditScheme.isEdit = false;
      this.addOrEditScheme.openTrigger_in = new Date().getTime();
    },
    setCostcheckitem(val) {
      this.costcheckitem = this._.cloneDeep(val);
    },
    // 保存核算方案名称
    async saveSchemeName(params) {
      const costcheckitemModel = this.costcheckitem || [];
      const data = {
        name: params.name,
        createtime: this.$moment().valueOf(),
        costcheckitem_model: params.id ? costcheckitemModel : [],
        projectid: this.projectId,
        id: params.id || 0
      };

      const response = await commonApi.costCheckPlanSave(data);
      if (response.code !== 0) {
        return;
      }

      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.addOrEditScheme.closeTrigger_in = new Date().getTime();
      this.getTabData();
    },
    ElInput_1_change_out() {
      this.filterTabData();
    },
    CetTable_1_record_out(val) {
      this.currentTabItem = val.id !== -1 ? val : null;
    }
  },
  async mounted() {
    await this.getEnergytype();
    this.getTabData();
  }
};
</script>
<style lang="scss" scoped>
.handel {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
  &.clickdisable {
    @include font_color(T4);
    cursor: not-allowed !important;
  }
}
</style>
