<template>
  <div class="overflow-auto h-full">
    <div
      class="border-[1px] border-solid border-B1 rounded-Ra p-J3 h-[380px] flex flex-col"
    >
      <div class="mb-J3 text-H3 font-bold">
        {{ $T("核算统计") }}
      </div>
      <CetChart
        class="flex-auto"
        v-bind="CetChart_comprehensiveCost"
      ></CetChart>
    </div>
    <div
      class="flex-col flex mt-J3 flex-auto border-[1px] border-solid border-B1 rounded-Ra p-J3 h-[300px]"
    >
      <div class="mb-J3 text-H3 font-bold">
        {{ $T("能源成本核算") }}
      </div>
      <div class="flex-auto">
        <el-table
          style="height: 100%"
          height="100%"
          border
          stripe
          tooltip-effect="light"
          :data.sync="CetTable_cost.data"
          :dynamicInput.sync="CetTable_cost.dynamicInput"
          v-bind="CetTable_cost"
          v-on="CetTable_cost.event"
        >
          <ElTableColumn
            type="index"
            :label="$T('序号')"
            headerAlign="left"
            align="left"
            :width="language ? 100 : 70"
          ></ElTableColumn>
          <template v-if="ElTableColumnArr.length">
            <ElTableColumn
              v-for="(item, index) in ElTableColumnArr"
              :key="index"
              v-bind="item"
            ></ElTableColumn>
          </template>
          <ElTableColumn v-else></ElTableColumn>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import omegaI18n from "@omega/i18n";

export default {
  name: "costAccount",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    language() {
      return omegaI18n.locale === "en";
    }
  },
  props: {
    selectTime: {
      type: String
    },
    currentNode: {
      type: Object
    },
    queryTime: {
      type: Object
    },
    dimConfigId: Number
  },
  data() {
    return {
      CetChart_comprehensiveCost: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      CetTable_cost: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          orders: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true, // 是否有queryNode入参, 没有则需要配置为false
          showSummary: {
            enabled: false,
            summaryColumns: [1],
            summaryTitle: $T("合计")
          }
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {}
      },
      ElTableColumnArr: [] // 表格列
    };
  },
  watch: {
    currentNode() {
      this.getAllData();
    },
    queryTime() {
      this.getAllData();
    }
  },
  methods: {
    formatNumberWithPrecision: common.formatNumberWithPrecision,
    formatNum: common.formatNum,
    getAllData: _.debounce(function () {
      this.CetChart_comprehensiveCost.options = {};
      this.ElTableColumnArr = [];
      this.CetTable_cost.data = [];
      if (!this.currentNode || this._.isEmpty(this.queryTime)) return;
      this.getEnergycostcheck();
    }, 300),
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 14) {
        return this.$moment(date).format("DD");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY/MM");
      }
    },
    // 能源成本核算
    getEnergycostcheck() {
      var _this = this;
      const params = {
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        },
        energyType: 13,
        projectId: this.projectId,
        ...this.queryTime,
        dimConfigId: this.dimConfigId
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        params.timeRanges = this.currentNode.effTimeList;
      }
      customApi.queryEnergycostcheck(params).then(res => {
        if (res.code === 0 && res.data) {
          const head = [
            {
              label: $T("核算周期"),
              prop: "cycle",
              width: 150,
              showOverflowTooltip: true,
              headerAlign: "left",
              align: "left"
            }
          ];
          const list = [];
          const data = res.data.data;
          const series = []; // 成本核算图数据
          res.data.header.map((item, index) => {
            const obj = {
              label: item.name,
              prop: "node" + index,
              minWidth: 140,
              showOverflowTooltip: true,
              headerAlign: "left",
              align: "right",
              formatter(val) {
                if (val["node" + index] || val["node" + index] === 0) {
                  return _this.formatNum(val["node" + index]);
                } else {
                  return "--";
                }
              }
            };
            head.push(obj);
            // 处理图数据
            if (index !== res.data.header.length - 1) {
              series[index] = {
                type: "bar",
                barMaxWidth: 30,
                data: []
              };
            }
          });
          const str = this.queryTime.cycle === 17 ? "YYYY-MM" : "YYYY-MM-DD";
          // 处理x轴
          const xAxisData = [];
          const xName = this.queryTime.cycle === 14 ? $T("天数") : $T("月份");
          for (const item in data) {
            const time = this.$moment(item * 1).format(str);
            xAxisData.push(this.getAxixs(item * 1, this.queryTime.cycle));
            const obj = {
              cycle: time
            };
            data[item].map((item1, i) => {
              const name = "node" + i;
              if (item1.value != null) obj[name] = item1.value.toFixed2(2);
              if (i < data[item].length - 1) {
                series[i].data.push(item1);
                series[i].name = data[item][i].name;
              }
            });
            list.push(obj);
          }
          this.ElTableColumnArr = head;
          this.CetTable_cost.data = list;

          this.CetChart_comprehensiveCost.options = {
            toolbox: {
              top: 30,
              right: 30,
              feature: {
                saveAsImage: {}
              }
            },
            tooltip: {
              trigger: "axis",
              confine: true,
              // appendToBody: true,
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
              },
              formatter(params) {
                const cycle = _this.queryTime.cycle;
                const formatStr =
                  cycle === 14 ? "YYYY-MM-DD" : cycle === 17 ? "YYYY-MM" : "";
                let str =
                  _this.$moment(params[0].data.time).format(formatStr) +
                  "<br />";
                params.forEach(item => {
                  str +=
                    item.marker +
                    item.seriesName +
                    ": " +
                    (item.data.value || item.data.value === 0
                      ? Number(item.data.value).toFixed(2)
                      : "--") +
                    (res.data.unitName || "") +
                    "<br />";
                });
                return str;
              }
            },
            legend: {
              type: "scroll",
              top: 10
            },
            dataZoom: [],
            grid: {
              left: "16",
              right: this.language ? 60 : "4%",
              bottom: "16",
              containLabel: true
            },
            xAxis: {
              type: "category",
              name: xName,
              nameLocation: "end",
              data: xAxisData,
              axisPointer: {
                type: "shadow"
              }
            },
            yAxis: {
              name: res.data.unitName,
              type: "value",
              nameTextStyle: {
                align: "right"
              }
            },
            series
          };
          if (series.length > 3) {
            this.CetChart_comprehensiveCost.options.grid.bottom = "15%";
            this.CetChart_comprehensiveCost.options.dataZoom = [
              {
                type: "inside",
                start: 0,
                end: 50
              },
              {
                start: 0,
                end: 100,
                handleSize: "30"
              }
            ];
          }
        }
      });
    }
  },
  mounted() {
    this.getAllData();
  }
};
</script>
