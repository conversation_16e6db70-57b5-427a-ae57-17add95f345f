<template>
  <div class="page overflow-auto h-full">
    <!-- 综合成本趋势 -->
    <div
      class="flex-col flex border-[1px] border-solid border-B1 rounded-Ra p-J3 h-[380px]"
    >
      <div class="mb-J3 text-H3 font-bold">
        {{ $T("综合成本趋势") }}
      </div>
      <div class="flex-auto chartBox">
        <CetChart v-bind="CetChart_comprehensiveCost"></CetChart>
      </div>
    </div>
    <div class="proportion h-[300px] flex-row flex">
      <div
        class="flex-auto flex-col flex border-[1px] border-solid border-B1 rounded-Ra p-J3"
      >
        <div class="mb-J3 text-H3 font-bold">
          {{ $T("综合成本概览") }}
        </div>
        <div class="proportion-item avg-cost flex-auto flex-col flex">
          <div class="cost-item mb-J1">
            <span>{{ $T("时间") }}:</span>
            &emsp;
            <span>{{ selectTime }}</span>
          </div>
          <div class="cost-item mb-J1">
            <span>{{ $T("总用能成本") }}:</span>
            &emsp;
            <span>
              {{ formatNumberWithPrecision(totalCost.totalValue, 2) }}
            </span>
            <span>{{ totalCost.unit }}</span>
          </div>
          <div class="compare flex-auto flex flex-col">
            <div class="flex flex-row">
              <span class="preText flex-auto p-J0">{{ $T("同比") }}</span>
              <span
                v-if="queryTime.cycle === 14"
                class="lastText ml-J3 flex-auto p-J0"
              >
                {{ $T("环比") }}
              </span>
            </div>
            <div class="flex-auto flex flex-row">
              <div class="flex-auto relative">
                <CetChart v-bind="CetChart_avgcost1"></CetChart>
                <div class="label">
                  <el-tooltip
                    :content="
                      _.isEmpty(tbLabel) ? '' : tbLabel.price + tbLabel.unit
                    "
                    effect="light"
                  >
                    <div class="price text-ellipsis">
                      <span>{{ tbLabel.price }}</span>
                    </div>
                  </el-tooltip>
                  <div class="percent">
                    <el-tooltip :content="tbLabel.percent" effect="light">
                      <span class="text-ellipsis font12">
                        {{ tbLabel.percent }}
                      </span>
                    </el-tooltip>
                    <img :src="tbLabel.src" v-if="tbLabel.src" alt="" />
                  </div>
                </div>
              </div>
              <div
                class="flex-auto relative ml-J3"
                v-if="queryTime.cycle === 14"
              >
                <CetChart v-bind="CetChart_avgcost2"></CetChart>
                <div class="label">
                  <el-tooltip
                    :content="
                      _.isEmpty(hbLabel) ? '' : hbLabel.price + hbLabel.unit
                    "
                    effect="light"
                  >
                    <div class="price text-ellipsis">
                      <span>{{ hbLabel.price }}</span>
                    </div>
                  </el-tooltip>
                  <div class="percent">
                    <el-tooltip :content="hbLabel.percent" effect="light">
                      <span class="text-ellipsis font12">
                        {{ hbLabel.percent }}
                      </span>
                    </el-tooltip>
                    <img :src="hbLabel.src" v-if="hbLabel.src" alt="" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex-auto ml-J3 mr-J3 flex-col flex border-[1px] border-solid border-B1 rounded-Ra p-J3"
      >
        <div class="mb-J3 text-H3 font-bold">
          {{ $T("分类成本占比") }}
        </div>
        <div class="proportion-item flex-auto flex-col flex">
          <div class="cost-item mb-J1">
            <span>{{ $T("时间") }}:</span>
            &emsp;
            <span>{{ selectTime }}</span>
          </div>
          <div class="classify flex-auto">
            <CetChart
              v-if="CetChart_energyCost.options.series[0].data.length"
              v-bind="CetChart_energyCost"
            ></CetChart>
            <div class="itemBox-empty" v-else>{{ $T("暂无数据") }}</div>
          </div>
        </div>
      </div>
      <div
        class="flex-auto flex-col flex border-[1px] border-solid border-B1 rounded-Ra p-J3"
      >
        <div class="mb-J3 text-H3 font-bold">
          {{ $T("区域成本占比") }}
        </div>
        <div
          class="proportion-item flex-auto flex-col flex"
          style="margin-right: 0"
        >
          <div class="cost-item mb-J1">
            <span>{{ $T("时间") }}:</span>
            &emsp;
            <span>{{ selectTime }}</span>
          </div>
          <div class="classify flex-auto">
            <CetChart
              v-if="CetChart_areaCost.options.series[0].data.length"
              v-bind="CetChart_areaCost"
            ></CetChart>
            <div class="itemBox-empty" v-else>{{ $T("暂无数据") }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import omegaI18n from "@omega/i18n";

export default {
  name: "costTrend",
  components: {},
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    language() {
      return omegaI18n.locale === "en";
    }
  },
  props: {
    selectTime: {
      type: String
    },
    currentNode: {
      type: Object
    },
    queryTime: {
      type: Object
    },
    dimConfigId: Number
  },
  data() {
    return {
      energyList: [], // 过滤energytype=13后的能源类型列表
      CetChart_comprehensiveCost: {
        //组件输入项
        inputData_in: null,
        options: {}
      },
      totalCost: {
        totalValue: null, // 总用能成本
        unit: "" // 单位
      },
      tbLabel: {}, // 同比数据
      hbLabel: {}, // 环比数据
      // 同比
      CetChart_avgcost1: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 环比
      CetChart_avgcost2: {
        //组件输入项
        inputData_in: null,
        options: {
          series: [
            {
              name: $T("同比"),
              type: "pie",
              radius: ["65%", "85%"],
              avoidLabelOverlap: false,
              emphasis: {
                scale: false
              },
              label: {
                position: "center",
                rich: {
                  a: {
                    color: "#00fff9",
                    fontSize: 24
                  },
                  b: {
                    color: "#00fff9"
                  },
                  c: {
                    fontSize: 16,
                    color: "#fff"
                  },
                  d: {
                    width: 14,
                    backgroundColor: {
                      image: "/static/assets/icons/arrow_up.png"
                    }
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      },
      // 分类成本占比
      CetChart_energyCost: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            type: "scroll",
            left: 10,
            bottom: -5
          },
          tooltip: {
            trigger: "item",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: "60%",
              center: ["50%", "40%"],
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      },
      // 区域成本占比
      CetChart_areaCost: {
        //组件输入项
        inputData_in: null,
        options: {
          legend: {
            type: "scroll",
            left: 10,
            bottom: -5
          },
          tooltip: {
            trigger: "item",
            formatter(params) {
              return (
                params.name +
                ": " +
                Number(params.value).toFixed(2) +
                (params.data.unit || "") +
                "(" +
                Number(params.percent).toFixed(2) +
                "%)"
              );
            }
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: "60%",
              center: ["50%", "40%"],
              label: {
                show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter(params) {
                  return (
                    params.name + ": " + Number(params.percent).toFixed(2) + "%"
                  );
                }
              },
              data: []
            }
          ]
        }
      }
    };
  },
  watch: {
    currentNode() {
      this.getAllData();
    },
    queryTime() {
      this.getAllData();
    }
  },
  methods: {
    formatNumberWithPrecision: common.formatNumberWithPrecision,
    async getProjectEnergy() {
      this.CetChart_comprehensiveCost.options = {};
      this.CetChart_energyCost.options.series[0].data = [];
      // 过滤折标煤
      const res = await customApi.getProjectEnergy(this.projectId);

      if (res.code !== 0) {
        return;
      }
      const filterEnergyType = await this.getFilterEnergyType();
      const data = res.data || [];
      const list = data.filter(
        item => !filterEnergyType.includes(item.energytype)
      );
      this.energyList = this._.cloneDeep(list);
      this.getAllData();
    },
    /**
     * 获取需要过滤能源类型
     */
    async getFilterEnergyType() {
      const res = customApi.organizationConfigFilterEnergyType();
      return res.data || [];
    },

    getAllData: _.debounce(function () {
      if (!this.currentNode || this._.isEmpty(this.queryTime)) return;
      this.getComprehensiveTrend();
      this.getComprehensiveCost();
      this.getClassifyCostPercent();
      this.getAreaCost();
    }, 300),
    // 综合成本趋势tooltip格式化
    formatTooltip(params, unit) {
      if (!params[0].data.logtime) return;
      const cycle = this.queryTime.cycle;
      const formatStr =
        cycle === 14 ? "YYYY-MM-DD" : cycle === 17 ? "YYYY-MM" : "";
      let str =
        this.$moment(params[0].data.logtime).format(formatStr) + "<br />";
      params.forEach(item => {
        str +=
          item.marker +
          item.seriesName +
          ": " +
          (item.data.value || item.data.value === 0
            ? Number(item.data.value).toFixed(2)
            : "--") +
          (unit || "") +
          "<br />";
      });
      return str;
    },
    //过滤获取图表x轴对应值
    getAxixs(date, type) {
      if (type === 14) {
        return this.$moment(date).format("DD");
      } else if (type === 17) {
        return this.$moment(date).format("YYYY/MM");
      }
    },
    formatterDate(cellValue, formatStr = "YYYY-MM-DD HH:mm:ss") {
      if (cellValue) {
        return this.$moment(cellValue).format(formatStr);
      } else if (cellValue === 0 || cellValue === "") {
        return cellValue;
      } else {
        return "--";
      }
    },
    // 综合成本图例综合成本、同比、环比增加悬浮提示
    formatLegend(name, startTime, endTime) {
      const cycle = this.queryTime.cycle;
      let str = name + ":";
      const monthStr = this.language ? "YYYY-MM" : "YYYY年MM月";
      const dayStr = this.language ? "YYYY-MM-DD" : "YYYY年MM月DD日";
      const formatStr = cycle === 17 ? monthStr : dayStr;
      const unit = cycle === 17 ? "M" : "d";
      if (name === $T("综合成本")) {
        str +=
          this.formatterDate(startTime, formatStr) +
          "~" +
          this.formatterDate(this.$moment(endTime).add(1, unit), formatStr);
      }
      if (name === $T("同比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "Y"),
            formatStr
          ) +
          "~" +
          this.formatterDate(
            this.$moment(endTime).add(1, unit).subtract(1, "Y"),
            formatStr
          );
      }
      if (name === $T("环比")) {
        str +=
          this.formatterDate(
            this.$moment(startTime).subtract(1, "M"),
            formatStr
          ) +
          "~" +
          this.formatterDate(startTime, formatStr);
      }
      return str;
    },
    // 综合成本趋势
    getComprehensiveTrend() {
      const _this = this;
      const params = {
        ids: [this.currentNode.id],
        modelLabel: this.currentNode.modelLabel,
        energyTypes: [13],
        ...this.queryTime,
        dimConfigId: this.dimConfigId
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        params.timeRanges = [
          {
            objectId: this.currentNode.id,
            objectLabel: this.currentNode.modelLabel,
            timeRanges: this.currentNode.effTimeList
          }
        ];
      }
      customApi.queryComprehensiveTrend(params).then(res => {
        if (res.data?.nowData?.length) {
          // 处理x轴
          const xAxisData = [];
          // 自然周期和非自然周期返回的开始时间和结束时间
          const startTime = res.data.nowData[0].logtime;
          const endTime = res.data.nowData[res.data.nowData.length - 1].logtime;
          res.data.nowData.forEach(item => {
            xAxisData.push(this.getAxixs(item.logtime, this.queryTime.cycle));
          });
          if (this.queryTime.cycle === 14) {
            this.CetChart_comprehensiveCost.options = {
              toolbox: {
                top: 30,
                right: 30,
                feature: {
                  saveAsImage: {
                    title: $T("保存为图片")
                  }
                }
              },
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unitName);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: "3%",
                right: this.language ? 60 : "4%",
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("天数"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unitName,
                nameTextStyle: {
                  align: "right"
                }
              },
              series: [
                {
                  name: $T("综合成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.nowData
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.yoyData,
                  smooth: true
                },
                {
                  name: $T("环比"),
                  type: "line",
                  data: res.data.chainData,
                  smooth: true
                }
              ]
            };
          } else {
            this.CetChart_comprehensiveCost.options = {
              tooltip: {
                trigger: "axis",
                formatter(params) {
                  return _this.formatTooltip(params, res.data.unitName);
                }
              },
              legend: {
                top: 10,
                tooltip: {
                  show: true,
                  formatter(params) {
                    return _this.formatLegend(params.name, startTime, endTime);
                  }
                }
              },
              grid: {
                left: "3%",
                right: this.language ? 60 : "4%",
                bottom: "3%",
                containLabel: true
              },
              xAxis: {
                type: "category",
                name: $T("月份"),
                nameLocation: "end",
                data: xAxisData,
                axisPointer: {
                  type: "shadow"
                }
              },
              yAxis: {
                type: "value",
                name: res.data.unitName,
                nameTextStyle: {
                  align: "right"
                }
              },
              series: [
                {
                  name: $T("综合成本"),
                  type: "bar",
                  barMaxWidth: 30,
                  data: res.data.nowData
                },
                {
                  name: $T("同比"),
                  type: "line",
                  data: res.data.yoyData,
                  smooth: true
                }
              ]
            };
          }
        }
      });
    },
    // 综合成本概览
    getComprehensiveCost() {
      const nodes = [
        {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel,
          name: this.currentNode.name
        }
      ];
      const params = {
        nodes,
        energyType: 13,
        queryType:
          this.queryTime.cycle === 17 ? 1 : this.queryTime.cycle === 14 ? 3 : 0,
        costKpiType: 0,
        projectId: this.projectId,
        ...this.queryTime,
        dimConfigId: this.dimConfigId
      };

      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        params.timeRanges = [
          {
            objectId: this.currentNode.id,
            objectLabel: this.currentNode.modelLabel,
            timeRanges: this.currentNode.effTimeList
          }
        ];
      }
      customApi.queryComprehensiveCost(params).then(res => {
        if (res.code === 0) {
          const mainData = res.data[0].data[0] || {};
          // 总用能成本
          this.totalCost = {
            totalValue: mainData.value,
            unit: res.data[0].unitName
          };
          // 同比
          const tbRate = Math.abs(mainData.yearOnYear);
          if (tbRate > 1) {
            this.CetChart_avgcost1.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost1.options.series[0].data = [
              tbRate,
              1 - tbRate
            ];
          }
          this.tbLabel = {
            price:
              this.formatNumberWithPrecision(mainData.yearOnYearCost, 2) ||
              "--",
            percent:
              mainData.yearOnYear || mainData.yearOnYear === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(mainData.yearOnYear) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              mainData.yearOnYear > 0
                ? require("./assets/arrow_up.png")
                : mainData.yearOnYear < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data[0].unitName
          };

          // 环比
          const hbRate = Math.abs(mainData.chain);
          if (hbRate > 1) {
            this.CetChart_avgcost2.options.series[0].data = [1];
          } else {
            this.CetChart_avgcost2.options.series[0].data = [
              hbRate,
              1 - hbRate
            ];
          }
          this.hbLabel = {
            price:
              this.formatNumberWithPrecision(mainData.chainCost, 2) || "--",
            percent:
              mainData.chain || mainData.chain === 0
                ? this.formatNumberWithPrecision(
                    Math.abs(mainData.chain) * 100,
                    2
                  ) + "%"
                : "--",
            src:
              mainData.chain > 0
                ? require("./assets/arrow_up.png")
                : mainData.chain < 0
                ? require("./assets/arrow_down.png")
                : "",
            unit: res.data[0].unitName
          };
        }
      });
    },
    // 分类成本占比
    getClassifyCostPercent() {
      const energyTypes = this.energyList.map(item => item.energytype);
      const data = {
        modelLabel: this.currentNode.modelLabel,
        ids: [this.currentNode.id],
        energyTypes,
        dimTagIds: null,
        ...this.queryTime,
        dimConfigId: this.dimConfigId
      };
      if (this.currentNode?.effTimeList?.[0]?.startTime) {
        data.timeRanges = [
          {
            objectId: this.currentNode.id,
            objectLabel: this.currentNode.modelLabel,
            timeRanges: this.currentNode.effTimeList
          }
        ];
      }
      this.CetChart_energyCost.options.series[0].data = [];
      customApi.queryEnergyCostValue(data).then(res => {
        if (res.code === 0) {
          let seriesData = [];
          let resData = res.data || [];
          resData.forEach(energyItem => {
            const target = this.energyList.find(
              item => item.energytype === energyItem.energytype
            );
            if (energyItem.value !== null) {
              seriesData.push({
                value: this.formatNumberWithPrecision(energyItem.value, 2),
                name: target && target.name,
                energyId: energyItem.energytype,
                unit: energyItem.unitName
              });
            }
          });
          this.CetChart_energyCost.options.series[0].data =
            this._.cloneDeep(seriesData);
        }
      });
    },
    // 区域成本占比
    getAreaCost() {
      let nodes = [],
        timeRanges = [];
      if (
        this.currentNode.children &&
        Array.isArray(this.currentNode.children)
      ) {
        nodes = this.currentNode.children.map(item => {
          if (item?.effTimeList?.[0]?.startTime) {
            timeRanges.push({
              objectId: item.id,
              objectLabel: item.modelLabel,
              timeRanges: item.effTimeList
            });
          }
          return {
            id: item.id,
            modelLabel: item.modelLabel,
            name: item.name
          };
        });
      } else {
        // 没有子节点时显示本身节点100%
        nodes = [
          {
            id: this.currentNode.id,
            modelLabel: this.currentNode.modelLabel,
            name: this.currentNode.name
          }
        ];

        if (this.currentNode?.effTimeList?.[0]?.startTime) {
          timeRanges.push({
            objectId: this.currentNode.id,
            objectLabel: this.currentNode.modelLabel,
            timeRanges: this.currentNode.effTimeList
          });
        }
      }
      const params = {
        nodes,
        energyType: 13,
        queryType:
          this.queryTime.cycle === 17 ? 1 : this.queryTime.cycle === 14 ? 3 : 0,
        costKpiType: 0,
        projectId: this.projectId,
        ...this.queryTime,
        dimConfigId: this.dimConfigId
      };
      if (timeRanges?.length) {
        params.timeRanges = timeRanges;
      }
      this.CetChart_areaCost.options.series[0].data = [];
      customApi.queryComprehensiveCost(params).then(res => {
        if (res.code === 0) {
          let seriesData = [];
          let resData = res.data || [];
          resData.forEach(item => {
            const itemData = item.data[0] || {};
            if (itemData.value !== null) {
              seriesData.push({
                value: this.formatNumberWithPrecision(itemData.value, 2),
                name: item.objectName,
                unit: item.unitName
              });
            }
          });
          this.CetChart_areaCost.options.series[0].data =
            this._.cloneDeep(seriesData);
        }
      });
    }
  },
  mounted() {
    this.getProjectEnergy();
  }
};
</script>

<style lang="scss" scoped>
.page {
  .chartBox {
    min-height: 300px;
  }
  .proportion {
    @include margin_top(J3);
    .proportion-item {
      position: relative;
      .cost-item {
        text-align: center;
        height: 28px;
        line-height: 28px;
        @include font_color(T5);
        @include background(BG2);
        span {
          font-size: 16px;
        }
      }
    }
    .avg-cost {
      padding-bottom: 0;
      .compare {
        .tip {
          position: absolute;
          top: 0;
          left: 10px;
          z-index: 99;
          padding: 2px 20px 2px 2px;
        }
        .preText {
          @include font_color(T5);
          @include background(BG2);
        }
        .lastText {
          @include font_color(T5);
          @include background(BG2);
        }
        .label {
          width: 60px;
          height: 50px;
          position: absolute;
          text-align: center;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          .percent {
            span {
              display: inline-block;
              max-width: calc(100% - 20px);
            }
            img {
              width: 14px;
            }
          }
        }
      }
    }
    .classify {
      .itemBox-empty {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      }
    }
  }
}
</style>
