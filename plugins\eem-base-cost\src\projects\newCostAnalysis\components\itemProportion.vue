<template>
  <div class="fullfilled flex flex-col" :class="{ light: isLight }">
    <div class="title text-H3 font-bold mb-J3">{{ $T("构成项占比") }}</div>
    <template v-if="!showEmpty">
      <div class="totalData flex-row flex pl-J3 pr-J3">
        <div class="iconBox">
          <div class="icon"></div>
        </div>
        <div class="text ml-J1 text-bottom">{{ $T("总用能成本") }}</div>

        <el-tooltip :content="total">
          <div class="flex-auto value ml-J1 text-ellipsis">
            {{ total }}
          </div>
        </el-tooltip>
        <div class="unit ml-J1 text-bottom">{{ unit }}</div>
      </div>
      <div class="flex-auto mt-J3">
        <CetChart v-bind="CetChart_1"></CetChart>
      </div>
    </template>
    <el-empty
      class="flex-auto"
      v-else-if="isLight"
      :image-size="216"
      image="static/assets/empty_min_light.png"
    ></el-empty>
    <el-empty
      v-else
      class="flex-auto"
      :image-size="216"
      image="static/assets/empty_min.png"
    ></el-empty>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import * as echarts from "echarts";
import customApi from "@/api/custom";
export default {
  props: {
    params: Object
  },
  computed: {
    isLight() {
      return omegaTheme.theme === "light";
    }
  },
  data() {
    return {
      showEmpty: true,
      total: null,
      unit: "--",
      CetChart_1: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "item",
            formatter: ({ name, data }) => {
              const { radio, realValue } = data;
              return `${name}<br/>
                ${$T("成本值")}：${this.valueFormat(realValue)} ${
                this.unit
              }<br/>
                ${$T("占比")}：${this.valueFormat(radio)}%`;
            }
          },
          legend: {
            type: "scroll",
            top: "center",
            right: 20,
            orient: "vertical",
            icon: "circle",
            textStyle: {
              rich: {
                name: {
                  width: 80,
                  fontSize: 12
                },
                radio: {
                  width: 40,
                  fontSize: 12
                }
              }
            },
            formatter: name => {
              const seriesData = this.CetChart_1.options.series[0].data;
              const data = seriesData.find(i => i.name === name);
              const nameStr = `{name|${echarts.format.truncateText(
                name,
                80,
                "12px Microsoft Yahei",
                "…"
              )}}`;
              const radio = `{radio|${echarts.format.truncateText(
                `${this.valueFormat(data.radio)}%`,
                50,
                "12px Microsoft Yahei",
                "…"
              )}}`;
              return nameStr + radio;
            },
            tooltip: {
              show: true,
              formatter: ({ name }) => {
                const seriesData = this.CetChart_1.options.series[0].data;
                const data = seriesData.find(i => i.name === name);
                return `${name}<br/>
                ${$T("成本值")}：${this.valueFormat(data.realValue)} ${
                  this.unit
                }<br/>
                ${$T("占比")}：${this.valueFormat(data.radio)}%`;
              }
            }
          },
          series: [
            {
              type: "pie",
              radius: ["60%", "80%"],
              center: ["25%", "50%"],
              avoidLabelOverlap: false,
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: false
                }
              },
              labelLine: {
                show: false
              },
              data: []
            }
          ]
        }
      }
    };
  },
  watch: {
    params: {
      handler() {
        this.getData();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getData() {
      this.showEmpty = true;
      if (!this.params) return;
      const res = await customApi.realtimeCostCheckItemCost(this.params);
      this.unit = res?.data?.[0]?.unitName ?? "--";
      const dataList = res?.data ?? [];
      let total = null;

      dataList.forEach(item => {
        if (item.value != null) {
          total += item.value;
        }
      });
      this.showEmpty = !dataList?.length;
      const seriesData = [];
      dataList.forEach(item => {
        let radio = null;
        if (total != null && item.value != null) {
          radio = (item.value / total) * 100;
        }
        seriesData.push({
          value: item.value > 0 ? item.value : 0,
          realValue: item.value,
          radio: radio,
          name: item.costCheckItemName
        });
      });
      this.total = this.valueFormat(total);
      this.CetChart_1.options.series[0].data = seriesData;
    },
    valueFormat(val) {
      if (val == null) return "--";
      return common.formatNum(val.toFixed(2));
    }
  }
};
</script>

<style lang="scss" scoped>
.totalData {
  height: 72px;
  box-sizing: border-box;
  background-image: url("../assets/overviewBG.png");
  background-size: 100% 100%;
  border-radius: 12px 12px 12px 12px;
  .iconBox {
    width: 24px;
    position: relative;
    .icon {
      height: 24px;
      width: 24px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-image: url("../assets/overviewIcon.png");
      background-size: 100% 100%;
    }
  }
  .text {
    font-size: 16px;
    margin-bottom: 24px;
  }
  .value {
    text-align: right;
    font-family: Barlow, Barlow;
    font-weight: bold;
    font-size: 28px;
    margin-top: 19px;
  }
  .unit {
    font-size: 16px;
    margin-bottom: 24px;
  }
}
.light {
  .totalData {
    background-image: url("../assets/overviewBG_light.png");
    .iconBox .icon {
      background-image: url("../assets/overviewIcon_light.png");
    }
  }
}
.text-bottom {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
</style>
