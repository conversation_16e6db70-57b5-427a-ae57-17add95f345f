<template>
  <div class="fullfilled flex flex-col">
    <div class="title text-H3 font-bold">{{ $T("构成项趋势") }}</div>
    <CetChart class="flex-auto" v-bind="CetChart_1"></CetChart>
  </div>
</template>

<script>
import { themeMap } from "cet-chart";
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
export default {
  props: {
    params: Object
  },
  data() {
    return {
      CetChart_1: {
        inputData_in: null,
        options: {
          tooltip: {
            trigger: "axis",
            formatter: list => {
              const name = list[0]?.name;
              return (
                `${name}</br>` +
                list
                  .map(item => {
                    return `${item.marker} ${
                      item.seriesName
                    } ${this.valueFormat(item.data.actualValue)}${
                      item.data.unit
                    }</br>`;
                  })
                  .join("")
              );
            },
            position: function (point, params, dom, rect, size) {
              // 计算 tooltip 应该出现的位置
              var x = point[0];
              var y = point[1];
              var boxWidth = size.contentSize[0];
              var boxHeight = size.contentSize[1];
              if (y - boxHeight > -40) {
                y = y - boxHeight;
              }
              if (boxWidth + x > size.viewSize[0]) {
                x = x - boxWidth;
              }
              return [x, y];
            }
          },
          legend: {
            lineStyle: {
              width: 2
            },
            width: "80%"
          },
          xAxis: [
            {
              type: "category",
              data: []
            }
          ],
          yAxis: [
            {
              type: "value",
              name: "--",
              nameTextStyle: {
                align: "left"
              }
            }
          ],
          grid: {
            left: "0",
            right: "0",
            top: "40",
            bottom: "0",
            containLabel: true
          },
          series: []
        }
      }
    };
  },
  watch: {
    params: {
      handler() {
        this.getData();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getData() {
      if (!this.params) return;
      const res = await customApi.realtimeCostCheckItemTrend(this.params);
      const unit = res?.data?.[0].unitName ?? "--";

      const aggregationCycle = this.params?.aggregationCycle;
      const dateType = aggregationCycle == 17 ? "MM" : "DD";
      const timeData = res?.data?.[0].dataList ?? [];
      const xAxisData = [];
      timeData.forEach(item => {
        const xAxisLabel = this.$moment(item.logTime).format(dateType);
        xAxisData.push(xAxisLabel);
      });

      const currentTheme = omegaTheme.theme;
      const themeMapConfig = themeMap.get(currentTheme);
      let colorList = themeMapConfig.color;
      const seriesData = res?.data ?? [];
      const series = [];
      seriesData.forEach((item, index) => {
        const serie = {
          name: item.costCheckItemName,
          type: "line",
          smooth: false,
          showSymbol: false,
          data:
            item?.dataList?.map(i => {
              return {
                value: i.value || 0,
                actualValue: i.value,
                unit: unit
              };
            }) ?? [],
          stack: "Total",
          areaStyle: {
            color: common.hexToRgb(colorList[index], 0.3)
          },
          lineStyle: {
            width: 0
          }
        };
        series.push(serie);
      });
      if (series?.length) {
        series[series.length - 1].lineStyle.width = 2;
      }

      this.CetChart_1.options.yAxis[0].name = unit;
      this.CetChart_1.options.xAxis[0].data = xAxisData;
      this.CetChart_1.options.series = series;
      this.CetChart_1.options = this._.cloneDeep(this.CetChart_1.options);
    },
    valueFormat(val) {
      if (val == null) return "--";
      return common.formatNum(val.toFixed(2));
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
