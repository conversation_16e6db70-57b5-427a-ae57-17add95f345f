<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full">
        <CetTree
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        >
          <span
            class="el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="{
                color: filNodeColor(node)
              }"
            >
              {{ node.label }}
            </span>
          </span>
        </CetTree>
      </div>
    </template>
    <template #container>
      <div class="fullfilled flex-auto flex flex-col">
        <div class="searchBox flex-row flex mb-J3 justify-between items-center">
          <div class="flex-row flex">
            <customElSelect
              v-model="ElSelect_1.value"
              v-bind="ElSelect_1"
              v-on="ElSelect_1.event"
              :prefix_in="$T('分析类型')"
            >
              <ElOption
                v-for="item in ElOption_1.options_in"
                :key="item[ElOption_1.key]"
                :label="item[ElOption_1.label]"
                :value="item[ElOption_1.value]"
                :disabled="item[ElOption_1.disabled]"
              ></ElOption>
            </customElSelect>
            <el-radio-group
              v-model="dataType"
              @input="dataTypeChange"
              class="ml-J1"
            >
              <el-radio-button :label="14">{{ $T("月") }}</el-radio-button>
              <el-radio-button :label="17">{{ $T("年") }}</el-radio-button>
            </el-radio-group>
            <el-button
              class="ml-J0"
              v-show="CetButton_prv.visible_in"
              :disabled="CetButton_prv.disable_in"
              v-bind="CetButton_prv.config"
              @click="CetButton_prv_statusTrigger_out"
            ></el-button>
            <el-date-picker
              class="datePicker ml-J0"
              v-bind="CetDatePicker_time.config"
              v-model="CetDatePicker_time.val"
              :type="datePickerType"
              :append-to-body="true"
              popper-class="datePopper"
              @change="dateChange"
              :picker-options="pickerOptions"
            ></el-date-picker>
            <el-button
              class="ml-J0"
              v-show="CetButton_next.visible_in"
              v-bind="CetButton_next.config"
              @click="CetButton_next_statusTrigger_out"
            ></el-button>
          </div>
          <CetButton
            class="ml-J1"
            v-bind="CetButton_download"
            v-on="CetButton_download.event"
          ></CetButton>
        </div>
        <div class="chartBox flex-auto rounded-Ra bg-BG1 mb-J3">
          <AllTrend v-if="ElSelect_1.value === 1" :params="params" />
          <ItemTrend v-else :params="params" />
        </div>
        <div class="cardList flex-row flex">
          <div class="mr-J3 rounded-Ra bg-BG1 overview">
            <AllOverview v-if="ElSelect_1.value === 1" :params="params" />
            <ItemProportion v-else :params="params" />
          </div>
          <div class="mr-J3 rounded-Ra bg-BG1 proportion">
            <Proportion :params="params" />
          </div>
          <div class="rounded-Ra bg-BG1 flex-auto">
            <Top5 :params="params" />
          </div>
        </div>
      </div>
    </template>
  </CetAside>
</template>

<script>
import customApi from "@/api/custom";
import AllTrend from "./components/allTrend.vue";
import AllOverview from "./components/allOverview.vue";
import ItemTrend from "./components/itemTrend.vue";
import ItemProportion from "./components/itemProportion.vue";
import Proportion from "./components/proportion.vue";
import Top5 from "./components/top5.vue";
import common from "eem-base/utils/common";
export default {
  name: "newCostAnalysis",
  components: {
    AllTrend,
    ItemTrend,
    AllOverview,
    ItemProportion,
    Proportion,
    Top5
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    datePickerType() {
      return this.showDataType === 14 ? "month" : "year";
    }
  },
  data() {
    return {
      params: null,
      currentNode: null,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300)
        }
      },
      ElSelect_1: {
        value: 1,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            name: $T("同环比分析")
          },
          {
            id: 2,
            name: $T("构成项分析")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      showDataType: 14,
      dataType: 14,
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-left"
        }
      },
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "",
          size: "small",
          icon: "el-icon-arrow-right"
        }
      },
      CetDatePicker_time: {
        val: Date.now(),
        config: {
          valueFormat: "timestamp",
          type: "year",
          rangeSeparator: "-",
          clearable: false,
          size: "small"
        }
      },
      CetButton_download: {
        visible_in: true,
        disable_in: false,
        title: $T("导出"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_download_statusTrigger_out
        }
      },
      pickerOptions: {
        // 添加回到今天的快捷键
        shortcuts: [
          {
            text: $T("当月"),
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          }
        ]
      }
    };
  },
  watch: {
    dataType: {
      handler(val) {
        this.pickerOptions.shortcuts[0].text =
          val === 17 ? $T("今年") : $T("当月");
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async init() {
      this.dataType = 14;
      this.showDataType = 14;
      this.ElSelect_1.value = 1;
      this.CetTree_1.searchText_in = "";
      this.CetDatePicker_time.val = Date.now();
      this.getTreeData();
    },
    async getTreeData() {
      const params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: null,
                  operator: "EQ",
                  prop: "roomtype",
                  tagid: 1
                }
              ]
            },
            modelLabel: "room"
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "airconditioner" }
        ],
        treeReturnEnable: true,
        // 按照3.5迭代骆海瑞要求添加该字段进行处理权限只返回一个项目节点的情况
        filterNoAuthEndNode: true
      };
      const res = await customApi.electricityCostValueNodeTreeAll(params);
      if (res.code !== 0) return;
      this.CetTree_1.inputData_in = res.data ?? [];
      // 选中第一个有数据 childSelectState = 1 的节点并展开节点
      const obj = this._.find(this.dataTransform(res.data), {
        childSelectState: 1,
        correlationState: 1
      });
      this.CetTree_1.selectNode = obj;
      this.currentNode = obj;
    },
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    CetTree_1_currentNode_out: _.debounce(function (val) {
      if (!val) return;
      if (val.childSelectState === 2 && val.correlationState === 2) {
        return this.$message.warning(
          $T("没有该节点权限，该节点没有关联当前方案")
        );
      } else if (val.childSelectState === 1 && val.correlationState === 2) {
        return this.$message.warning($T("当前节点未关联成本核算方案！"));
      } else if (val.childSelectState === 2 && val.correlationState === 1) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.currentNode = this._.cloneDeep(val);
      this.getData();
    }),
    ElSelect_1_change_out() {
      this.getData();
    },
    CetButton_prv_statusTrigger_out() {
      const time = this.CetDatePicker_time.val;
      const type = this.dataType === 14 ? "month" : "year";
      this.CetDatePicker_time.val = this.$moment(time).add(type, -1).valueOf();
      this.getData();
    },
    CetButton_next_statusTrigger_out() {
      const time = this.CetDatePicker_time.val;
      const type = this.dataType === 14 ? "month" : "year";
      this.CetDatePicker_time.val = this.$moment(time).add(type, 1).valueOf();
      this.getData();
    },
    dateChange() {
      this.getData();
    },
    dataTypeChange(val) {
      // 在时间选择打开情况下，直接切换周期，会导致下次打开时间选择框错位
      setTimeout(() => {
        this.showDataType = val;
        this.getData();
      }, 500);
    },
    getData() {
      if (!this.currentNode?.id) return;
      this.params = this.getParams();
    },
    CetButton_download_statusTrigger_out() {
      const params = this.getParams();
      const url =
        this.ElSelect_1.value === 1
          ? "/eem-service/v1/realtime-cost/cost-radio-trend/export"
          : "/eem-service/v1/realtime-cost/check-item/cost/trend/export";
      common.downExcel(url, params, this.token, this.projectId);
    },
    getParams() {
      const type = this.dataType === 14 ? "month" : "year";
      const time = this.$moment(this.CetDatePicker_time.val);
      let nodes = [];
      if (this.currentNode?.children?.length) {
        nodes = this.currentNode.children.map(i => {
          return {
            id: i.id,
            modelLabel: i.modelLabel
          };
        });
      }
      return {
        startTime: +time.startOf(type),
        endTime: time.endOf(type) + 1,
        aggregationCycle: this.dataType,
        node: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        },
        nodes,
        projectId: this.projectId
      };
    },
    //通过节点childSelectState属性，调整字体颜色
    filNodeColor(node) {
      const childSelectState = this._.get(node, "data.childSelectState", null);
      const correlationState = this._.get(node, "data.correlationState", null);
      if (childSelectState === 2 || correlationState === 2) {
        return "#989898";
      }
    }
  },
  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  padding: 0;
}
.searchBox {
  .datePicker {
    width: 200px;
    :deep(.datePopper) {
      position: absolute;
      z-index: 1;
    }
  }
}
.chartBox {
  box-sizing: border-box;
  @include padding(J4);
}
.cardList {
  height: 370px;
  & > div {
    box-sizing: border-box;
    @include padding(J4);
  }
  .overview,
  .proportion {
    width: 420px;
  }
}
</style>
