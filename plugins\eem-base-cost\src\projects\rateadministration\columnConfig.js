import moment from "moment";
import _ from "lodash";
import omegaI18n from "@omega/i18n";

// 容量费率
export const VOLUME = [
  {
    label: $T("生效时间"),
    prop: "effectivedate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      return moment(val.effectivedate).format("YYYY-MM");
    }
  },
  {
    label: $T("费率（元/kVA）"),
    prop: "feerate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      if (_.get(val, prop) || _.get(val, prop) === 0) {
        return _.get(val, prop);
      } else {
        return "--";
      }
    }
  },
  {
    type: "tag",
    label: $T("当前状态"),
    prop: "status$text",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left"
  }
];

// 需量计费
export const DEMAND = [
  {
    label: $T("生效时间"),
    prop: "effectivedate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      return moment(val.effectivedate).format("YYYY-MM");
    }
  },
  {
    label: $T("费率（元/kW）"),
    prop: "feerate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      if (_.get(val, prop) || _.get(val, prop) === 0) {
        return _.get(val, prop);
      } else {
        return "--";
      }
    }
  },
  {
    label: $T("上限比例") + `${$T("（{0}）", "%")}`,
    prop: "highlimit",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      if (_.get(val, prop) || _.get(val, prop) === 0) {
        return _.get(val, prop);
      } else {
        return "--";
      }
    }
  },
  {
    label: $T("惩罚系数"),
    prop: "punishrate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      if (_.get(val, prop) || _.get(val, prop) === 0) {
        return _.get(val, prop);
      } else {
        return "--";
      }
    }
  },
  {
    label: $T("计算负偏差"),
    prop: "calculatedeviation",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      return _.get(val, prop) ? $T("是") : $T("否");
    }
  },
  {
    type: "tag",
    label: $T("当前状态"),
    prop: "status$text",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left"
  }
];
/**
 * 单一费率
 * @param {String} symbol 单位
 */
export const getSingle = symbol => {
  return [
    {
      label: $T("生效时间"),
      prop: "effectivedate",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        return moment(val.effectivedate).format("YYYY-MM");
      }
    },
    {
      label: `${$T("费率")}${$T("（{0}）", symbol)}`,
      prop: "feerate",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      type: "tag",
      label: $T("当前状态"),
      prop: "status$text",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left"
    }
  ];
};
/**
 * 分时费率
 * @param {Number} maxNum 最大分时时段数量
 * @param {String} symbol 单位
 */
export const getTimeSharing = (maxNum, symbol) => {
  const config = [
    {
      label: $T("时段方案"),
      prop: "dailyName",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      minWidth: 180,
      fixed: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      label: $T("生效时间"),
      prop: "effectivedate$text",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      minWidth: 200,
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    }
  ];

  for (let i = 0; i < maxNum; i++) {
    config.push({
      label: $T("时段"),
      prop: `data[${i}].identification`,
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      minWidth: 100,
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    });
    config.push({
      label: $T("时间"),
      prop: `data[${i}].timePeriod`,
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      minWidth: 140,
      formatter: (val, prop) => {
        if (_.get(val, prop) && _.get(val, prop).length > 0) {
          return _.get(val, prop).join(";");
        } else {
          return "--";
        }
      }
    });
    config.push({
      label: `${$T("费率")}${$T("（{0}）", symbol)}`,
      prop: `data[${i}].rate`,
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      width: 150,
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    });
  }
  config.push({
    type: "tag",
    label: $T("当前状态"),
    prop: "status$text",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    fixed: "right",
    minWidth: omegaI18n.locale === "en" ? 120 : 100
  });
};

/**
 * 阶梯费率
 * @param {String} symbol 单位
 */
export const getStairs = symbol => {
  return [
    {
      label: $T("生效时间"),
      prop: "effectivedate",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        return moment(val.effectivedate).format("YYYY-MM");
      }
    },
    {
      label: $T("第一阶梯用量"),
      prop: "stagefeeset_model[0].stagefeeset$text",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      label: $T("费率") + `${$T("（{0}）", symbol)}`,
      prop: "stagefeeset_model[0].rate",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      label: $T("第二阶梯用量"),
      prop: "stagefeeset_model[1].stagefeeset$text",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      label: `${$T("费率")}${$T("（{0}）", symbol)}`,
      prop: "stagefeeset_model[1].rate",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      label: $T("第三阶梯用量"),
      prop: "stagefeeset_model[2].stagefeeset$text",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      label: `${$T("费率")}${$T("（{0}）", symbol)}`,
      prop: "stagefeeset_model[2].rate",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left",
      formatter: (val, prop) => {
        if (_.get(val, prop) || _.get(val, prop) === 0) {
          return _.get(val, prop);
        } else {
          return "--";
        }
      }
    },
    {
      type: "tag",
      label: $T("当前状态"),
      prop: "status$text",
      showOverflowTooltip: true,
      headerAlign: "left",
      align: "left"
    }
  ];
};

// 力调电费
export const ADJUST = [
  {
    label: $T("生效时间"),
    prop: "effectivedate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      return moment(val.effectivedate).format("YYYY-MM");
    }
  },
  {
    label: $T("力调费率考核标准"),
    prop: "name",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      if (_.get(val, prop) || _.get(val, prop) === 0) {
        return _.get(val, prop);
      } else {
        return "--";
      }
    }
  },
  {
    type: "tag",
    label: $T("当前状态"),
    prop: "status$text",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left"
  }
];

/**
 * 附加费
 * @param {String} symbol 单位
 * @returns
 */
export const getAdditional = symbol => [
  {
    label: $T("生效时间"),
    prop: "effectivedate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      return moment(val.effectivedate).format("YYYY-MM");
    }
  },
  {
    label: `${$T("费率")}${$T("（{0}）", symbol)}`,
    prop: "feerate",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left",
    formatter: (val, prop) => {
      if (_.get(val, prop) || _.get(val, prop) === 0) {
        return _.get(val, prop);
      } else {
        return "--";
      }
    }
  },
  {
    type: "tag",
    label: $T("当前状态"),
    prop: "status$text",
    showOverflowTooltip: true,
    headerAlign: "left",
    align: "left"
  }
];
