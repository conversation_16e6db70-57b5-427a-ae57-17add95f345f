<template>
  <div class="fullheight">
    <div class="fullheight flex flex-row">
      <div
        class="fullheight flex-col flex w-[420px] bg-BG1 rounded-Ra p-J4 box-border"
      >
        <div class="mb-J3 flex flex-row justify-between">
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            :prefix_in="$T('能源类型')"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
          <CetButton
            class="fr ml-J1"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
            v-permission="'feemanage_edit'"
          ></CetButton>
        </div>
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_energytype$text"></ElTableColumn>
          <ElTableColumn
            v-bind="ElTableColumn_feeratetype$text"
          ></ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_allowDelete$text">
            <template slot-scope="{ row }">
              <span
                v-permission="'feemanage_edit'"
                class="clickformore delete"
                :class="{
                  clickdisable: !row.allowDelete
                }"
                @click.stop="handleDeleteRate(row)"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
      <div class="ml-J3 flex-auto flex-col flex">
        <div class="mb-J3 flex flex-row items-center justify-between">
          <span class="text-H1 font-bold">{{ $T("方案详情") }}</span>
          <CetButton
            class="fr"
            v-bind="CetButton_2"
            v-on="CetButton_2.event"
            v-permission="'feemanage_edit'"
          ></CetButton>
        </div>
        <currentRate
          :currentRate="currentRate"
          :currentTabel1Item="currentTabel1Item"
        />
        <div class="flex-auto mt-J3 flex flex-col">
          <div class="mb-J3 text-H2 font-bold">{{ $T("调整历史") }}</div>
          <CetTable
            class="flex-auto bg-BG1 rounded-Ra p-J4 box-border"
            :data.sync="CetTable_2.data"
            :dynamicInput.sync="CetTable_2.dynamicInput"
            v-bind="CetTable_2"
            v-on="CetTable_2.event"
          >
            <template>
              <ElTableColumn
                v-for="(item, index) in ElTableColumnArr"
                :key="index"
                v-bind="item"
              >
                <template slot-scope="scope">
                  <el-tag
                    v-if="item.type === 'tag'"
                    size="small"
                    style="color: #fff"
                    :type="statusTagTypeFormatter(scope.row.status)"
                  >
                    {{ scope.row.status$text || "--" }}
                  </el-tag>
                  <span v-else>
                    {{
                      item.formatter
                        ? item.formatter(scope.row, item.prop)
                        : scope.row[item.prop]
                    }}
                  </span>
                </template>
              </ElTableColumn>
            </template>
            <ElTableColumn
              v-bind="ElTableColumn_edit"
              :width="en ? 140 : 130"
              v-if="CetTable_2.showEdit"
            >
              <template slot-scope="scope">
                <!-- 力调电费且当前状态为已过期时显示查看 -->
                <span
                  class="clickformore mr-J1"
                  v-if="
                    currentTabel1Item.feeratetype !== 3 &&
                    scope.row.status === 3
                  "
                  @click.stop="handleDetail(scope.row)"
                >
                  {{ $T("查看") }}
                </span>
                <span
                  v-else
                  class="fl mr-J3 clickformore"
                  :class="{
                    clickdisable: scope.row.status === 3
                  }"
                  @click.stop="handleEdit(scope.row)"
                  v-permission="'feemanage_edit'"
                >
                  {{ $T("编辑") }}
                </span>
                <span
                  class="fl mr-J3 clickformore delete"
                  :class="{
                    clickdisable: scope.row.status !== 2
                  }"
                  @click.stop="handleDelete(scope.row)"
                  v-permission="'feemanage_edit'"
                >
                  {{ $T("删除") }}
                </span>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>
      </div>
    </div>
    <AddRateScheme
      :visibleTrigger_in="AddRateScheme.visibleTrigger_in"
      :closeTrigger_in="AddRateScheme.closeTrigger_in"
      :queryId_in="AddRateScheme.queryId_in"
      :disableDate="AddRateScheme.disableDate"
      :detailVisible="AddRateScheme.detailVisible"
      :inputData_in="AddRateScheme.inputData_in"
      :dateArr="AddRateScheme.dateArr"
      :tableData="tableData"
      @finishTrigger_out="AddRateScheme_finishTrigger_out"
      :energyList_in="ElOption_1.options_in"
    />
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import currentRate from "./rateManage/currentRate.vue";
import AddRateScheme from "./rateManage/AddRateScheme.vue";
import customApi from "@/api/custom";
import omegaI18n from "@omega/i18n";
import {
  VOLUME,
  DEMAND,
  getSingle,
  getTimeSharing,
  getStairs,
  ADJUST,
  getAdditional
} from "./columnConfig.js";
const STATUS_MAP = {
  1: $T("生效中"),
  2: $T("未生效"),
  3: $T("已过期")
};
export default {
  name: "RateManage",
  components: {
    currentRate,
    AddRateScheme
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    en() {
      return omegaI18n.locale === "en";
    }
  },
  data() {
    return {
      timeShareId: null, // 分时费率关联的分时方案id
      currentTabel1Item: null,
      currentTabel1Item2: null,
      currentRate: [], // 展示费率
      actionCurrentRate: null, //当前生效的费率
      ElSelect_1: {
        value: null,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 0,
            text: $T("全部")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("费率方案"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol()
      },
      ElTableColumn_energytype$text: {
        prop: "energyTypeName", // 支持path a[0].b
        label: $T("能源类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol(),
        width: "90" //绝对宽度
      },
      ElTableColumn_feeratetype$text: {
        prop: "feerateTypeName", // 支持path a[0].b
        label: $T("费用类型"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: common.formatTextCol()
      },
      ElTableColumn_allowDelete$text: {
        prop: "allowDelete$text", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: "80" //绝对宽度
      },
      CetButton_1: {
        visible_in: true,
        disable_in: true,
        title: $T("新增费率方案"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetTable_2: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        event: {
          record_out: this.CetTable_2_record_out
        },
        showEdit: true
      },
      ElTableColumn_edit: {
        prop: "", // 支持path a[0].b
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },
      ElTableColumnArr: [{}],
      AddRateScheme: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        disableDate: false,
        detailVisible: false,
        dateArr: []
      },
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("费率调整"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      tableData: []
    };
  },

  methods: {
    /**
     * 获取能源类型
     */
    async getEnergytype() {
      const response = await customApi.getProjectEnergy(this.projectId);
      if (response.code !== 0) {
        return;
      }
      const data = response.data || [];
      this.CetButton_1.disable_in = !data.length;

      if (data.length === 0) {
        this.ElOption_1.options_in = null;
        return;
      }

      const filterEnergyType = await this.getFilterEnergyType();

      const energyList = response.data.filter(
        item => !filterEnergyType.includes(item.energytype)
      );

      const selectData = energyList.map(item => {
        return {
          id: item.energytype,
          text: item.name,
          symbol: common.formatSymbol(item.symbol) || "--"
        };
      });

      selectData.unshift({
        id: 0,
        text: $T("全部")
      });

      this.ElOption_1.options_in = selectData;
      this.ElSelect_1.value = 0;
      this.getTabelData();
    },
    /**
     * 获取需要过滤能源类型
     */
    async getFilterEnergyType() {
      const res = customApi.organizationConfigFilterEnergyType();
      return res.data || [];
    },
    ElSelect_1_change_out() {
      this.getTabelData();
    },
    // 查询计费方案
    async getTabelData() {
      if (this.ElSelect_1.value === null) {
        return;
      }

      const params = { energyType: this.ElSelect_1.value };
      const response = await customApi.getSchemeConfigFeeScheme(
        this.projectId,
        params
      );
      if (response.code !== 0) {
        this.CetTable_1.data = [];
        this.renderTable();
        return;
      }

      const resData = response.data || [];
      this.CetTable_1.data = resData;
    },
    /**
     * 费率方案的选择输出
     * @param val 当前选择的费率方案
     */
    CetTable_1_record_out(val) {
      this.CetButton_2.disable_in = val.id === -1;

      if (val.id === -1) {
        // 重置
        this.CetTable_2.data = [];
        this.currentRate = [];
        this.currentTabel1Item = null;
        return;
      }

      this.currentTabel1Item = val;
      // 判断费率类型
      if (
        (val.feeratesubtype === 2 && val.feeratetype === 2) ||
        (val.feeratesubtype === 2 && val.feeratetype === 4)
      ) {
        // 分时费率
        this.getTimeFeeSchemeDetail();
      } else {
        // 非分时费率
        this.getFeeSchemeDetail();
      }
    },
    /**
     * 查询分时费率记录
     */
    async getTimeFeeSchemeDetail() {
      this.CetTable_2.data = [];
      this.currentRate = [];
      this.timeShareId = null;
      const { id, energytype } = this.currentTabel1Item;
      const response = await customApi.getSchemeConfigFeeRecordTimeShare(
        id,
        energytype
      );

      if (response.code !== 0) {
        return;
      }
      this.renderTable(response.data);
      this.timeShareId = response.data.timeShareId;
    },
    /**
     * 查询费率记录，不支持分时费率记录的查询
     */
    async getFeeSchemeDetail() {
      this.CetTable_2.data = [];
      this.currentRate = [];
      let feeRecordType = "";
      /*
      // 容量费率 volumefeerecord
      // 需量费率 demandfeerecord
      // 单一费率 singlefeerecord
      // 阶梯费率 stagefeerecord
      // 力调费率 powertariff
      // 附加费 surchargefeerecord
      */

      const { feeratetype, feeratesubtype, energytype, id } =
        this.currentTabel1Item;
      if (energytype === 2 && feeratetype === 1 && feeratesubtype === 1) {
        // 容量费率 volumefeerecord
        feeRecordType = "volumefeerecord";
      } else if (
        // 需量费率 demandfeerecord
        energytype === 2 &&
        feeratetype === 1 &&
        feeratesubtype === 2
      ) {
        feeRecordType = "demandfeerecord";
      } else if (feeratetype === 2 && feeratesubtype === 1) {
        // 单一费率 singlefeerecord
        feeRecordType = "singlefeerecord";
      } else if (feeratetype === 2 && feeratesubtype === 3) {
        // 阶梯费率 stagefeerecord
        feeRecordType = "stagefeerecord";
      } else if (feeratetype === 3) {
        // 力调费率 powertariff
        feeRecordType = "powertariff";
      } else if (feeratetype === 4 && feeratesubtype === 1) {
        // 附加费  单一费率 singlefeerecord
        feeRecordType = "singlefeerecord";
      } else if (feeratetype === 4 && feeratesubtype === 3) {
        // 附加费  单一费率 stagefeerecord
        feeRecordType = "stagefeerecord";
      }

      if (["volumefeerecord", "demandfeerecord"].includes(feeRecordType)) {
        const response = await customApi.demandMaintaingGetFeeSchemeItem({
          id
        });
        if (response.code !== 0) {
          return;
        }

        this.renderTable(response.data[feeRecordType + "_model"]);
        return;
      }

      const response = await customApi.schemeConfigFeeRecord(feeRecordType, id);
      if (response.code !== 0) {
        return;
      }

      this.renderTable(response.data);
    },
    // 渲染表格
    renderTable(tabData) {
      this.actionCurrentRate = null;
      this.CetTable_2.data = [];
      this.currentRate = [];
      this.ElTableColumnArr = [{}];

      if (!tabData || !this.currentTabel1Item) {
        return;
      }
      const { feeratetype, feeratesubtype, energytype } =
        this.currentTabel1Item;
      if (
        (!tabData.length || tabData.length === 0) &&
        feeratetype !== 2 &&
        feeratesubtype !== 2
      ) {
        return;
      }

      let action = false;
      // 解析字段
      // 单独处理分时
      if (
        (feeratetype === 2 && feeratesubtype === 2) ||
        (feeratetype === 4 && feeratesubtype === 2)
      ) {
        this.CetTable_2.showEdit = false;
        const dataList = tabData.data || [];
        // 列表生效时间按倒序排序
        dataList.sort((a, b) => {
          if (a.endTime < b.startTime) return 1;
          return -1;
        });

        dataList.forEach(item => {
          if (
            item.startTime <= +this.$moment().endOf("day") &&
            item.endTime >= +this.$moment().startOf("day")
          ) {
            if (!action) {
              item.status = 1;
              item.status$text = STATUS_MAP[1];
              // 保存生效费率
              this.actionCurrentRate = item;
              action = true;
            }
          } else if (item.startTime > +this.$moment().endOf("day")) {
            item.status = 2;
            item.status$text = STATUS_MAP[2];
          } else if (item.endTime < +this.$moment().startOf("day")) {
            item.status = 3;
            item.status$text = STATUS_MAP[3];
          }

          item.effectivedate$text =
            this.$moment(item.startTime).format("YYYY-MM-DD") +
            " ~ " +
            this.$moment(item.endTime).format("YYYY-MM-DD");
        });
        this.CetTable_2.data = dataList;
      } else {
        this.CetTable_2.showEdit = true;

        // 列表生效时间按倒序排序
        tabData.sort((a, b) => b.effectivedate - a.effectivedate);

        tabData.forEach(item => {
          if (item.effectivedate <= +this.$moment().endOf("month")) {
            if (!action) {
              item.status = 1;
              item.status$text = STATUS_MAP[1];
              // 保存生效费率
              this.actionCurrentRate = item;
              action = true;
            } else {
              item.status = 3;
              item.status$text = STATUS_MAP[3];
            }
          } else {
            item.status = 2;
            item.status$text = STATUS_MAP[2];
          }
        });

        this.CetTable_2.data = tabData;
      }

      // 处理单位
      let symbol = $T("元") + "/--";
      if (this.ElOption_1.options_in.length > 0) {
        const selectEnergy = this.ElOption_1.options_in.find(
          item => item.id === energytype
        );
        symbol = $T("元") + "/" + selectEnergy?.symbol;
      }

      // 判断调整历史的表头
      if (energytype === 2 && feeratetype === 1 && feeratesubtype === 1) {
        // 容量费率
        this.ElTableColumnArr = VOLUME;
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: $T("元") + "/kVA"
            }
          ];
        }
      } else if (
        energytype === 2 &&
        feeratetype === 1 &&
        feeratesubtype === 2
      ) {
        // 需量计费
        this.ElTableColumnArr = DEMAND;
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: $T("元/kW")
            }
          ];
        }
      } else if (
        (feeratetype === 2 && feeratesubtype === 1) ||
        (feeratetype === 4 && feeratesubtype === 1)
      ) {
        // 单一费率
        // 处理单位
        if (this.CetTable_2.data.length > 0) {
          this.CetTable_2.data.forEach(item => {
            item.feerate = item.feerate / 100000;
          });
        }
        this.ElTableColumnArr = getSingle(symbol);
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: symbol
            }
          ];
        }
      } else if (
        (feeratetype === 2 && feeratesubtype === 2) ||
        (feeratetype === 4 && feeratesubtype === 2)
      ) {
        // 分时费率
        let maxNum = 0;
        this.CetTable_2.data.forEach(item => {
          if (item.data?.length > maxNum) {
            maxNum = item.data.length;
          }

          // 转单位
          const data = item.data || [];
          data.forEach(i => {
            if (i.rate || i.rate === 0) {
              i.rate = i.rate / 100000;
            }
          });
        });

        this.ElTableColumnArr = getTimeSharing(maxNum, symbol);

        const currentRateData = this.actionCurrentRate.data || [];
        this.currentRate = currentRateData.map(item => {
          return {
            Fee: item.rate || item.rate === 0 ? item.rate : "--",
            FeeText: symbol,
            bottomTitle: `${item.identification || "--"} ${$T("时段")}${$T(
              "费率"
            )}${$T("（{0}）", item.timePeriod || "--")}`
          };
        });
      } else if (
        (feeratetype === 2 && feeratesubtype === 3) ||
        (feeratetype === 4 && feeratesubtype === 3)
      ) {
        (this.CetTable_2.data || []).forEach(item => {
          const stagefeesetData = item.stagefeeset_model || [];
          stagefeesetData.forEach((ite, index) => {
            // 处理单位
            ite.rate = ite.rate / 100000;
            let stagefeeset$text = "";
            if (stagefeesetData[index + 1]) {
              stagefeeset$text = `${ite.lowlimit} ~ ${
                stagefeesetData[index + 1].lowlimit
              }`;
            } else {
              stagefeeset$text = `>${ite.lowlimit}`;
            }
            this.$set(ite, "stagefeeset$text", stagefeeset$text);
          });
        });
        // 阶梯费率
        this.ElTableColumnArr = getStairs(symbol);

        const stagefeesetData = this.actionCurrentRate?.stagefeeset_model ?? [];
        this.currentRate = stagefeesetData.map((item, index) => {
          const titleText = `${$T("第")}${
            index === 0
              ? $T("一")
              : index === 1
              ? $T("二")
              : index === 2
              ? $T("三")
              : ""
          }${$T("阶梯")}${$T("（{0}）", item.stagefeeset$text)}`;

          return {
            Fee: item.rate,
            FeeText: symbol,
            bottomTitle: titleText
          };
        });
      } else if (feeratetype === 3) {
        // 力调电费
        // 处理单位
        (this.CetTable_2.data || []).forEach(item => {
          if (item.powertarifffactor_model?.length) {
            item.powertarifffactor_model.forEach(ite => {
              // 处理单位
              ite.punishrate = Number((ite.punishrate * 100).toFixed2(2));
            });
          }
        });
        this.ElTableColumnArr = ADJUST;
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.name,
              bottomTitle: $T("力调费率考核标准")
            }
          ];
        }
      } else if (feeratetype === 4) {
        // 处理单位
        if (this.CetTable_2.data.length > 0) {
          this.CetTable_2.data.forEach(item => {
            item.feerate = item.feerate / 100000;
          });
        }
        // 附加费
        this.ElTableColumnArr = getAdditional(symbol);
        if (this.actionCurrentRate) {
          this.currentRate = [
            {
              Fee: this.actionCurrentRate.feerate,
              FeeText: symbol
            }
          ];
        }
      }

      let dateArr;
      if (tabData.length) {
        dateArr = tabData.map(item =>
          this.$moment(item.effectivedate).format("YYYY-MM")
        );
      }
      // 传入已有记录的时间
      this.AddRateScheme.dateArr = dateArr;
    },
    // 当前状态标签样式格式化
    statusTagTypeFormatter(cellValue) {
      return cellValue === 1
        ? "status1"
        : cellValue === 3
        ? "status2"
        : cellValue === 2
        ? "status3"
        : "primary";
    },
    CetButton_2_statusTrigger_out() {
      this.tableData = this._.cloneDeep(this.CetTable_2.data);
      this.setParams();
      this.AddRateScheme.disableDate = false;
      this.AddRateScheme.detailVisible = false;
    },
    /**
     * 聚合参数给新建、编辑框
     * @param val 目标调整记录，不传则是当前生效的记录，最后就是选第一条
     */
    setParams(val) {
      let inputDataObj = {};
      let actionCurrentRate =
        this.actionCurrentRate || this.CetTable_2.data[0] || {};
      if (val) {
        actionCurrentRate = val;
      }

      const { energytype, feeratetype, feeratesubtype } =
        this.currentTabel1Item;
      /*
      // 容量费率 volumefeerecord
      // 需量费率 demandfeerecord
      // 单一费率 singlefeerecord
      // 阶梯费率 stagefeerecord
      // 力调费率 powertariff
      // 附加费 surchargefeerecord
      */
      if (energytype === 2 && feeratetype === 1 && feeratesubtype === 1) {
        // 容量费率 volumefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          energytype: energytype, // 能源类型
          rateType: feeratesubtype, // 1: 容量计费 2： 需量计费
          volumefeerate: actionCurrentRate.feerate // 容量计费值
        };
      } else if (
        // 需量费率 demandfeerecord
        energytype === 2 &&
        feeratetype === 1 &&
        feeratesubtype === 2
      ) {
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          energytype: energytype, // 能源类型
          rateType: feeratesubtype, // 1: 容量计费 2： 需量计费
          demandfeerate: actionCurrentRate.feerate, // 需量计费值
          highLimit: actionCurrentRate.highlimit,
          punishRate: actionCurrentRate.punishrate,
          calculatedeviation: actionCurrentRate.calculatedeviation
        };
      } else if (
        (feeratetype === 2 && feeratesubtype === 1) ||
        (feeratetype === 4 && feeratesubtype === 1)
      ) {
        // 单一费率 singlefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          electricitychargerate: feeratesubtype,
          energytype: energytype, // 能源类型
          feerate: actionCurrentRate.feerate // 单一费率值
        };
      } else if (
        (feeratetype === 2 && feeratesubtype === 2) ||
        (feeratetype === 4 && feeratesubtype === 2)
      ) {
        // 分时费率
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          electricitychargerate: feeratesubtype,
          energytype: energytype, // 能源类型
          timeShareScheme_id: this.timeShareId //分时方案id
        };
      } else if (
        (feeratetype === 2 && feeratesubtype === 3) ||
        (feeratetype === 4 && feeratesubtype === 3)
      ) {
        // 阶梯费率 stagefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          electricitychargerate: feeratesubtype,
          energytype: energytype, // 能源类型
          stagefeerateTableData: []
        };
        var stagefeerateTableData = [];
        if (
          actionCurrentRate.stagefeeset_model &&
          actionCurrentRate.stagefeeset_model.length > 0
        ) {
          actionCurrentRate.stagefeeset_model.forEach((item, index) => {
            stagefeerateTableData.push({
              energyNum: item.lowlimit,
              name: item.name,
              rate: item.rate
            });
          });
        }
        inputDataObj.stagefeerateTableData = stagefeerateTableData;
      } else if (feeratetype === 3) {
        // 力调费率 powertariff
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          electricitychargerate: feeratesubtype,
          energytype: energytype, // 能源类型
          powertariff_model: actionCurrentRate
        };
      } else if (feeratetype === 4) {
        // 附加费 surchargefeerecord
        inputDataObj = {
          id: this.currentTabel1Item.id,
          name: this.currentTabel1Item.name,
          feeratetype: feeratetype, // 1: 基本电费 2： 电度电费 3 : 力调电费 4 :附加费
          effectivedate: actionCurrentRate.effectivedate, //生效时间
          energytype: energytype, // 能源类型
          feerate: actionCurrentRate.feerate, // 单一费率值
          fjfName: actionCurrentRate.name
        };
      }
      this.AddRateScheme.inputData_in = this._.cloneDeep(inputDataObj);
      this.AddRateScheme.visibleTrigger_in = new Date().getTime();
    },
    CetButton_1_statusTrigger_out(val) {
      this.tableData = [];
      this.AddRateScheme.inputData_in = {
        energytype: this.ElSelect_1.value || 2
      };
      this.AddRateScheme.disableDate = false;
      this.AddRateScheme.visibleTrigger_in = this._.cloneDeep(val);
    },
    handleDeleteRate(row) {
      if (!row.allowDelete) return;
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            const response = await customApi.deleteSchemeConfig(row.id);
            if (response.code !== 0) {
              return;
            }
            this.$message({
              message: $T("删除成功"),
              type: "success"
            });
            this.getTabelData();
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    CetTable_2_record_out(val) {
      this.currentTabel1Item2 = val.id !== -1 ? val : null;
    },
    handleDelete(row) {
      if (row.status !== 2) return;
      const { id, modelLabel } = row;
      this.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            const response = await customApi.deleteSchemeConfigFeeRecord(
              id,
              modelLabel
            );
            if (response.code !== 0) {
              return;
            }

            this.$message({
              message: $T("删除成功"),
              type: "success"
            });

            this.CetTable_1_record_out(this.currentTabel1Item);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: action => {
          if (action !== "confirm") {
            this.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    handleEdit(row) {
      if (row.status === 3) return;
      this.tableData = this._.cloneDeep(this.CetTable_2.data);
      this.setParams(row);
      this.AddRateScheme.disableDate = true;
      this.AddRateScheme.detailVisible = false;
    },
    // 力调已过期查看详情
    handleDetail(row) {
      this.tableData = this._.cloneDeep(this.CetTable_2.data);
      this.setParams(row);
      this.AddRateScheme.disableDate = true;
      this.AddRateScheme.detailVisible = true;
    },
    AddRateScheme_finishTrigger_out() {
      this.getTabelData();
    }
  },
  mounted() {
    this.getEnergytype();
  }
};
</script>
<style lang="scss" scoped>
.el-tag.el-tag--status3 {
  background-color: var(--Sta3);
  border-color: var(--Sta3);
  color: #fff;
}
.el-tag.el-tag--status2 {
  background-color: var(--Sta4);
  border-color: var(--Sta4);
  color: #fff;
}
.el-tag.el-tag--status1 {
  background-color: var(--Sta1);
  border-color: var(--Sta1);
  color: #fff;
}
.clickformore {
  cursor: pointer;
  @include font_color(ZS);
  &.delete {
    @include font_color(Sta3);
  }
}
.clickdisable,
.clickdisable.delete {
  @include font_color(T4);
  cursor: not-allowed !important;
}
</style>
