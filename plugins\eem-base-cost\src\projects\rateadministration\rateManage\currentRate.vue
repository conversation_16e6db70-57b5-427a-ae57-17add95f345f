<template>
  <div class="page1">
    <div class="currentRateBox">
      <div class="currentRateBox_title">{{ $T("当前生效费率") }}</div>
      <div
        :class="{ currentRateBox_item: true, energy: index === 0 }"
        v-for="(item, index) in currentRate"
        :key="index"
        style="display: flex; justify-content: center"
      >
        <div class="topIcon mr-J3 pl-J3" v-if="index === 0">
          <span
            :style="{
              background:
                currentTabel1Item && currentTabel1Item.energytype == 3
                  ? '#00CC33'
                  : '#0152D9'
            }"
          >
            {{ currentTabel1Item && currentTabel1Item.energyTypeName }}
          </span>
          <el-tooltip
            :content="currentTabel1Item && currentTabel1Item.name"
            effect="light"
          >
            <div class="text-ellipsis">
              {{ currentTabel1Item && currentTabel1Item.name }}
            </div>
          </el-tooltip>
        </div>
        <div class="feerate pl-J1 pr-J1">
          <el-tooltip
            :content="item.Fee + ' ' + (item.FeeText ? item.FeeText : '')"
            effect="light"
          >
            <div class="text-ellipsis">
              <span class="currentRate">{{ item.Fee }}</span>
              {{ item.FeeText }}
            </div>
          </el-tooltip>
          <el-tooltip effect="light" :content="item.bottomTitle">
            <div class="bottomTitle text-ellipsis">
              <span v-if="item.iconText" class="iconText">
                {{ item.iconText }}
              </span>
              <span>{{ item.bottomTitle }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "currentRate",
  components: {},
  props: {
    currentRate: {
      type: Array,
      default: () => []
    },
    currentTabel1Item: {
      type: Object
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      testData: [
        {
          Fee: 10,
          FeeText: $T("单位"),
          bottomTitle: $T("时段费率") + $T("（14：00~22：00)"),
          iconText: $T("峰")
        },
        {
          Fee: 10
        },
        {
          Fee: 10,
          FeeText: $T("单位")
        },
        {
          Fee: 10,
          FeeText: $T("单位"),
          bottomTitle: $T("容量费率")
        }
      ]
    };
  },
  watch: {
    currentRate: {
      handler: function () {
        // 监听传入的生效费率，展示对应的效果
      },
      deep: true
    }
  },

  methods: {
    //占位函数，无实际用处
    nothing() {}
  },

  created: function () {}
};
</script>
<style lang="scss" scoped>
.page1 {
  width: 100%;
  height: 240px;
  position: relative;
}
.currentRateBox {
  height: 240px;
  box-shadow: none;
  font-size: 16px;
  background: linear-gradient(
    rgb(52, 143, 218) 0%,
    rgb(52, 143, 218) 0%,
    rgb(27, 61, 199) 100%,
    rgb(27, 61, 199) 100%
  );
  border-width: initial;
  border-style: none;
  border-color: initial;
  border-image: initial;
  border-radius: 4px;
  position: relative;
  color: #fff;
  display: flex;
  text-align: center;
  & > .currentRateBox_title {
    border-radius: 4px;
    font-size: 16px;
    position: absolute;
    left: 0;
    top: 0;
    display: inline-block;
    padding: 5px 30px;
    background: linear-gradient(
      270deg,
      rgba(46, 122, 213, 1) 0%,
      rgba(3, 80, 218, 1) 100%
    );
    color: #fff;
  }
  .currentRate {
    font-size: 48px;
    margin-right: 5px;
  }
  .currentRateBox_item {
    width: 0;
    flex: 1;
    padding-top: 60px;
    .topIcon {
      font-size: 18px;
      line-height: 48px;
      width: 140px;
      box-sizing: border-box;
      & > span {
        width: 80px;
        text-align: center;
        font-size: 13px;
        line-height: 28px;
        display: inline-block;
        margin: 24px 0 0 0;
        border-radius: 5px;
        background: #0152d9;
        color: #fff;
      }
    }
    .feerate {
      width: 100%;
    }
  }
  .energy {
    flex: 1.5;
    .feerate {
      width: calc(100% - 140px);
    }
  }
  .bottomTitle {
    margin-top: 10px;
    .iconText {
      display: inline-block;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #fff;
      color: #000;
      position: relative;
      line-height: 40px;
      white-space: nowrap;
    }
  }
}
</style>
