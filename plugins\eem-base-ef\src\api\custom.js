//能管基础
import commonApi from "eem-base/api";
import * as energy from "./energy.js";
import * as efficiencyBenchmarkingManage from "./efficiencyBenchmarkingManage.js";
import * as tree from "./tree.js";
import * as energyEfficiencyEvent from "./energyEfficiencyEvent.js";
import * as user from "./user.js";
import * as efficiencyAlarmConfig from "./efficiencyAlarmConfig.js";
import * as config from "./config.js";
import * as energyEfficiencyIndexes from "./energyEfficiencyIndexes.js";

export default {
  ...commonApi,
  ...energy,
  ...efficiencyBenchmarkingManage,
  ...tree,
  ...energyEfficiencyEvent,
  ...user,
  ...efficiencyAlarmConfig,
  ...config,
  ...energyEfficiencyIndexes
};
