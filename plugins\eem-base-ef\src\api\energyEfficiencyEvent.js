import fetch from "eem-base/utils/fetch";

// 获取能效事件
export function queryEvents(data) {
  return fetch({
    url: `/eem-service/v1/alarmEvent/queryEvents`,
    method: "POST",
    data
  });
}

// 获取事件能效趋势曲线
export function eventAnalysis(data) {
  return fetch({
    url: `/eem-service/v1/alarmEvent/eventAnalysis`,
    method: "POST",
    data
  });
}

// 事件确认
export function confirmEvents(data) {
  return fetch({
    url: `/eem-service/v1/alarmEvent/confirmEvents`,
    method: "POST",
    data
  });
}
