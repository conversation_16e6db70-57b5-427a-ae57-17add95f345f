import fetch from "eem-base/utils/fetch";

// 能效配置维度配置查询
export function getEfNodeDimTreeConfig(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef-node/dim-tree-config`,
    method: "POST",
    data
  });
}

export function getNodeTreeSimple(data) {
  return fetch({
    url: `/eem-service/v1/node/nodeTree/simple`,
    method: "POST",
    data
  });
}

// 查询维度节点树数据
export function getAttributeDimensionTreeNodetree(data) {
  return fetch({
    url: `/eem-service/v1/attribute-dimension/tree/node-tree`,
    method: "POST",
    data
  });
}

// 查询维度节点树列表（包含id为-1的固定管理层级）
export function getAttributeDimensionTreeNodeConfig(data, params) {
  return fetch({
    url: `/eem-service/v1/attribute-dimension/tree/node-config`,
    method: "POST",
    data,
    params
  });
}

/**
 * 获取多维度节点树 可支持传入末端需要保留的节点
 * @param {*keepNodeTypes} array 传入末端保留的节点的modelLabel，默认不过滤
 * @returns
 */
export function dimensionTreeFilterByEnergytype(data) {
  return fetch({
    url: `/eem-service/v1/attribute-dimension/tree/filter-by-energytype`,
    method: "POST",
    data
  });
}

// 多维度树
export function getEfNodeTreeAnalysis(data) {
  return fetch({
    url: `/eem-service/v1/ef-node/tree/analysis`,
    method: "POST",
    data
  });
}

/**
 * 查询根节点
 */
export function rootNode() {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef-node/root-node`,
    method: "GET"
  });
}

/**
 * 查询根节点
 */
export function efNodeTree(data) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef-node/tree`,
    method: "POST",
    data
  });
}

/**
 * 能效分析节点树查询
 */
export function treeByEf(params) {
  return fetch({
    url: `/eem-base/energy-efficiency/v1/ef-node/treeByEf`,
    method: "POST",
    params
  });
}
