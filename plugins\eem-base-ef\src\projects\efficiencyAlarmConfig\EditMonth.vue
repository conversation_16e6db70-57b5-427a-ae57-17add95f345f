<template>
  <div class="editMonthBox general">
    <el-table :data="calendarData" border style="width: 100%">
      <el-table-column
        v-for="(day, index) in weekDays"
        :key="index"
        :prop="index.toString()"
        :label="day.label"
        align="left"
      >
        <template #default="{ row }">
          <div
            class="day-cell"
            :class="{
              weekend: index === 0 || index === 6
            }"
          >
            <div class="day-number text-T4 text-Aa mb-J0">
              {{ row[index].day }}
            </div>
            <el-tooltip :content="valueFormat(dataMap[row[index].date])">
              <div
                class="day-content text-H3 text-ellipsis"
                v-if="row[index].day"
              >
                {{ valueFormat(dataMap[row[index].date]) }}
              </div>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import omegaI18n from "@omega/i18n";
export default {
  name: "EditMonth",
  props: {
    data_in: Array,
    date: [String, Date, Number]
  },
  data() {
    const en = omegaI18n.locale === "en";
    return {
      calendarData: [],
      weekDays: [
        { label: en ? "Sun" : "日" },
        { label: en ? "Mon" : "一" },
        { label: en ? "Tue" : "二" },
        { label: en ? "Wed" : "三" },
        { label: en ? "Thu" : "四" },
        { label: en ? "Fri" : "五" },
        { label: en ? "Sat" : "六" }
      ],
      dataMap: {}
    };
  },
  watch: {
    data: {
      handler: function () {
        this.updateCalendar();
      },
      deep: true,
      immediate: true
    },
    date: {
      handler: function () {
        this.updateCalendar();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    valueFormat(val) {
      if (val == null) {
        return "--";
      }
      return val.toFixed(2);
    },
    updateCalendar() {
      this.calendarData = this.generateMonthCalendar(this.date);
      const data = this.data_in || [];
      const start = +this.$moment(this.date).startOf("month");
      const end = +this.$moment(this.date).endOf("month");
      const dataMap = {};
      data.forEach(({ aggregationcycle, validtime, limitvalue }) => {
        if (validtime >= start && validtime <= end && aggregationcycle === 12) {
          dataMap[validtime] = limitvalue;
        }
      });
      this.dataMap = dataMap;
    },
    generateMonthCalendar(date) {
      const currentMonth = this.$moment(date).startOf("month");
      const daysInMonth = currentMonth.daysInMonth();
      const firstDayOfWeek = currentMonth.day();

      const calendar = [];
      let dayCount = 1;
      const totalWeeks = Math.ceil((firstDayOfWeek + daysInMonth) / 7);

      for (let week = 0; week < totalWeeks; week++) {
        const weekDays = [];

        for (let day = 0; day < 7; day++) {
          if ((week === 0 && day < firstDayOfWeek) || dayCount > daysInMonth) {
            weekDays.push({
              date: null,
              day: "",
              moment: null,
              isCurrentMonth: false
            });
          } else {
            const currentDate = currentMonth.clone().date(dayCount);
            weekDays.push({
              date: +currentDate,
              day: dayCount,
              moment: currentDate,
              isCurrentMonth: true
            });
            dayCount++;
          }
        }

        calendar.push(weekDays);
      }

      return calendar;
    }
  }
};
</script>
<style lang="scss" scoped>
.day-cell {
  height: 80px;
  padding: var(--J0) 0;
}
</style>
