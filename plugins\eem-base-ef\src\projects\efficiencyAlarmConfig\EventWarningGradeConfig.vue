<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div class="eem-cont-c1">
      <el-table :data="tableData" border>
        <el-table-column
          type="index"
          :label="$T('序号')"
          align="left"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="key1"
          :label="$T('启用报警')"
          align="center"
          :width="en ? 140 : 100"
        >
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row[scope.column.property].val"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="key2" :label="$T('报警等级标签')" align="left">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row[scope.column.property].val"
              :placeholder="$T('请选择')"
              @change="
                selectChange(scope.row, scope.row[scope.column.property].val)
              "
            >
              <el-option
                v-for="item in scope.row[scope.column.property].options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="key3" :label="$T('报警等级')" align="left">
          <template slot-scope="scope">
            <el-tag
              :color="getLabelColor(scope.row.key2.val).bgColor"
              :style="`color: ${getLabelColor(scope.row.key2.val).color}`"
            >
              {{ scope.row[scope.column.property].val }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="key4"
          :label="$T('报警名称')"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row[scope.column.property].val"
              :placeholder="$T('请输入内容')"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="key5"
          :label="$T('自动计算比例')"
          align="left"
          :minWidth="en ? 120 : 80"
        >
          <template slot-scope="scope">
            <ElInputNumber
              v-model="scope.row.key5.val"
              v-bind="ElInputNumber_1"
              v-on="ElInputNumber_1.event"
            ></ElInputNumber>
            <span class="ml-J0">%</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { getEventGradeColor } from "@/utils/eventColor.js";
import omegaI18n from "@omega/i18n";
export default {
  name: "EventWarningGradeConfig",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Array
    },
    // 报警等级
    alarmlevelconfig_model: {
      type: Array
    }
  },

  computed: {
    en() {
      return omegaI18n.locale === "en";
    }
  },

  data() {
    return {
      rate: null,
      tableData: [],
      copyTableData: [],
      CetDialog_1: {
        title: $T("报警等级配置"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInputNumber_1: {
        value: "",
        style: {
          width: "70px"
        },
        controls: false,
        min: 0,
        max: 100,
        step: 2,
        precision: 0,
        controlsPosition: "",
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.getSchemeLevel();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    // 获取报警等级配置
    getSchemeLevel() {
      if (!this.inputData_in?.length) {
        return;
      }
      this.tableData = this.inputData_in.map(item => {
        return {
          key1: {
            val: item.isactive
          },
          key2: {
            val: item.alarmcolorset_id,
            options: [
              {
                value: 1,
                label: $T("红色报警")
              },
              {
                value: 2,
                label: $T("橙色报警")
              },
              {
                value: 3,
                label: $T("黄色报警")
              }
            ]
          },
          key3: {
            val: item.alarmlevel
          },
          key4: {
            val: item.name
          },
          key5: {
            val: item.rate
          },
          id: item.id
        };
      });
    },
    // 选择器变化
    selectChange(data, val) {
      if (val == 1) {
        data.key3.val = $T("报警等级一");
        data.key5.val = 100;
      } else if (val == 2) {
        data.key3.val = $T("报警等级二");
        data.key5.val = 85;
      } else if (val == 3) {
        data.key3.val = $T("报警等级三");
        data.key5.val = 80;
      }
    },
    CetButton_confirm_statusTrigger_out() {
      this.Add_Level();
    },
    Add_Level() {
      let data;
      let ajaxFlag = true;
      data = this.tableData.map(item => {
        return {
          isactive: item.key1.val,
          alarmcolorset_id: item.key2.val,
          alarmlevel: item.key3.val,
          id: item.id,
          modelLabel: "alarmlevelconfig",
          name: item.key4.val.trim(),
          rate: item.key5.val
        };
      });
      data.forEach(item => {
        if (item.isactive && !item.name) {
          this.$message({
            message: $T("启用的报警名称不能为空"),
            type: "warning"
          });
          ajaxFlag = false;
          return;
        }
        if (item.name.length > 255) {
          this.$message({
            message: $T("报警名称长度不能大于255个字符"),
            type: "warning"
          });
          ajaxFlag = false;
        }
      });
      if (!ajaxFlag) {
        return;
      }
      this.$emit("alarmlevelconfig_model_out", data);
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    getLabelColor(val) {
      return getEventGradeColor(val);
    }
  }
};
</script>
