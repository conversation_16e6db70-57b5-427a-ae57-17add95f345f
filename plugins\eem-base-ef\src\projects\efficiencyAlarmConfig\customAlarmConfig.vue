<template>
  <div class="h-full w-full">
    <ElForm
      :model="form.data"
      :rules="form.rules"
      label-position="top"
      ref="form"
    >
      <el-form-item :label="$T('方案名称')" prop="name">
        <el-input
          class="w-[300px]"
          v-model.trim="form.data.name"
          :placeholder="$T('输入方案名称')"
        ></el-input>
      </el-form-item>
      <el-form-item :label="$T('指标方向')" prop="limittype">
        <el-select
          class="w-[300px]"
          :placeholder="$T('请选择')"
          v-model="form.data.limittype"
        >
          <el-option :label="$T('越上限')" :value="1"></el-option>
          <el-option :label="$T('越下限')" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$T('指标值')" prop="limitvalue">
        <ElInputNumber
          class="w-[300px]"
          v-model="form.data.limitvalue"
          v-bind="ElInputNumber_1"
          v-on="ElInputNumber_1.event"
        ></ElInputNumber>
      </el-form-item>
    </ElForm>
  </div>
</template>

<script>
import common from "eem-base/utils/common.js";
export default {
  name: "customAlarmConfig",
  props: {
    data_in: Object
  },
  data() {
    return {
      form: {
        data: {},
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入方案名称"),
              trigger: ["blur", "change"]
            },
            common.check_name,
            common.check_pattern_name
          ],
          limittype: [
            {
              required: true,
              message: $T("请选择指标方向"),
              trigger: ["blur", "change"]
            }
          ],
          limitvalue: [
            {
              required: true,
              message: $T("请输入指标值"),
              trigger: ["blur", "change"]
            }
          ]
        }
      },
      ElInputNumber_1: {
        value: null,
        controls: false,
        min: 0,
        max: 999999999999.99,
        step: 2,
        precision: 3,
        controlsPosition: "",
        placeholder: $T("请输入内容"),
        event: {}
      }
    };
  },
  watch: {
    data_in: {
      handler() {
        this.init();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    init() {
      this.form.data = {
        name: this.data_in?.name || "",
        limittype: this.data_in?.limittype,
        limitvalue: this.data_in?.limitvalue ?? undefined,
        id: this.data_in?.id ?? 0
      };
    },
    async validate() {
      try {
        await this.$refs.form.validate();
        return this.form.data;
      } catch (error) {
        return false;
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
