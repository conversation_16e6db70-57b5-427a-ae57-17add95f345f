<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <span slot="footer">
      <span class="fl ml-J1">
        {{ $T("最多选中{0}个节点", this.maxRelevanceNodeNumber) }}
      </span>
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
    <div class="flex-row flex" style="height: 672px">
      <div
        class="mr-J3"
        style="width: 446px; box-sizing: border-box"
        v-show="multidimensional"
      >
        <div class="title">{{ $T("选择维度") }}</div>
        <CetTable
          ref="cetTable"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          class="mt-J3"
          style="height: calc(100% - 38px)"
        >
          <ElTableColumn width="50">
            <template slot-scope="scope">
              <el-radio v-model="radio" :label="scope.row.id"></el-radio>
            </template>
          </ElTableColumn>
          <ElTableColumn
            type="index"
            :label="$T('序号')"
            width="70"
          ></ElTableColumn>
          <ElTableColumn
            :label="$T('节点树名称')"
            prop="name"
            show-overflow-tooltip
          ></ElTableColumn>
          <ElTableColumn :label="$T('关联节点状态')" prop="relateNode">
            <template slot-scope="scope">
              <el-tag :type="scope.row.relateNode ? 'warning' : 'info'">
                {{ $T(scope.row.relateNode ? $T("已关联") : $T("未关联")) }}
              </el-tag>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
      <div class="flex-auto flex flex-col">
        <div class="title">{{ $T("关联节点") }}</div>
        <div class="mt-J3 flex-row flex flex-auto">
          <div class="leftTreeBox flex-auto flex flex-col">
            <div>
              <span class="title">{{ $T("选择节点") }}</span>
              <el-checkbox v-model="checked" @change="checkedChange" class="fr">
                {{ $T("默认选中子节点") }}
              </el-checkbox>
            </div>
            <CetGiantTree
              ref="giantTree1"
              class="mt-J3 flex-auto"
              v-show="!checked"
              v-bind="CetGiantTree_1"
              v-on="CetGiantTree_1.event"
            ></CetGiantTree>
            <CetGiantTree
              ref="giantTree2"
              class="mt-J3 flex-auto"
              v-show="checked"
              v-bind="CetGiantTree_2"
              v-on="CetGiantTree_2.event"
            ></CetGiantTree>
            <div class="flex flex-row justify-end">
              <CetButton
                v-bind="CetButton_reset"
                v-on="CetButton_reset.event"
              ></CetButton>
              <CetButton
                class="ml-J3"
                v-bind="CetButton_review"
                v-on="CetButton_review.event"
              ></CetButton>
            </div>
          </div>
          <div class="rightTreeBox ml-J3 flex-auto">
            <div>
              <span class="title">{{ $T("已选节点预览") }}</span>
              <span class="fr selectNum">
                {{
                  $T(
                    "已选节点：{0} 个",
                    CetVirtualTree_2.filterNodes_in?.length ?? 0
                  )
                }}
              </span>
            </div>
            <ElInput
              class="mb-J3 search mt-J3"
              v-model.trim="ElInput_2.value"
              v-bind="ElInput_2"
              v-on="ElInput_2.event"
            ></ElInput>
            <CetVirtualTree
              class="cetVirtualTree flex-auto"
              v-bind="CetVirtualTree_2"
              v-on="CetVirtualTree_2.event"
            ></CetVirtualTree>
          </div>
        </div>
      </div>
    </div>
  </CetDialog>
</template>
<script>
import commonApi from "@/api/custom.js";
export default {
  name: "relevanceNode",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Array
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    },
    rootNode() {
      return {
        id: this.inputData_in?.[0]?.rootnodeid,
        modelLabel: this.inputData_in?.[0]?.rootnodelabel
      };
    }
  },

  data() {
    return {
      firstFlag: true, // 区分是否首次进入弹窗，首次进入弹窗表格选中当前行时不需要提示是否保存
      radio: "",
      // 最大关联节点树
      maxRelevanceNodeNumber: 200,
      // 选择框是否点击
      checked: false,
      CetDialog_1: {
        width: "1200px",
        title: $T("关联节点"),
        top: "5vh",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      currentRow: {},
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this._.debounce(this.CetTable_1_record_out, 300)
        }
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      CetButton_review: {
        visible_in: true,
        disable_in: false,
        title: $T("预览"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_review_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      treeCheckedNodes: [],
      // 1组件
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      // 2组件
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "ps", N: "ps" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      ElInput_2: {
        value: "",
        placeholder: $T("请输入关键字搜索"),
        "suffix-icon": "el-icon-search",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElInput_2_change_out
        }
      },
      CetVirtualTree_2_TreeV2: null,
      CetVirtualTree_2: {
        filterNodes_in: [],
        inputData_in: [],
        attribute: {
          data: [],
          props: {
            value: "tree_id",
            label: "name",
            children: "children",
            disabled: "id"
          },
          emptyText: $T("暂无数据"),
          nodeKey: "tree_id",
          defaultCheckedKeys: [],
          defaultExpandedKeys: [],
          showCheckbox: true,
          highlightCurrent: true,
          checkStrictly: true,
          height: 420,
          filterMethod: this.hideNode
        },
        event: {
          onCreate: this.CetVirtualTree_2_onCreate
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in(val) {
      this.firstFlag = true;
      this.treeCheckedNodes = [];
      this.checked = false;
      this.CetDialog_1.openTrigger_in = val;
      this.ElInput_2.value = "";
      await this.getTableData();
      await this.$nextTick();
      this.$refs.cetTable.$refs.cetTable.setCurrentRow(
        this.CetTable_1.data?.[0]
      );
      if (this.inputData_in && this.inputData_in.length === 1) {
        // 单条管理需要先查已关联节点
        // this.getAlreadyRelevance();
      } else {
        this.CetGiantTree_1.checkedNodes = [];
        this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        this.CetGiantTree_2.checkedNodes = [];
        this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      }
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    async getTableData() {
      const queryData = {
        projectId: this.projectId,
        status: true
      };
      const batchFlag = this.inputData_in?.length !== 1;
      const id = this._.get(this.inputData_in, "[0].id");
      if (!batchFlag) {
        queryData.efIds = [id];
      }
      const res = await commonApi.getEfNodeDimTreeConfig(queryData);
      this.CetTable_1.data = res?.data || [];
    },
    async CetTable_1_record_out(val) {
      if (this.giveUpOut) {
        this.giveUpOut = false;
        return;
      }
      this.CetVirtualTree_2_TreeV2?.setCheckedKeys([]);
      const batchFlag = this.inputData_in?.length !== 1;
      this.checked = false;
      if (this.firstFlag) {
        this.TableClickNextDo(val, batchFlag);
        return;
      }
      this.$confirm($T("是否要保存当前维度关联节点"), $T("提示"), {
        confirmButtonText: $T("保存"),
        cancelButtonText: $T("不保存"),
        type: "warning"
      })
        .then(async () => {
          this.giveUpOut = true;
          const nextFlag = await this.Add_relevance(false);
          if (nextFlag === false) return;
          this.TableClickNextDo(val, batchFlag);
        })
        .catch(() => {
          this.TableClickNextDo(val, batchFlag);
        });
    },
    async TableClickNextDo(val, batchFlag = false) {
      this.currentRow = this._.cloneDeep(val);
      this.radio = val.id;
      if (val.id === -1 && !val.modelLabel) {
        this.CetGiantTree_1.inputData_in = [];
        this.CetGiantTree_2.inputData_in = [];
        this.CetVirtualTree_2_TreeV2?.setData([]);
        return;
      }
      this.firstFlag = false;
      val.id === -1 ? await this.getTreeData1() : await this.getTreeData2();
      this.setShowNodes();
      !batchFlag && this.getAlreadyRelevance();
    },
    CetButton_reset_statusTrigger_out() {
      this.getAlreadyRelevance();
    },
    CetButton_review_statusTrigger_out() {
      this.setShowNodes();
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.Add_relevance();
    },
    CetGiantTree_1_checkedNodes_out(val) {
      if (!this.checked) {
        this.treeCheckedNodes = this._.cloneDeep(val);
      }
    },
    CetGiantTree_2_checkedNodes_out(val) {
      if (this.checked) {
        this.treeCheckedNodes = this._.cloneDeep(val);
      }
    },
    async getAlreadyRelevance() {
      var _this = this;
      const checkedNodes = await this.getAlreadyNodes();
      if (checkedNodes.length) {
        _this.CetGiantTree_1.checkedNodes = checkedNodes;
        // _this.CetGiantTree_2.checkedNodes = checkedNodes;
        _this.expandNode(
          checkedNodes,
          "tree_id",
          _this.$refs.giantTree1.ztreeObj
        );
        _this.expandNode(
          checkedNodes,
          "tree_id",
          _this.$refs.giantTree2.ztreeObj
        );
      } else {
        _this.CetGiantTree_1.checkedNodes = [];
        _this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
        _this.CetGiantTree_2.checkedNodes = [];
        _this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      }
      this.treeCheckedNodes = checkedNodes;
      _this.CetGiantTree_1.inputData_in = this._.cloneDeep(
        _this.CetGiantTree_1.inputData_in
      );
      this.setShowNodes();
    },
    async getAlreadyNodes() {
      const id = this._.get(this.inputData_in, "[0].id");
      if (!id) {
        return [];
      }
      const res = await commonApi.getEfSetIdByRelevance({
        efSetId: id,
        dimTreeConfigId: this.currentRow.id
      });
      if (res?.code !== 0) return [];

      const data = this._.get(res, "data", []);
      const checkedNodes = data.map(item => {
        return {
          id: item.objectid,
          modelLabel: item.objectlabel,
          tree_id: `${item.objectlabel}_${item.objectid}`
        };
      });
      return checkedNodes;
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          let node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree1.$el).find(".ztree").scrollLeft(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollLeft(0);
      }, 0);
    },
    async Add_relevance(closeFlag = true) {
      var _this = this;
      var params = [];
      var customApiFn;
      let efSetIds = [];
      _this.inputData_in.forEach(item => {
        item.mergeDisplayAdds.forEach(key => {
          efSetIds.push(key.efSetId);
        });
      });
      params = {
        efSetIds: efSetIds,
        projectId: this.projectId,
        dimTreeConfigId: this.currentRow.id,
        nodes: [],
        rootNode: {
          id: 0,
          modelLabel: "string"
        }
      };
      if (_this.treeCheckedNodes.length > this.maxRelevanceNodeNumber) {
        this.$message.error(
          $T("最多选中{0}个节点", this.maxRelevanceNodeNumber)
        );
        return false;
      }
      customApiFn = commonApi.addEfSetByNodeRelevanceAll;
      _this.treeCheckedNodes.forEach(item => {
        params.nodes.push({
          id: item.id,
          modelLabel: item.modelLabel
        });
      });
      const response = await customApiFn(params);
      if (response.code === 0) {
        _this.$message({
          type: "success",
          message: $T("保存成功")
        });
        if (closeFlag) {
          _this.CetDialog_1.closeTrigger_in = new Date().getTime();
        } else {
          await this.getTableData();
        }
      }
    },
    // 获取管理层级节点树
    async getTreeData1() {
      const queryData = {
        nodeTreeGroupId: 2,
        rootNode: this.rootNode
      };
      const res = await commonApi.efNodeTree(queryData);
      if (res.code !== 0) {
        return;
      }
      const treeData = this._.get(res, "data", []);
      this.CetGiantTree_1.inputData_in = treeData;
      this.CetGiantTree_2.inputData_in = treeData;
      this.CetVirtualTree_2_TreeV2.setData(this.setReviewTreeData(treeData));
      this.CetVirtualTree_2_TreeV2.setCheckedKeys([]);
    },
    // 获取维度节点树
    async getTreeData2() {
      const queryData = {
        dimTreeConfigId: this.currentRow.id
      };
      const res = await commonApi.getAttributeDimensionTreeNodetree(queryData);
      const treeData = this._.get(res, "data", []);
      this.CetGiantTree_1.inputData_in = treeData;
      this.CetGiantTree_2.inputData_in = treeData;
      this.CetVirtualTree_2_TreeV2.setData(this.setReviewTreeData(treeData));
      this.CetVirtualTree_2_TreeV2.setCheckedKeys([]);
    },
    setReviewTreeData(data) {
      if (!data.length) return [];
      data.forEach(item => {
        this.$set(item, "disabled", true);
        if (item.children?.length) this.setReviewTreeData(item.children);
      });
      return data;
    },
    // 选择框的改变
    checkedChange() {
      const vm = this;
      let checkNodes = vm._.cloneDeep(vm.treeCheckedNodes);
      setTimeout(() => {
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree1.$el).find(".ztree").scrollLeft(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollLeft(0);
        if (vm.checked) {
          vm.CetGiantTree_2.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree2.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
          }
        } else {
          vm.CetGiantTree_1.checkedNodes = checkNodes;
          vm.expandNode(checkNodes, "tree_id", vm.$refs.giantTree1.ztreeObj);
          if (!checkNodes.length) {
            vm.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
          }
        }
      }, 0);
    },
    CetVirtualTree_2_onCreate(event) {
      this.CetVirtualTree_2_TreeV2 = event;
    },
    ElInput_2_change_out(val) {
      this.CetVirtualTree_2_TreeV2.filter(val);
    },
    // 获取需要展示的节点
    async setShowNodes() {
      await this.$nextTick();
      const showTreeKeys = [];
      this.treeCheckedNodes.forEach(item => {
        const tree_id = item.tree_id;
        if (!showTreeKeys.includes(tree_id)) {
          showTreeKeys.push(tree_id);
        }
      });
      this.CetVirtualTree_2_TreeV2.setCheckedKeys(
        this.treeCheckedNodes.map(i => i.tree_id)
      );
      this.CetVirtualTree_2.filterNodes_in = showTreeKeys;
      this.CetVirtualTree_2_TreeV2.filter(this.ElInput_2.value);
    },
    hideNode(value, data) {
      const showTreeKeys = this.CetVirtualTree_2.filterNodes_in ?? [];
      if (!showTreeKeys.includes(data.tree_id)) return false;
      if (!value) return true;
      return data.name.includes(value);
    }
  }
};
</script>
<style lang="scss" scoped>
.title {
  @include font_size(H3);
}
:deep() {
  .el-radio__label {
    display: none;
  }
}
.el-tag {
  height: 30px;
  line-height: 30px;
}
.el-tag--warning {
  @include border_color(Sta2);
}
.el-tag--info {
  border: 1px solid;
  @include border_color(B1);
  @include font_color(T6, !important);
  @include background_color(BG2);
}
.leftTreeBox .gianttree {
  height: calc(100% - 70px);
}
.leftTreeBox,
.rightTreeBox {
  border: 1px solid;
  @include border_color(B1);
  border-radius: var(--Ra);
  @include padding(J4);
}
.selectNum {
  @include font_size(Ab);
  @include font_color(T3);
}
:deep() {
  .el-checkbox__input.is-disabled .el-checkbox__inner {
    @include background_color(B1);
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    &::after {
      @include border_color(BG2);
    }
  }
}
.cetVirtualTree {
  :deep(.el-vl__window.el-tree-virtual-list::-webkit-scrollbar-track) {
    background-color: transparent;
  }
  :deep(.el-virtual-scrollbar) {
    display: none;
  }
}
</style>
