﻿<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div class="eem-cont-c1 flex flex-row">
      <CetGiantTree
        class="tree flex-auto"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <div class="flex flex-col flex-auto ml-J3">
        <div>
          <el-input
            :placeholder="$T('输入事件描述关键字')"
            suffix-icon="el-icon-search"
            v-model="searchParams.description"
            v-show="false"
          ></el-input>
        </div>
        <div class="event-box">
          <span class="event-box-head">{{ $T("事件状态") }}</span>
          <div class="event-box-content">
            <ElCheckboxGroup
              v-model="ElCheckboxGroup_3.value"
              v-bind="ElCheckboxGroup_3"
              v-on="ElCheckboxGroup_3.event"
            >
              <ElCheckbox
                v-for="item in ElCheckboxList_3.options_in"
                :key="item[ElCheckboxList_3.key]"
                :label="item[ElCheckboxList_3.label]"
                :disabled="item[ElCheckboxList_3.disabled]"
              >
                {{ item[ElCheckboxList_3.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </div>
        <div class="event-box mt-J3">
          <span class="event-box-head">{{ $T("事件类型") }}</span>
          <div class="event-box-content">
            <ElCheckboxGroup
              v-model="searchParams.types"
              v-bind="ElCheckboxGroup_1"
              v-on="ElCheckboxGroup_1.event"
            >
              <ElCheckbox
                v-for="item in ElCheckboxList_1.options_in"
                :key="item[ElCheckboxList_1.key]"
                :label="item[ElCheckboxList_1.label]"
                :disabled="item[ElCheckboxList_1.disabled]"
              >
                {{ item[ElCheckboxList_1.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </div>
        <div class="event-box mt-J3">
          <span class="event-box-head">{{ $T("事件等级") }}</span>
          <div class="event-box-content">
            <ElCheckboxGroup
              v-model="searchParams.levels"
              v-bind="ElCheckboxGroup_2"
              v-on="ElCheckboxGroup_2.event"
            >
              <ElCheckbox
                v-for="item in ElCheckboxList_2.options_in"
                :key="item[ElCheckboxList_2.key]"
                :label="item[ElCheckboxList_2.label]"
                :disabled="item[ElCheckboxList_2.disabled]"
              >
                {{ item[ElCheckboxList_2.text] }}
              </ElCheckbox>
            </ElCheckboxGroup>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="ml-J1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
export default {
  name: "energyConsumptionEventAdvancedQuery",
  components: {},
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    searchParams_in: Object,
    treeData_in: Array,
    clickNode_in: Object
  },

  data() {
    const setNodeClasses = (treeId, treeNode) => {
      return treeNode.childSelectState == 2
        ? { add: ["halfSelectedNode"] }
        : { remove: ["halfSelectedNode"] };
    };
    return {
      searchParams: {},
      clickNode: null,
      CetDialog_1: {
        title: $T("事件-高级查询"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElCheckboxGroup_1: {
        value: [],
        style: {},
        showCheckAll: true,
        event: {}
      },
      ElCheckboxList_1: {
        options_in: [],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_2: {
        value: [],
        style: {},
        showCheckAll: true,
        event: {}
      },
      ElCheckboxList_2: {
        options_in: [
          {
            id: 1,
            text: $T("一级")
          },
          {
            id: 2,
            text: $T("二级")
          },
          {
            id: 3,
            text: $T("三级")
          },
          {
            id: 4,
            text: $T("其他")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      ElCheckboxGroup_3: {
        value: [1, 3],
        style: {},
        showCheckAll: true,
        event: {}
      },
      ElCheckboxList_3: {
        options_in: [
          {
            id: 1,
            text: $T("待处理事件")
          },
          {
            id: 3,
            text: $T("已处理事件")
          }
        ],
        key: "id",
        label: "id",
        text: "text",
        disabled: "disabled"
      },
      eventKeyWord: "",

      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          view: {
            nodeClasses: setNodeClasses,
            addDiyDom: (treeId, treeNode) => {
              if (!treeNode.changeStatus) return;
              var sObj = $("#" + treeNode.tId + "_a");
              const dom = `
              <div class="relative inline-block tooltipBox" >
                <i class="el-icon-question fcT2"></i>
                <div class="tooltip el-tooltip__popper is-light" x-placement="bottom">
                  ${this.effTimeFormat(treeNode)}
                  <div x-arrow="" class="popper__arrow" style="left: 49.5px;"></div>
                </div>
              </div>`;
              sObj.append(dom);
            }
          }
        },
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      CetCheckboxGroup_3: {
        dataConfig: {
          edition: "v1",
          queryUrl: "/data-center/v1/common/queryEnumerations",
          type: "",
          modelLabel: "transientfaultdirection",
          dropItemId: "id",
          dropItemText: "text"
        },
        dataMode: "static",
        inputData_in: [],
        CheckedArray: [],
        config: {
          style: {
            display: "inline-block",
            width: "100%"
          },
          showCheckAll: true
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      this.searchParams = this._.cloneDeep(this.searchParams_in);
      this.clickNode = this._.cloneDeep(this.clickNode_in);
      this.initTree();
      this.CetGiantTree_1.selectNode = this.clickNode;
      this.loadEnumrations();

      // 解析状态
      const { status } = this.searchParams_in;
      const statusMap = {
        3: [3],
        1: [1]
      };
      this.ElCheckboxGroup_3.value = statusMap[status] || [1, 3];
    },
    loadEnumrations() {
      var data = this.$store.state.enumerations.eventtype || [];
      data = data.filter(item => {
        return [708, 709].indexOf(item.id) != -1;
      });
      this.ElCheckboxList_1.options_in = data;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      if (!this.ElCheckboxGroup_3.value.length) {
        return this.$message.error("请选择事件状态");
      }
      if (!this.searchParams.types) {
        return this.$message.error("请选择事件类型");
      }
      if (!this.searchParams.levels) {
        return this.$message.error("请选择事件等级");
      }

      let status = undefined;
      if (this.ElCheckboxGroup_3.value.length === 1) {
        status = this.ElCheckboxGroup_3.value[0];
      }
      const params = {
        ...this.searchParams,
        status
      };
      this.$emit("saveData_out", this.clickNode, params);
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },

    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      this.clickNode = this._.cloneDeep(val);
    },
    initTree() {
      this.CetGiantTree_1.inputData_in = this._.cloneDeep(this.treeData_in);
    },
    effTimeFormat(data) {
      const { effTimeList } = data;
      if (!effTimeList) return;
      return effTimeList
        .map(item => {
          const { startTime, endTime } = item;
          return `${this.$moment(startTime).format(
            "YYYY-MM-DD"
          )}~${this.$moment(endTime).format("YYYY-MM-DD")}`;
        })
        .join(",");
    }
  }
};
</script>
<style lang="scss" scoped>
.tree {
  height: 500px;
  :deep(.halfSelectedNode .node_name) {
    @include font_color(T6);
  }

  :deep(.tooltipBox) {
    .tooltip {
      left: -44px;
      display: none;
    }
    &:hover .tooltip {
      display: block;
    }
  }
}
.event-box {
  border: 1px solid;
  @include border_color(B1);
  border-radius: var(--Ra);
}

.event-box-head {
  display: inline-block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-width: 0 0 1px;
  border-style: solid;
  border-radius: var(--Ra) var(--Ra) 0 0;
  padding: 0 20px;
  @include border_color(B1);
  @include background_color(B1);
}

.event-box-content {
  padding: var(--J3);
}
</style>
