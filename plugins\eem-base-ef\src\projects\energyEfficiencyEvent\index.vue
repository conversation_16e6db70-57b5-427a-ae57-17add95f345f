﻿<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full flex flex-col">
        <customElSelect
          v-model="ElSelect_treeType.value"
          v-bind="ElSelect_treeType"
          v-on="ElSelect_treeType.event"
          :prefix_in="$T('节点树类型')"
          class="mb-J3"
          v-if="multidimensional"
        >
          <ElOption
            v-for="item in ElOption_treeType.options_in"
            :key="item[ElOption_treeType.key]"
            :label="item[ElOption_treeType.label]"
            :value="item[ElOption_treeType.value]"
            :disabled="item[ElOption_treeType.disabled]"
          ></ElOption>
        </customElSelect>
        <CetGiantTree
          class="flex-auto"
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </div>
    </template>
    <template #container>
      <div class="h-full">
        <EnergyWarning
          :dimTreeConfigId="ElSelect_treeType.value"
          :clickNode="clickNode"
          :treeData_in="CetGiantTree_1.inputData_in"
          @setClickNode="setClickNode_out"
          @time_out="time_out"
          @refreshTree="refreshTree"
        ></EnergyWarning>
      </div>
    </template>
  </CetAside>
</template>
<script>
import customApi from "@/api/custom";
import EnergyWarning from "./energyconsumptionevent/EnergyConsumptionEvent";

export default {
  name: "energyEfficiencyEvent",
  components: {
    EnergyWarning
  },

  computed: {
    token() {
      return this.$store.state.token;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    multidimensional() {
      return this.$store.state.multidimensional;
    }
  },

  data() {
    const setNodeClasses = (treeId, treeNode) => {
      return treeNode.childSelectState == 2
        ? { add: ["halfSelectedNode"] }
        : { remove: ["halfSelectedNode"] };
    };
    return {
      keepParams: {},
      // treeType组件
      ElSelect_treeType: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_treeType_change_out
        }
      },
      // treeType组件
      ElOption_treeType: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          },
          // 按照要求进行zTree的节点权限置灰操作
          view: {
            nodeClasses: setNodeClasses,
            addDiyDom: (treeId, treeNode) => {
              if (this.ElSelect_treeType.value < 1) {
                return;
              }
              if (!treeNode.changeStatus) return;
              var sObj = $("#" + treeNode.tId + "_a");
              const dom = `
              <div class="relative inline-block tooltipBox" >
                <i class="el-icon-question fcT2"></i>
                <div class="tooltip el-tooltip__popper is-light" x-placement="bottom">
                  ${this.effTimeFormat(treeNode)}
                  <div x-arrow="" class="popper__arrow" style="left: 49.5px;"></div>
                </div>
              </div>`;
              sObj.append(dom);
            }
          }
        },
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300)
        }
      },
      currentNode: null,
      clickNode: null,
      timeRange: null
    };
  },

  methods: {
    // 获取节点树类型下拉框
    async queryTreeType() {
      const queryData = {
        projectId: this.projectId,
        status: true
      };
      const res = await customApi.getAttributeDimensionTreeNodeConfig(
        queryData
      );
      this.ElOption_treeType.options_in = (res?.data || []).map(i => ({
        id: i.id,
        name: i.name
      }));
      const flag = this.ElOption_treeType.options_in.find(i => i.id === -1);
      this.ElSelect_treeType.value = flag
        ? -1
        : this.ElOption_treeType.options_in?.[0]?.id ?? null;
      this.ElSelect_treeType_change_out(this.ElSelect_treeType.value);
    },
    CetTree_1_currentNode_out(val) {
      if (!val) {
        return;
      }
      // 按照张壮要求，在通用3.5迭代中，重新添加成点击弹窗处理
      if (val.childSelectState == 2) {
        return this.$message.warning($T("没有该节点权限"));
      }
      this.clickNode = this._.cloneDeep(val);
    },
    ElSelect_treeType_change_out(val) {
      if (this._.isNil(val)) {
        this.CetGiantTree_1.inputData_in = [];
        return;
      }
      val === -1 ? this.getTreeData1() : this.getTreeData2();
    },
    async getTreeData1() {
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      const subLayerConditions = [
        { modelLabel: "sectionarea" },
        { modelLabel: "building" },
        { modelLabel: "floor" },
        {
          filter: {
            composemethod: true,
            expressions: [
              {
                limit: null,
                operator: "EQ",
                prop: "roomtype",
                tagid: 2
              }
            ]
          },
          modelLabel: "room"
        },
        { modelLabel: "manuequipment" },
        { modelLabel: "airconditioner" },
        { modelLabel: "virtualbuildingnode" }
      ];
      if (this.$route.name === "energyConsumptionEvent") {
        subLayerConditions.push({ modelLabel: "virtualdevicenode" });
      }
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions,
        treeReturnEnable: true,
        // 按照3.5迭代骆海瑞要求添加该字段进行处理权限只返回一个项目节点的情况
        filterNoAuthEndNode: true
      };
      const res = await customApi.getNodeTreeSimple(data);
      if (res.code !== 0) {
        return;
      }

      this.CetGiantTree_1.inputData_in = res.data;
      // 选中第一个有数据 childSelectState = 1 的节点并展开节点
      const obj = this._.find(this.dataTransform(res.data), [
        "childSelectState",
        1
      ]);
      if (obj?.tree_id === this.CetGiantTree_1.selectNode?.tree_id) {
        this.CetTree_1_currentNode_out(obj);
      }
      this.CetGiantTree_1.selectNode = obj;
    },
    // 获取维度节点树数据
    async getTreeData2(keepSelectNode) {
      const queryData = {
        dimTreeConfigId: this.ElSelect_treeType.value,
        projectId: this.projectId,
        startTime: this.timeRange?.[0],
        endTime: this.timeRange?.[1],
        aggregationCycle: 12
      };
      if (this.$route.name === "energyEfficiencyEvent") {
        queryData.keepNodeTypes = ["manuequipment"];
      }
      const res = await customApi.dimensionTreeFilterByEnergytype(queryData);
      this.CetGiantTree_1.inputData_in = res?.data || [];
      this.CetGiantTree_1.inputData_in = res.data;
      let obj,
        nodeList = this.dataTransform(res.data);
      if (keepSelectNode) {
        obj = this._.find(
          nodeList,
          ["id", this.clickNode.id],
          ["modelLabel", this.clickNode.modelLabel]
        );
      }
      if (!keepSelectNode || !obj) {
        // 选中第一个有数据 childSelectState = 1 的节点并展开节点
        obj = this._.find(nodeList, ["childSelectState", 1]);
      }
      if (obj?.tree_id === this.CetGiantTree_1.selectNode?.tree_id) {
        this.CetTree_1_currentNode_out(obj);
      }
      this.CetGiantTree_1.selectNode = obj;
    },
    // 获取第一个有权限的节点
    dataTransform(array) {
      const cloneData = this._.cloneDeep(array);
      const arr = [];
      const expanded = datas => {
        if (datas && datas.length > 0 && datas[0]) {
          datas.forEach(e => {
            arr.push(e);
            expanded(e.children);
          });
          return arr;
        }
      };
      return expanded(cloneData);
    },
    setClickNode_out(val) {
      if (!val) {
        return;
      }
      this.CetGiantTree_1.selectNode = val;
      this.clickNode = val;
      this.CetTree_1_currentNode_out(val);
    },
    time_out(val) {
      this.timeRange = val;
    },
    refreshTree() {
      this.getTreeData2(true);
    },
    effTimeFormat(data) {
      const { effTimeList } = data;
      if (!effTimeList) return;
      return effTimeList
        .map(item => {
          const { startTime, endTime } = item;
          return `${this.$moment(startTime).format(
            "YYYY-MM-DD"
          )}~${this.$moment(endTime).format("YYYY-MM-DD")}`;
        })
        .join(",");
    }
  },

  mounted() {
    this.CetGiantTree_1.selectNode = null;
    this.queryTreeType();
  }
};
</script>
<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
</style>
