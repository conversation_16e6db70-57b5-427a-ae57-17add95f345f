<template>
  <div class="date-range">
    <el-button
      size="small"
      icon="el-icon-arrow-left"
      @click="queryPrv"
      style="margin-right: 5px"
    ></el-button>
    <span class="date-range-label">{{$T('时段')}}</span>
    <el-date-picker
      ref="datePicker"
      v-model="value"
      class="date-picker"
      :firstDayOfWeek="1"
      size="small"
      :clearable="false"
      value-format="timestamp"
      type="daterange"
      align="right"
      unlink-panels
      :range-separator="$T('至')"
      :start-placeholder="$T('开始日期')"
      :end-placeholder="$T('结束日期')"
      :picker-options="pickerOptions"
    ></el-date-picker>
    <el-button
      size="small"
      icon="el-icon-arrow-right"
      @click="queryNext"
      style="margin-left: 5px"
    ></el-button>
  </div>
</template>
<script>
import moment from "moment";
const SHORT_CUTS = [
  {
    text: this.$T("今天"),
    typeID: 1,
    unit: "d",
    number: 1,
    onClick(picker) {
      console.log("picker", picker);

      const end = moment().endOf("d").valueOf() + 1;
      const start = moment().startOf("d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: this.$T("昨天"),
    typeID: 2,
    unit: "d",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").valueOf();
      const start = moment().startOf("d").add(-1, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近3天"),
    typeID: 3,
    unit: "d",
    number: 3,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-2, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近一周"),
    typeID: 4,
    unit: "w",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-6, "d").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近一个月"),
    typeID: 5,
    unit: "M",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-1, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近三个月"),
    typeID: 6,
    unit: "M",
    number: 3,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-3, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近半年"),
    typeID: 7,
    unit: "M",
    number: 6,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-6, "M").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  },
  {
    text: $T("最近1年"),
    typeID: 8,
    unit: "y",
    number: 1,
    onClick(picker) {
      const end = moment().startOf("d").add(1, "d").valueOf();
      const start = moment().startOf("d").add(-1, "y").valueOf();
      picker.typeID = this.typeID;
      picker.$emit("pick", [start, end]);
    }
  }
];
export default {
  name: "TimeRange",
  props: {
    val: Array,
    shortcuts: {
      type: Array,
      default: () => SHORT_CUTS
    },
    typeIds: {
      type: Array,
      default: () => [1, 2, 3, 4, 5, 6, 7, 8]
    },
    disabledDate: Function,
    onPick: Function
  },
  watch: {
    val: {
      deep: true,
      handler: function (val, oldVal) {
        this.value = val;
      }
    },
    value: {
      deep: true,
      handler: function (val, oldVal) {
        this.changDate(val);
      }
    }
  },
  computed: {},
  data() {
    const shortcuts = this.shortcuts.filter(item =>
      this.typeIds.includes(item.typeID)
    );
    return {
      pickerOptions: {
        firstDayOfWeek: 1,
        shortcuts: shortcuts,
        disabledDate: this.disabledDate,
        onPick: this.onPick
      },
      timeOptions: [
        {
          type: "today",
          text: this.$T("今天"),
          typeID: 1,
          number: 1,
          unit: "d"
        },
        {
          type: "yestoday",
          text: this.$T("昨天"),
          typeID: 2,
          number: -1,
          unit: "d"
        },
        {
          type: "lastThreeDay",
          text: $T("近3天"),
          typeID: 3,
          number: -3,
          unit: "d"
        },
        {
          type: "lastSevenDay",
          text: $T("近7天"),
          number: -7,
          typeID: 5,
          unit: "d"
        },
        {
          type: "theMonth",
          text: this.$T("当月"),
          number: 0,
          typeID: 5,
          unit: "M"
        }
      ],
      currentTimeOption: {
        type: "today",
        text: this.$T("今天"),
        typeID: 1,
        number: 0,
        unit: "d"
      },
      value: []
    };
  },
  methods: {
    queryPrv() {
      const date = moment(this.value[0]);
      let typeID;
      try {
        typeID = this.$refs.datePicker.picker.typeID || 1;
      } catch (error) {
        typeID = 1;
      }
      const currentDate = [];
      const shortcuts = this._.find(SHORT_CUTS, ["typeID", typeID]);
      currentDate[0] = date
        .subtract(shortcuts.number, shortcuts.unit)
        .valueOf();
      currentDate[1] = this.value[0];
      // this.changDate(currentDate);
      this.value = currentDate;
    },
    queryNext() {
      const date = moment(this.value[1]);
      let typeID;
      try {
        typeID = this.$refs.datePicker.picker.typeID || 1;
      } catch (error) {
        typeID = 1;
      }
      const currentDate = [];
      const shortcuts = this._.find(SHORT_CUTS, ["typeID", typeID]);
      currentDate[0] = this.value[1];
      currentDate[1] = date.add(shortcuts.number, shortcuts.unit).valueOf();
      // this.changDate(currentDate);
      this.value = currentDate;
    },
    changDate(val) {
      this.value = val;
      this.$emit("update:val", val);
      this.$emit("change", val);
    }
  },
  created: function () {},
  mounted() {
    this.value = this.val;
  }
};
</script>
<style lang="scss">
.date-range {
  display: flex;
  align-items: center;
  .date-range-label {
    display: block;
    // width: 86px;
    padding: 0 10px;
    text-align: center;
    line-height: 32px;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid;
    border-right: 0px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    @include border_color(B1);
    @include background_color(BG1);
    @include font_color(T1);
  }
  .date-picker {
    flex: 1;
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
  }
  .el-button {
    padding: 9px;
  }
}
</style>
