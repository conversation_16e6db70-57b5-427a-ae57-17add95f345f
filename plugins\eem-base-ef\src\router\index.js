/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "/efficiencyBenchmarkingManage",
        component: () =>
          import("@/projects/efficiencyBenchmarkingManage/index.vue")
      },
      {
        path: "/energyEfficiencyEvent",
        component: () => import("@/projects/energyEfficiencyEvent/index.vue")
      },
      {
        path: "/efficiencyAlarmConfig",
        component: () => import("@/projects/efficiencyAlarmConfig/index.vue")
      },
      {
        path: "/energyEfficiencyIndexes",
        component: () => import("@/projects/energyEfficiencyIndexes/index.vue")
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
