import { modules, state, mutations, actions } from "eem-base/store/index.js";
import customApi from "@/api/custom";

export default {
  modules: { ...modules },
  state: {
    ...state,
    multidimensional: true,
    systemCfg: { numberOfNodesCompared: 4 }
  },
  mutations: {
    ...mutations,
    setMultidimensional(state, val) {
      state.multidimensional = val;
    }
  },
  actions: {
    ...actions,
    async getConfig({ commit }) {
      const res = await customApi.configProperties();
      const config = res.data || {};
      commit("setMultidimensional", !!config.isSupportMultiDimension);
    }
  }
};
