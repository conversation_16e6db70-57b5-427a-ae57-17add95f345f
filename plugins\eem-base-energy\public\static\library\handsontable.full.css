@charset "UTF-8"; /*!
 * Copyright (c) HANDSONCODE sp. z o. o.
 * 
 * HANDSONTABLE is a software distributed by HANDSONCODE sp. z o. o.,
 * a Polish corporation, based in Gdynia, Poland, at 96/98 Aleja Zwycięstwa,
 * registered with the National Court Register under number 538651,
 * EU tax ID number: PL5862294002, share capital: PLN 62,800.00.
 * 
 * This software is protected by applicable copyright laws, including
 * international treaties, and dual-licensed – depending on whether
 * your use is intended for or may result in commercial advantage
 * or monetary compensation (commercial purposes), or not.
 * 
 * If your use involves only such purposes as research, private study,
 * evaluation and the like, you agree to be bound by the terms included
 * in the “handsontable-non-commercial-license.pdf” file, available
 * in the main directory of this software repository.
 * 
 * By installing, copying, or otherwise using this software for
 * commercial purposes, you agree to be bound by the terms included
 * in the “handsontable-general-terms.pdf” file, available in the main
 * directory of this software repository.
 * 
 * HANDSONCODE PROVIDES THIS SOFTWARE ON AN “AS IS” BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND. IN NO EVENT
 * AND UNDER NO LEGAL THEORY, SHALL HANDSONCODE BE LIABLE
 * TO YOU FOR DAMAGES, INCLUDING ANY DIRECT, INDIRECT, SPECIAL,
 * INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER ARISING
 * FROM USE OR INABILITY TO USE THIS SOFTWARE.
 * 
 * Version: 7.0.0
 * Release date: 06/03/2019 (built at 06/03/2019 11:52:26)
 */
.handsontable .table td,.handsontable .table th {
    border-top: none
}

.handsontable tr {
    background: #fff
}

.handsontable td {
    background-color: inherit
}

.handsontable .table caption+thead tr:first-child td,.handsontable .table caption+thead tr:first-child th,.handsontable .table colgroup+thead tr:first-child td,.handsontable .table colgroup+thead tr:first-child th,.handsontable .table thead:first-child tr:first-child td,.handsontable .table thead:first-child tr:first-child th {
    border-top: 1px solid #ccc
}

.handsontable .table-bordered {
    border: 0;
    border-collapse: separate
}

.handsontable .table-bordered td,.handsontable .table-bordered th {
    border-left: none
}

.handsontable .table-bordered td:first-child,.handsontable .table-bordered th:first-child {
    border-left: 1px solid #ccc
}

.handsontable .table>tbody>tr>td,.handsontable .table>tbody>tr>th,.handsontable .table>tfoot>tr>td,.handsontable .table>tfoot>tr>th,.handsontable .table>thead>tr>td,.handsontable .table>thead>tr>th {
    line-height: 21px;
    padding: 0 4px
}

.col-lg-1.handsontable,.col-lg-2.handsontable,.col-lg-3.handsontable,.col-lg-4.handsontable,.col-lg-5.handsontable,.col-lg-6.handsontable,.col-lg-7.handsontable,.col-lg-8.handsontable,.col-lg-9.handsontable,.col-lg-10.handsontable,.col-lg-11.handsontable,.col-lg-12.handsontable,.col-md-1.handsontable,.col-md-2.handsontable,.col-md-3.handsontable,.col-md-4.handsontable,.col-md-5.handsontable,.col-md-6.handsontable,.col-md-7.handsontable,.col-md-8.handsontable,.col-md-9.handsontable .col-sm-1.handsontable,.col-md-10.handsontable,.col-md-11.handsontable,.col-md-12.handsontable,.col-sm-2.handsontable,.col-sm-3.handsontable,.col-sm-4.handsontable,.col-sm-5.handsontable,.col-sm-6.handsontable,.col-sm-7.handsontable,.col-sm-8.handsontable,.col-sm-9.handsontable .col-xs-1.handsontable,.col-sm-10.handsontable,.col-sm-11.handsontable,.col-sm-12.handsontable,.col-xs-2.handsontable,.col-xs-3.handsontable,.col-xs-4.handsontable,.col-xs-5.handsontable,.col-xs-6.handsontable,.col-xs-7.handsontable,.col-xs-8.handsontable,.col-xs-9.handsontable,.col-xs-10.handsontable,.col-xs-11.handsontable,.col-xs-12.handsontable {
    padding-left: 0;
    padding-right: 0
}

.handsontable .table-striped>tbody>tr:nth-of-type(2n) {
    background-color: #fff
}

.handsontable {
    position: relative
}

.handsontable .hide {
    display: none
}

.handsontable .relative {
    position: relative
}

.handsontable.htAutoSize {
    visibility: hidden;
    left: -99000px;
    position: absolute;
    top: -99000px
}

.handsontable .wtHider {
    width: 0
}

.handsontable .wtSpreader {
    position: relative;
    width: 0;
    height: auto
}

.handsontable div,.handsontable input,.handsontable table,.handsontable tbody,.handsontable td,.handsontable textarea,.handsontable th,.handsontable thead {
    box-sizing: content-box;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box
}

.handsontable input,.handsontable textarea {
    min-height: 0
}

.handsontable table.htCore {
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
    border-width: 0;
    table-layout: fixed;
    width: 0;
    outline-width: 0;
    cursor: default;
    max-width: none;
    max-height: none
}

.handsontable col,.handsontable col.rowHeader {
    width: 50px
}

.handsontable td,.handsontable th {
    border-top-width: 0;
    border-left-width: 0;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    height: 22px;
    empty-cells: show;
    line-height: 21px;
    padding: 0 4px;
    background-color: #fff;
    vertical-align: top;
    overflow: hidden;
    outline-width: 0;
    white-space: pre-line;
    background-clip: padding-box
}

.handsontable td.htInvalid {
    background-color: #ff4c42!important
}

.handsontable td.htNoWrap {
    white-space: nowrap
}

.handsontable th:last-child {
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc
}

.handsontable th.htNoFrame,.handsontable th:first-child.htNoFrame,.handsontable tr:first-child th.htNoFrame {
    border-left-width: 0;
    background-color: #fff;
    border-color: #fff
}

.handsontable .htNoFrame+td,.handsontable .htNoFrame+th,.handsontable.htRowHeaders thead tr th:nth-child(2),.handsontable td:first-of-type,.handsontable th:first-child,.handsontable th:nth-child(2) {
    border-left: 1px solid #ccc
}

.handsontable tr:first-child td,.handsontable tr:first-child th {
    border-top: 1px solid #ccc
}

.ht_master:not(.innerBorderLeft):not(.emptyColumns)~.handsontable:not(.ht_clone_top) thead tr th:first-child,.ht_master:not(.innerBorderLeft):not(.emptyColumns)~.handsontable tbody tr th {
    border-right-width: 0
}

.ht_master:not(.innerBorderTop) thead tr.lastChild th,.ht_master:not(.innerBorderTop) thead tr:last-child th,.ht_master:not(.innerBorderTop)~.handsontable thead tr.lastChild th,.ht_master:not(.innerBorderTop)~.handsontable thead tr:last-child th {
    border-bottom-width: 0
}

.handsontable th {
    background-color: #f0f0f0;
    color: #222;
    text-align: center;
    font-weight: 400;
    white-space: nowrap
}

.handsontable thead th {
    padding: 0
}

.handsontable th.active {
    background-color: #ccc
}

.handsontable thead th .relative {
    padding: 2px 4px
}

#hot-display-license-info {
    font-size: 10px;
    color: #323232;
    padding: 5px 0 3px;
    font-family: Helvetica,Arial,sans-serif;
    text-align: left
}

#hot-display-license-info a {
    font-size: 10px
}

.handsontable .manualColumnResizer {
    position: fixed;
    top: 0;
    cursor: col-resize;
    z-index: 110;
    width: 5px;
    height: 25px
}

.handsontable .manualRowResizer {
    position: fixed;
    left: 0;
    cursor: row-resize;
    z-index: 110;
    height: 5px;
    width: 50px
}

.handsontable .manualColumnResizer.active,.handsontable .manualColumnResizer:hover,.handsontable .manualRowResizer.active,.handsontable .manualRowResizer:hover {
    background-color: #34a9db
}

.handsontable .manualColumnResizerGuide {
    position: fixed;
    right: 0;
    top: 0;
    background-color: #34a9db;
    display: none;
    width: 0;
    border-right: 1px dashed #777;
    margin-left: 5px
}

.handsontable .manualRowResizerGuide {
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: #34a9db;
    display: none;
    height: 0;
    border-bottom: 1px dashed #777;
    margin-top: 5px
}

.handsontable .manualColumnResizerGuide.active,.handsontable .manualRowResizerGuide.active {
    display: block;
    z-index: 199
}

.handsontable .columnSorting {
    position: relative
}

.handsontable .columnSorting.sortAction:hover {
    text-decoration: underline;
    cursor: pointer
}

.handsontable span.colHeader {
    display: inline-block;
    line-height: 1.1
}

.handsontable span.colHeader.columnSorting:before {
    top: 50%;
    margin-top: -6px;
    padding-left: 8px;
    position: absolute;
    right: -9px;
    content: "";
    height: 10px;
    width: 5px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: right
}

.handsontable span.colHeader.columnSorting.ascending:before {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFNJREFUeAHtzjkSgCAUBNHPgsoy97+ulGXRqJE5L+xkxoYt2UdsLb5bqFINz+aLuuLn5rIu2RkO3fZpWENimNgiw6iBYRTPMLJjGFxQZ1hxxb/xBI1qC8k39CdKAAAAAElFTkSuQmCC")
}

.handsontable span.colHeader.columnSorting.descending:before {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFJJREFUeAHtzjkSgCAQRNFmQYUZ7n9dKUvru0TmvPAn3br0QfgdZ5xx6x+rQn23GqTYnq1FDcnuzZIO2WmedVqIRVxgGKEyjNgYRjKGkZ1hFIZ3I70LyM0VtU8AAAAASUVORK5CYII=")
}

.htGhostTable .htCore span.colHeader.columnSorting:not(.indicatorDisabled):after {
    content: "*";
    display: inline-block;
    position: relative;
    padding-right: 20px
}

.handsontable .wtBorder {
    position: absolute;
    font-size: 0
}

.handsontable .wtBorder.hidden {
    display: none!important
}

.handsontable .wtBorder.current {
    z-index: 10
}

.handsontable .wtBorder.area {
    z-index: 8
}

.handsontable .wtBorder.fill {
    z-index: 6
}

.handsontable td.area,.handsontable td.area-1,.handsontable td.area-2,.handsontable td.area-3,.handsontable td.area-4,.handsontable td.area-5,.handsontable td.area-6,.handsontable td.area-7 {
    position: relative
}

.handsontable td.area-1:before,.handsontable td.area-2:before,.handsontable td.area-3:before,.handsontable td.area-4:before,.handsontable td.area-5:before,.handsontable td.area-6:before,.handsontable td.area-7:before,.handsontable td.area:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    bottom: -100%\9;
    background: #005eff
}

@media (-ms-high-contrast:none),screen and (-ms-high-contrast:active) {
    .handsontable td.area-1:before,.handsontable td.area-2:before,.handsontable td.area-3:before,.handsontable td.area-4:before,.handsontable td.area-5:before,.handsontable td.area-6:before,.handsontable td.area-7:before,.handsontable td.area:before {
        bottom: -100%
    }
}

.handsontable td.area:before {
    opacity: .1
}

.handsontable td.area-1:before {
    opacity: .2
}

.handsontable td.area-2:before {
    opacity: .27
}

.handsontable td.area-3:before {
    opacity: .35
}

.handsontable td.area-4:before {
    opacity: .41
}

.handsontable td.area-5:before {
    opacity: .47
}

.handsontable td.area-6:before {
    opacity: .54
}

.handsontable td.area-7:before {
    opacity: .58
}

.handsontable tbody th.ht__highlight,.handsontable thead th.ht__highlight {
    background-color: #dcdcdc
}

.handsontable tbody th.ht__active_highlight,.handsontable thead th.ht__active_highlight {
    background-color: #8eb0e7;
    color: #000
}

.handsontable .wtBorder.corner {
    font-size: 0;
    cursor: crosshair
}

.handsontable .htBorder.htFillBorder {
    background: red;
    width: 1px;
    height: 1px
}

.handsontableInput {
    border: none;
    outline-width: 0;
    margin: 0;
    padding: 1px 5px 0;
    font-family: inherit;
    line-height: 21px;
    font-size: inherit;
    box-shadow: inset 0 0 0 2px #5292f7;
    resize: none;
    display: block;
    color: #000;
    border-radius: 0;
    background-color: #fff
}

.handsontableInputHolder {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 104
}

.htSelectEditor {
    -webkit-appearance: menulist-button!important;
    position: absolute;
    width: auto
}

.handsontable .htDimmed {
    color: #777
}

.handsontable .htSubmenu {
    position: relative
}

.handsontable .htSubmenu :after {
    content: "\25B6";
    color: #777;
    position: absolute;
    right: 5px;
    font-size: 9px
}

.handsontable .htLeft {
    text-align: left
}

.handsontable .htCenter {
    text-align: center
}

.handsontable .htRight {
    text-align: right
}

.handsontable .htJustify {
    text-align: justify
}

.handsontable .htTop {
    vertical-align: top
}

.handsontable .htMiddle {
    vertical-align: middle
}

.handsontable .htBottom {
    vertical-align: bottom
}

.handsontable .htPlaceholder {
    color: #999
}

.handsontable .htAutocompleteArrow {
    float: right;
    font-size: 10px;
    color: #eee;
    cursor: default;
    width: 16px;
    text-align: center
}

.handsontable td .htAutocompleteArrow:hover {
    color: #777
}

.handsontable td.area .htAutocompleteArrow {
    color: #d3d3d3
}

.handsontable .htCheckboxRendererInput {
    display: inline-block
}

.handsontable .htCheckboxRendererInput.noValue {
    opacity: .5
}

.handsontable .htCheckboxRendererLabel {
    cursor: pointer;
    display: inline-block;
    width: 100%
}

.handsontable .handsontable.ht_clone_top .wtHider {
    padding: 0 0 5px
}

.handsontable .autocompleteEditor.handsontable {
    padding-right: 17px
}

.handsontable .autocompleteEditor.handsontable.htMacScroll {
    padding-right: 15px
}

.handsontable.listbox {
    margin: 0
}

.handsontable.listbox .ht_master table {
    border: 1px solid #ccc;
    border-collapse: separate;
    background: #fff
}

.handsontable.listbox td,.handsontable.listbox th,.handsontable.listbox tr:first-child td,.handsontable.listbox tr:first-child th,.handsontable.listbox tr:last-child th {
    border-color: transparent
}

.handsontable.listbox td,.handsontable.listbox th {
    white-space: nowrap;
    text-overflow: ellipsis
}

.handsontable.listbox td.htDimmed {
    cursor: default;
    color: inherit;
    font-style: inherit
}

.handsontable.listbox .wtBorder {
    visibility: hidden
}

.handsontable.listbox tr:hover td,.handsontable.listbox tr td.current {
    background: #eee
}

.ht_clone_top {
    z-index: 101
}

.ht_clone_left {
    z-index: 102
}

.ht_clone_bottom_left_corner,.ht_clone_debug,.ht_clone_top_left_corner {
    z-index: 103
}

.handsontable td.htSearchResult {
    background: #fcedd9;
    color: #583707
}

.htBordered {
    border-width: 1px
}

.htBordered.htTopBorderSolid {
    border-top-style: solid;
    border-top-color: #000
}

.htBordered.htRightBorderSolid {
    border-right-style: solid;
    border-right-color: #000
}

.htBordered.htBottomBorderSolid {
    border-bottom-style: solid;
    border-bottom-color: #000
}

.htBordered.htLeftBorderSolid {
    border-left-style: solid;
    border-left-color: #000
}

.handsontable tbody tr th:nth-last-child(2) {
    border-right: 1px solid #ccc
}

.handsontable thead tr:nth-last-child(2) th.htGroupIndicatorContainer {
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px
}

.ht_clone_top_left_corner thead tr th:nth-last-child(2) {
    border-right: 1px solid #ccc
}

.htCollapseButton {
    width: 10px;
    height: 10px;
    line-height: 10px;
    text-align: center;
    border-radius: 5px;
    border: 1px solid #f3f3f3;
    box-shadow: 1px 1px 3px rgba(0,0,0,.4);
    cursor: pointer;
    margin-bottom: 3px;
    position: relative
}

.htCollapseButton:after {
    content: "";
    height: 300%;
    width: 1px;
    display: block;
    background: #ccc;
    margin-left: 4px;
    position: absolute;
    bottom: 10px
}

thead .htCollapseButton {
    right: 5px;
    position: absolute;
    top: 5px;
    background: #fff
}

thead .htCollapseButton:after {
    height: 1px;
    width: 700%;
    right: 10px;
    top: 4px
}

.handsontable tr th .htExpandButton {
    position: absolute;
    width: 10px;
    height: 10px;
    line-height: 10px;
    text-align: center;
    border-radius: 5px;
    border: 1px solid #f3f3f3;
    box-shadow: 1px 1px 3px rgba(0,0,0,.4);
    cursor: pointer;
    top: 0;
    display: none
}

.handsontable thead tr th .htExpandButton {
    top: 5px
}

.handsontable tr th .htExpandButton.clickable {
    display: block
}

.collapsibleIndicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 5px;
    border: 1px solid #a6a6a6;
    line-height: 10px;
    color: #222;
    border-radius: 10px;
    font-size: 10px;
    width: 10px;
    height: 10px;
    cursor: pointer;
    box-shadow: 0 0 0 6px #eee;
    background: #eee
}

.handsontable col.hidden {
    width: 0!important
}

.handsontable table tr th.lightRightBorder {
    border-right: 1px solid #e6e6e6
}

.handsontable tr.hidden,.handsontable tr.hidden td,.handsontable tr.hidden th {
    display: none
}

.ht_clone_bottom,.ht_clone_left,.ht_clone_top,.ht_master {
    overflow: hidden
}

.ht_master .wtHolder {
    overflow: auto
}

.handsontable .ht_clone_left thead,.handsontable .ht_master thead,.handsontable .ht_master tr th {
    visibility: hidden
}

.ht_clone_bottom .wtHolder,.ht_clone_left .wtHolder,.ht_clone_top .wtHolder {
    overflow: hidden
}

.handsontable.mobile,.handsontable.mobile .wtHolder {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-overflow-scrolling: touch
}

.htMobileEditorContainer {
    display: none;
    position: absolute;
    top: 0;
    width: 70%;
    height: 54pt;
    background: #f8f8f8;
    border-radius: 20px;
    border: 1px solid #ebebeb;
    z-index: 999;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -webkit-text-size-adjust: none
}

.topLeftSelectionHandle-HitArea:not(.ht_master .topLeftSelectionHandle-HitArea),.topLeftSelectionHandle:not(.ht_master .topLeftSelectionHandle) {
    z-index: 9999
}

.bottomRightSelectionHandle,.bottomRightSelectionHandle-HitArea,.topLeftSelectionHandle,.topLeftSelectionHandle-HitArea {
    left: -10000px;
    top: -10000px
}

.htMobileEditorContainer.active {
    display: block
}

.htMobileEditorContainer .inputs {
    position: absolute;
    right: 210pt;
    bottom: 10pt;
    top: 10pt;
    left: 14px;
    height: 34pt
}

.htMobileEditorContainer .inputs textarea {
    font-size: 13pt;
    border: 1px solid #a1a1a1;
    -webkit-appearance: none;
    box-shadow: none;
    position: absolute;
    left: 14px;
    right: 14px;
    top: 0;
    bottom: 0;
    padding: 7pt
}

.htMobileEditorContainer .cellPointer {
    position: absolute;
    top: -13pt;
    height: 0;
    width: 0;
    left: 30px;
    border-left: 13pt solid transparent;
    border-right: 13pt solid transparent;
    border-bottom: 13pt solid #ebebeb
}

.htMobileEditorContainer .cellPointer.hidden {
    display: none
}

.htMobileEditorContainer .cellPointer:before {
    content: "";
    display: block;
    position: absolute;
    top: 2px;
    height: 0;
    width: 0;
    left: -13pt;
    border-left: 13pt solid transparent;
    border-right: 13pt solid transparent;
    border-bottom: 13pt solid #f8f8f8
}

.htMobileEditorContainer .moveHandle {
    position: absolute;
    top: 10pt;
    left: 5px;
    width: 30px;
    bottom: 0;
    cursor: move;
    z-index: 9999
}

.htMobileEditorContainer .moveHandle:after {
    content: "..\A..\A..\A..";
    white-space: pre;
    line-height: 10px;
    font-size: 20pt;
    display: inline-block;
    margin-top: -8px;
    color: #ebebeb
}

.htMobileEditorContainer .positionControls {
    width: 205pt;
    position: absolute;
    right: 5pt;
    top: 0;
    bottom: 0
}

.htMobileEditorContainer .positionControls>div {
    width: 50pt;
    height: 100%;
    float: left
}

.htMobileEditorContainer .positionControls>div:after {
    content: " ";
    display: block;
    width: 15pt;
    height: 15pt;
    text-align: center;
    line-height: 50pt
}

.htMobileEditorContainer .downButton:after,.htMobileEditorContainer .leftButton:after,.htMobileEditorContainer .rightButton:after,.htMobileEditorContainer .upButton:after {
    transform-origin: 5pt 5pt;
    -webkit-transform-origin: 5pt 5pt;
    margin: 21pt 0 0 21pt
}

.htMobileEditorContainer .leftButton:after {
    border-top: 2px solid #288ffe;
    border-left: 2px solid #288ffe;
    -webkit-transform: rotate(-45deg)
}

.htMobileEditorContainer .leftButton:active:after {
    border-color: #cfcfcf
}

.htMobileEditorContainer .rightButton:after {
    border-top: 2px solid #288ffe;
    border-left: 2px solid #288ffe;
    -webkit-transform: rotate(135deg)
}

.htMobileEditorContainer .rightButton:active:after {
    border-color: #cfcfcf
}

.htMobileEditorContainer .upButton:after {
    border-top: 2px solid #288ffe;
    border-left: 2px solid #288ffe;
    -webkit-transform: rotate(45deg)
}

.htMobileEditorContainer .upButton:active:after {
    border-color: #cfcfcf
}

.htMobileEditorContainer .downButton:after {
    border-top: 2px solid #288ffe;
    border-left: 2px solid #288ffe;
    -webkit-transform: rotate(225deg)
}

.htMobileEditorContainer .downButton:active:after {
    border-color: #cfcfcf
}

.handsontable.hide-tween {
    animation: opacity-hide .3s;
    animation-fill-mode: forwards;
    -webkit-animation-fill-mode: forwards
}

.handsontable.show-tween {
    animation: opacity-show .3s;
    animation-fill-mode: forwards;
    -webkit-animation-fill-mode: forwards
}

/*!
 * Pikaday
 * Copyright © 2014 David Bushell | BSD & MIT license | http://dbushell.com/
 */
.pika-single {
    z-index: 9999;
    display: block;
    position: relative;
    color: #333;
    background: #fff;
    border: 1px solid #ccc;
    border-bottom-color: #bbb;
    font-family: Helvetica Neue,Helvetica,Arial,sans-serif
}

.pika-single:after,.pika-single:before {
    content: " ";
    display: table
}

.pika-single:after {
    clear: both
}

.pika-single {
    *zoom:1}

.pika-single.is-hidden {
    display: none
}

.pika-single.is-bound {
    position: absolute;
    box-shadow: 0 5px 15px -5px rgba(0,0,0,.5)
}

.pika-lendar {
    float: left;
    width: 240px;
    margin: 8px
}

.pika-title {
    position: relative;
    text-align: center
}

.pika-label {
    display: inline-block;
    *display: inline;
    position: relative;
    z-index: 9999;
    overflow: hidden;
    margin: 0;
    padding: 5px 3px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    background-color: #fff
}

.pika-title select {
    cursor: pointer;
    position: absolute;
    z-index: 9998;
    margin: 0;
    left: 0;
    top: 5px;
    filter: alpha(opacity=0);
    opacity: 0
}

.pika-next,.pika-prev {
    display: block;
    cursor: pointer;
    position: relative;
    outline: none;
    border: 0;
    padding: 0;
    width: 20px;
    height: 30px;
    text-indent: 20px;
    white-space: nowrap;
    overflow: hidden;
    background-color: transparent;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 75% 75%;
    opacity: .5;
    *position: absolute;
    *top: 0
}

.pika-next:hover,.pika-prev:hover {
    opacity: 1
}

.is-rtl .pika-next,.pika-prev {
    float: left;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAUklEQVR42u3VMQoAIBADQf8Pgj+OD9hG2CtONJB2ymQkKe0HbwAP0xucDiQWARITIDEBEnMgMQ8S8+AqBIl6kKgHiXqQqAeJepBo/z38J/U0uAHlaBkBl9I4GwAAAABJRU5ErkJggg==");
    *left: 0
}

.is-rtl .pika-prev,.pika-next {
    float: right;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAU0lEQVR42u3VOwoAMAgE0dwfAnNjU26bYkBCFGwfiL9VVWoO+BJ4Gf3gtsEKKoFBNTCoCAYVwaAiGNQGMUHMkjGbgjk2mIONuXo0nC8XnCf1JXgArVIZAQh5TKYAAAAASUVORK5CYII=");
    *right: 0
}

.pika-next.is-disabled,.pika-prev.is-disabled {
    cursor: default;
    opacity: .2
}

.pika-select {
    display: inline-block;
    *display: inline
}

.pika-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    border: 0
}

.pika-table td,.pika-table th {
    width: 14.285714285714286%;
    padding: 0
}

.pika-table th {
    color: #999;
    font-size: 12px;
    line-height: 25px;
    font-weight: 700;
    text-align: center
}

.pika-button {
    cursor: pointer;
    display: block;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    outline: none;
    border: 0;
    margin: 0;
    width: 100%;
    padding: 5px;
    color: #666;
    font-size: 12px;
    line-height: 15px;
    text-align: right;
    background: #f5f5f5
}

.pika-week {
    font-size: 11px;
    color: #999
}

.is-today .pika-button {
    color: #3af;
    font-weight: 700
}

.is-selected .pika-button {
    color: #fff;
    font-weight: 700;
    background: #3af;
    box-shadow: inset 0 1px 3px #178fe5;
    border-radius: 3px
}

.is-inrange .pika-button {
    background: #d5e9f7
}

.is-startrange .pika-button {
    color: #fff;
    background: #6cb31d;
    box-shadow: none;
    border-radius: 3px
}

.is-endrange .pika-button {
    color: #fff;
    background: #3af;
    box-shadow: none;
    border-radius: 3px
}

.is-disabled .pika-button,.is-outside-current-month .pika-button {
    pointer-events: none;
    cursor: default;
    color: #999;
    opacity: .3
}

.pika-button:hover {
    color: #fff;
    background: #ff8000;
    box-shadow: none;
    border-radius: 3px
}

.pika-table abbr {
    border-bottom: none;
    cursor: help
}

.htCommentCell {
    position: relative
}

.htCommentCell:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    border-left: 6px solid transparent;
    border-top: 6px solid #000
}

.htComments {
    display: none;
    z-index: 1059;
    position: absolute
}

.htCommentTextArea {
    box-shadow: 0 1px 3px rgba(0,0,0,.117647),0 1px 2px rgba(0,0,0,.239216);
    box-sizing: border-box;
    border: none;
    border-left: 3px solid #ccc;
    background-color: #fff;
    width: 215px;
    height: 90px;
    font-size: 12px;
    padding: 5px;
    outline: 0!important;
    -webkit-appearance: none
}

.htCommentTextArea:focus {
    box-shadow: 0 1px 3px rgba(0,0,0,.117647),0 1px 2px rgba(0,0,0,.239216),inset 0 0 0 1px #5292f7;
    border-left: 3px solid #5292f7
}

/*!
 * Handsontable ContextMenu
 */
.htContextMenu:not(.htGhostTable) {
    display: none;
    position: absolute;
    z-index: 1060
}

.htContextMenu .ht_clone_corner,.htContextMenu .ht_clone_debug,.htContextMenu .ht_clone_left,.htContextMenu .ht_clone_top {
    display: none
}

.htContextMenu table.htCore {
    border: 1px solid #ccc;
    border-bottom-width: 2px;
    border-right-width: 2px
}

.htContextMenu .wtBorder {
    visibility: hidden
}

.htContextMenu table tbody tr td {
    background: #fff;
    border-width: 0;
    padding: 4px 6px 0;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.htContextMenu table tbody tr td:first-child {
    border: 0
}

.htContextMenu table tbody tr td.htDimmed {
    font-style: normal;
    color: #323232
}

.htContextMenu table tbody tr td.current,.htContextMenu table tbody tr td.zeroclipboard-is-hover {
    background: #f3f3f3
}

.htContextMenu table tbody tr td.htSeparator {
    border-top: 1px solid #e6e6e6;
    height: 0;
    padding: 0;
    cursor: default
}

.htContextMenu table tbody tr td.htDisabled {
    color: #999;
    cursor: default
}

.htContextMenu table tbody tr td.htDisabled:hover {
    background: #fff;
    color: #999;
    cursor: default
}

.htContextMenu table tbody tr.htHidden {
    display: none
}

.htContextMenu table tbody tr td .htItemWrapper {
    margin-left: 10px;
    margin-right: 6px
}

.htContextMenu table tbody tr td div span.selected {
    margin-top: -2px;
    position: absolute;
    left: 4px
}

.htContextMenu .ht_master .wtHolder {
    overflow: hidden
}

textarea#HandsontableCopyPaste {
    position: fixed!important;
    top: 0!important;
    right: 100%!important;
    overflow: hidden;
    opacity: 0;
    outline: 0 none!important
}

.htRowHeaders .ht_master.innerBorderLeft~.ht_clone_left td:first-of-type,.htRowHeaders .ht_master.innerBorderLeft~.ht_clone_top_left_corner th:nth-child(2) {
    border-left: 0 none
}

.handsontable.ht__manualColumnMove.after-selection--columns thead th.ht__highlight {
    cursor: move;
    cursor: grab
}

.handsontable.ht__manualColumnMove.on-moving--columns,.handsontable.ht__manualColumnMove.on-moving--columns thead th.ht__highlight {
    cursor: move;
    cursor: grabbing
}

.handsontable.ht__manualColumnMove.on-moving--columns .manualColumnResizer {
    display: none
}

.handsontable .ht__manualColumnMove--backlight,.handsontable .ht__manualColumnMove--guideline {
    position: absolute;
    height: 100%;
    display: none
}

.handsontable .ht__manualColumnMove--guideline {
    background: #757575;
    width: 2px;
    top: 0;
    margin-left: -1px;
    z-index: 105
}

.handsontable .ht__manualColumnMove--backlight {
    background: #343434;
    background: rgba(52,52,52,.25);
    display: none;
    z-index: 105;
    pointer-events: none
}

.handsontable.on-moving--columns .ht__manualColumnMove--backlight,.handsontable.on-moving--columns.show-ui .ht__manualColumnMove--guideline {
    display: block
}

.handsontable .wtHider {
    position: relative
}

.handsontable.ht__manualRowMove.after-selection--rows tbody th.ht__highlight {
    cursor: move;
    cursor: grab
}

.handsontable.ht__manualRowMove.on-moving--rows,.handsontable.ht__manualRowMove.on-moving--rows tbody th.ht__highlight {
    cursor: move;
    cursor: grabbing
}

.handsontable.ht__manualRowMove.on-moving--rows .manualRowResizer {
    display: none
}

.handsontable .ht__manualRowMove--backlight,.handsontable .ht__manualRowMove--guideline {
    position: absolute;
    width: 100%;
    display: none
}

.handsontable .ht__manualRowMove--guideline {
    background: #757575;
    height: 2px;
    left: 0;
    margin-top: -1px;
    z-index: 105
}

.handsontable .ht__manualRowMove--backlight {
    background: #343434;
    background: rgba(52,52,52,.25);
    display: none;
    z-index: 105;
    pointer-events: none
}

.handsontable.on-moving--rows .ht__manualRowMove--backlight,.handsontable.on-moving--rows.show-ui .ht__manualRowMove--guideline {
    display: block
}

.handsontable tbody td[rowspan][class*=area][class*=highlight]:not([class*=fullySelectedMergedCell]):before {
    opacity: 0
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-0]:before,.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-multiple]:before {
    opacity: .1
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-1]:before {
    opacity: .2
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-2]:before {
    opacity: .27
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-3]:before {
    opacity: .35
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-4]:before {
    opacity: .41
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-5]:before {
    opacity: .47
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-6]:before {
    opacity: .54
}

.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-7]:before {
    opacity: .58
}

/*!
 * Handsontable DropdownMenu
 */
.handsontable .changeType {
    background: #eee;
    border-radius: 2px;
    border: 1px solid #bbb;
    color: #bbb;
    font-size: 9px;
    line-height: 9px;
    padding: 2px;
    margin: 3px 1px 0 5px;
    float: right
}

.handsontable .changeType:before {
    content: "\25BC   "
}

.handsontable .changeType:hover {
    border: 1px solid #777;
    color: #777;
    cursor: pointer
}

.htDropdownMenu:not(.htGhostTable) {
    display: none;
    position: absolute;
    z-index: 9999
}

.htDropdownMenu .ht_clone_corner,.htDropdownMenu .ht_clone_debug,.htDropdownMenu .ht_clone_left,.htDropdownMenu .ht_clone_top {
    display: none
}

.htDropdownMenu table.htCore {
    border: 1px solid #bbb;
    border-bottom-width: 2px;
    border-right-width: 2px
}

.htDropdownMenu .wtBorder {
    visibility: hidden
}

.htDropdownMenu table tbody tr td {
    background: #fff;
    border-width: 0;
    padding: 4px 6px 0;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.htDropdownMenu table tbody tr td:first-child {
    border: 0
}

.htDropdownMenu table tbody tr td.htDimmed {
    font-style: normal;
    color: #323232
}

.htDropdownMenu table tbody tr td.current,.htDropdownMenu table tbody tr td.zeroclipboard-is-hover {
    background: #e9e9e9
}

.htDropdownMenu table tbody tr td.htSeparator {
    border-top: 1px solid #e6e6e6;
    height: 0;
    padding: 0;
    cursor: default
}

.htDropdownMenu table tbody tr td.htDisabled {
    color: #999
}

.htDropdownMenu table tbody tr td.htDisabled:hover {
    background: #fff;
    color: #999;
    cursor: default
}

.htDropdownMenu:not(.htGhostTable) table tbody tr.htHidden {
    display: none
}

.htDropdownMenu table tbody tr td .htItemWrapper {
    margin-left: 10px;
    margin-right: 10px
}

.htDropdownMenu table tbody tr td div span.selected {
    margin-top: -2px;
    position: absolute;
    left: 4px
}

.htDropdownMenu .ht_master .wtHolder {
    overflow: hidden
}

.handsontable span.colHeader.columnSorting:after {
    top: 50%;
    margin-top: -2px;
    position: absolute;
    right: -15px;
    padding-left: 5px;
    font-size: 8px;
    height: 8px;
    line-height: 1.1;
    text-decoration: underline;
    text-decoration: none
}

.handsontable span.colHeader.columnSorting[class*=" sort-"]:after,.handsontable span.colHeader.columnSorting[class^=sort-]:after {
    content: "+"
}

.handsontable span.colHeader.columnSorting.sort-1:after {
    content: "1"
}

.handsontable span.colHeader.columnSorting.sort-2:after {
    content: "2"
}

.handsontable span.colHeader.columnSorting.sort-3:after {
    content: "3"
}

.handsontable span.colHeader.columnSorting.sort-4:after {
    content: "4"
}

.handsontable span.colHeader.columnSorting.sort-5:after {
    content: "5"
}

.handsontable span.colHeader.columnSorting.sort-6:after {
    content: "6"
}

.handsontable span.colHeader.columnSorting.sort-7:after {
    content: "7"
}

.htGhostTable th div button.changeType+span.colHeader.columnSorting:not(.indicatorDisabled) {
    padding-right: 5px
}

/*!
 * Handsontable Filters
 */
.htFiltersConditionsMenu:not(.htGhostTable) {
    display: none;
    position: absolute;
    z-index: 10000
}

.htFiltersConditionsMenu .ht_clone_corner,.htFiltersConditionsMenu .ht_clone_debug,.htFiltersConditionsMenu .ht_clone_left,.htFiltersConditionsMenu .ht_clone_top {
    display: none
}

.htFiltersConditionsMenu table.htCore {
    border: 1px solid #bbb;
    border-bottom-width: 2px;
    border-right-width: 2px
}

.htFiltersConditionsMenu .wtBorder {
    visibility: hidden
}

.htFiltersConditionsMenu table tbody tr td {
    background: #fff;
    border-width: 0;
    padding: 4px 6px 0;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.htFiltersConditionsMenu table tbody tr td:first-child {
    border: 0
}

.htFiltersConditionsMenu table tbody tr td.htDimmed {
    font-style: normal;
    color: #323232
}

.htFiltersConditionsMenu table tbody tr td.current,.htFiltersConditionsMenu table tbody tr td.zeroclipboard-is-hover {
    background: #e9e9e9
}

.htFiltersConditionsMenu table tbody tr td.htSeparator {
    border-top: 1px solid #e6e6e6;
    height: 0;
    padding: 0
}

.htFiltersConditionsMenu table tbody tr td.htDisabled {
    color: #999
}

.htFiltersConditionsMenu table tbody tr td.htDisabled:hover {
    background: #fff;
    color: #999;
    cursor: default
}

.htFiltersConditionsMenu table tbody tr td .htItemWrapper {
    margin-left: 10px;
    margin-right: 10px
}

.htFiltersConditionsMenu table tbody tr td div span.selected {
    margin-top: -2px;
    position: absolute;
    left: 4px
}

.htFiltersConditionsMenu .ht_master .wtHolder {
    overflow: hidden
}

.handsontable .htMenuFiltering {
    border-bottom: 1px dotted #ccc;
    height: 135px;
    overflow: hidden
}

.handsontable .ht_master table td.htCustomMenuRenderer {
    background-color: #fff;
    cursor: auto
}

.handsontable .htFiltersMenuLabel {
    font-size: 12px
}

.handsontable .htFiltersMenuActionBar {
    text-align: center;
    padding-top: 10px;
    padding-bottom: 3px
}

.handsontable .htFiltersMenuCondition.border {
    border-bottom: 1px dotted #ccc!important
}

.handsontable .htFiltersMenuCondition .htUIInput {
    padding: 0 0 5px
}

.handsontable .htFiltersMenuValue {
    border-bottom: 1px dotted #ccc!important
}

.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch {
    padding: 0
}

.handsontable .htFiltersMenuCondition .htUIInput input,.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input {
    padding: 4px;
    box-sizing: border-box;
    width: 100%
}

.htUIMultipleSelect .ht_master .wtHolder {
    overflow-y: scroll
}

.handsontable .htFiltersActive .changeType {
    border: 1px solid #509272;
    color: #18804e;
    background-color: #d2e0d9
}

.handsontable .htUISelectAll {
    margin-right: 10px
}

.handsontable .htUIClearAll,.handsontable .htUISelectAll {
    display: inline-block
}

.handsontable .htUIClearAll a,.handsontable .htUISelectAll a {
    color: #3283d8;
    font-size: 12px
}

.handsontable .htUISelectionControls {
    text-align: right
}

.handsontable .htCheckboxRendererInput {
    margin: 0 5px 0 0;
    vertical-align: middle;
    height: 1em
}

.handsontable .htUIInput {
    padding: 3px 0 7px;
    position: relative;
    text-align: center
}

.handsontable .htUIInput input {
    border-radius: 2px;
    border: 1px solid #d2d1d1
}

.handsontable .htUIInput input:focus {
    outline: 0
}

.handsontable .htUIInputIcon {
    position: absolute
}

.handsontable .htUIInput.htUIButton {
    cursor: pointer;
    display: inline-block
}

.handsontable .htUIInput.htUIButton input {
    background-color: #eee;
    color: #000;
    cursor: pointer;
    font-family: arial,sans-serif;
    font-size: 11px;
    font-weight: 700;
    height: 19px;
    min-width: 64px
}

.handsontable .htUIInput.htUIButton input:hover {
    border-color: #b9b9b9
}

.handsontable .htUIInput.htUIButtonOK {
    margin-right: 10px
}

.handsontable .htUIInput.htUIButtonOK input {
    background-color: #0f9d58;
    border-color: #18804e;
    color: #fff
}

.handsontable .htUIInput.htUIButtonOK input:hover {
    border-color: #1a6f46
}

.handsontable .htUISelect {
    cursor: pointer;
    margin-bottom: 7px;
    position: relative
}

.handsontable .htUISelectCaption {
    background-color: #e8e8e8;
    border-radius: 2px;
    border: 1px solid #d2d1d1;
    font-family: arial,sans-serif;
    font-size: 11px;
    font-weight: 700;
    padding: 3px 20px 3px 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.handsontable .htUISelectCaption:hover {
    background-color: #e8e8e8;
    border: 1px solid #b9b9b9
}

.handsontable .htUISelectDropdown:after {
    content: "\25B2";
    font-size: 7px;
    position: absolute;
    right: 10px;
    top: 0
}

.handsontable .htUISelectDropdown:before {
    content: "\25BC";
    font-size: 7px;
    position: absolute;
    right: 10px;
    top: 8px
}

.handsontable .htUIMultipleSelect .handsontable .htCore {
    border: none
}

.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {
    background-color: #f5f5f5
}

.handsontable .htUIMultipleSelectSearch input {
    border-radius: 2px;
    border: 1px solid #d2d1d1;
    padding: 3px
}

.handsontable .htUIRadio {
    display: inline-block;
    margin-right: 5px;
    height: 100%
}

.handsontable .htUIRadio:last-child {
    margin-right: 0
}

.handsontable .htUIRadio>input[type=radio] {
    margin-right: .5ex
}

.handsontable .htFiltersMenuOperators {
    padding-bottom: 5px
}

.handsontable.ganttChart tr:first-child th div.relative {
    padding-right: 21px
}

.handsontable.ganttChart .colHeader {
    display: block
}

.handsontable.ganttChart td.rangeBar {
    background: #48b703;
    border-right-width: 0;
    position: relative;
    box-shadow: inset 0 3px 0 #fff
}

.handsontable.ganttChart td.rangeBar.last {
    border-right-width: 1px
}

.handsontable.ganttChart td.rangeBar.area {
    background: #7ec481
}

.handsontable.ganttChart td.rangeBar.partial {
    background: #8edf5a
}

.handsontable.ganttChart td.rangeBar.area.partial {
    background: #a1d8ad
}

.handsontable thead th.hiddenHeader:not(:first-of-type) {
    display: none
}

.handsontable th.ht_nestingLevels {
    text-align: left;
    padding-left: 7px
}

.handsontable th div.ht_nestingLevels {
    display: inline-block;
    position: absolute;
    left: 11px
}

.handsontable.innerBorderLeft th div.ht_nestingLevels,.handsontable.innerBorderLeft~.handsontable th div.ht_nestingLevels {
    right: 10px
}

.handsontable th span.ht_nestingLevel {
    display: inline-block
}

.handsontable th span.ht_nestingLevel_empty {
    display: inline-block;
    width: 10px;
    height: 1px;
    float: left
}

.handsontable th span.ht_nestingLevel:after {
    content: "\2510";
    font-size: 9px;
    display: inline-block;
    position: relative;
    bottom: 3px
}

.handsontable th div.ht_nestingButton {
    display: inline-block;
    position: absolute;
    right: -2px;
    cursor: pointer
}

.handsontable th div.ht_nestingButton.ht_nestingExpand:after {
    content: "+"
}

.handsontable th div.ht_nestingButton.ht_nestingCollapse:after {
    content: "-"
}

.handsontable.innerBorderLeft th div.ht_nestingButton,.handsontable.innerBorderLeft~.handsontable th div.ht_nestingButton {
    right: 0
}

.handsontable th.beforeHiddenColumn {
    position: relative
}

.handsontable th.afterHiddenColumn:before,.handsontable th.beforeHiddenColumn:after {
    color: #bbb;
    position: absolute;
    top: 50%;
    font-size: 5pt;
    transform: translateY(-50%)
}

.handsontable th.afterHiddenColumn {
    position: relative
}

.handsontable th.beforeHiddenColumn:after {
    right: 1px;
    content: "\25C0"
}

.handsontable th.afterHiddenColumn:before {
    left: 1px;
    content: "\25B6"
}

.handsontable td.firstVisibleColumn,.handsontable th.firstVisibleColumn {
    border-left: 1px solid #ccc
}

/*!
 * Handsontable HiddenRows
 */
.handsontable th.afterHiddenRow:after,.handsontable th.beforeHiddenRow:before {
    color: #bbb;
    font-size: 6pt;
    line-height: 6pt;
    position: absolute;
    left: 2px
}

.handsontable th.afterHiddenRow,.handsontable th.beforeHiddenRow {
    position: relative
}

.handsontable th.beforeHiddenRow:before {
    content: "\25B2";
    bottom: 2px
}

.handsontable th.afterHiddenRow:after {
    content: "\25BC";
    top: 2px
}

.handsontable.ht__selection--rows tbody th.afterHiddenRow.ht__highlight:after,.handsontable.ht__selection--rows tbody th.beforeHiddenRow.ht__highlight:before {
    color: #eee
}

.handsontable td.afterHiddenRow.firstVisibleRow,.handsontable th.afterHiddenRow.firstVisibleRow {
    border-top: 1px solid #ccc
}
