import fetch from "eem-base/utils/fetch";
import { http } from "@omega/http";
const version = "v1";

// 获取分时方案
export function getTimeShareScheme(data, energyType) {
  return fetch({
    url: `/eem-service/${version}/schemeConfig/timeShareRelationship/node?energyType=${energyType}`,
    method: "POST",
    data: data
  });
}
// 查询折标能耗，传入父层级聚合周期，查询子层级聚合周期数据
export function getSynthesizeConsume(data) {
  return fetch({
    url: `/eem-service/${version}/energy/standard/consumption/child`,
    method: "POST",
    data: data
  });
}
// 获取项目能源类型
export function getProjectEnergy(projectId) {
  return fetch({
    url: `/eem-service/${version}/project/projectEnergy`,
    method: "GET",
    params: {
      projectId
    }
  });
}
// 获取同比环比数据
export function getEnergyTbhb(data) {
  return fetch({
    url: `/eem-service/${version}/energy/consumption/tbhb`,
    method: "POST",
    data: data
  });
}
// 聚合同比环比数据
export function getPolymerizationEnergyTbhb(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energydata/time/tbhb`,
    method: "POST",
    data: data
  });
}
// 获取top5排名
export function getConsumptionTop5(data) {
  return fetch({
    url: `/eem-service/${version}/energy/consumption`,
    method: "POST",
    data: data
  });
}
// 查询子聚合周期分时能耗
export function getTimeChartData(data) {
  return fetch({
    url: `/eem-service/${version}/energy/timeshare/consumption/child/cycle`,
    method: "POST",
    data: data
  });
}
// 查询分时能耗
export function getTimeshareConsumption(data) {
  return fetch({
    url: `/eem-service/${version}/energy/timeshare/consumption`,
    method: "POST",
    data: data
  });
}

// 获取所有指标数据
export function queryEnergyefficiencyset(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getEnergyefficiencyset`,
    method: "POST",
    data
  });
}

// 根据指标类型获取项目节点树
export function queryEnergyTree(data, energyefficiencysetId) {
  return fetch({
    url: `/eem-service/${version}/ef-node/tree?efId=${energyefficiencysetId}`,
    method: "POST",
    data
  });
}
/**
 * 获取多维度节点树 可支持传入末端需要保留的节点
 * @param {*keepNodeTypes} array 传入末端保留的节点的modelLabel，默认不过滤
 * @returns
 */
export function dimensionTreeFilterByEnergytype(data) {
  return fetch({
    url: `/eem-service/${version}/attribute-dimension/tree/filter-by-energytype`,
    method: "POST",
    data
  });
}
// 通过能效配置获取能效数据
export function queryEnergyEfficiencyData(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/query`,
    method: "POST",
    data
  });
}

// 能效对标图表数据
export function queryEnergyEfficiencyDataWithLevel(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/withLevel`,
    method: "POST",
    data
  });
}

// 能效最值数据
export function queryMaxMinAvgEnergyEfficiency(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getMaxMinAvgEnergyEfficiency`,
    method: "POST",
    data
  });
}

// 历史最优值数据
export function queryHistoryOptimumValue(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getHistoryOptimumValue`,
    method: "POST",
    data
  });
}

// 能效对标数据
export function queryEnergyEfficiencyBenchmark(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energyEfficiency/getEnergyEfficiencyBenchmark`,
    method: "POST",
    data
  });
}

export function queryStandardEnergyTypes() {
  return fetch({
    url: `/eem-service/${version}/common/standardEnergyTypes`,
    method: "GET"
  });
}

// 能源流向
export function queryEnergyFlow(data) {
  return fetch({
    url: `/eem-service/${version}/energy/flow/query`,
    method: "POST",
    data
  });
}

export function queryEnergyFlowNoLoading(data) {
  return http({
    url: `/eem-service/${version}/energy/flow/query`,
    method: "POST",
    data
  });
}

//能源流向-根据节点查询关联的节点内容
export function queryEnergyConnection(data) {
  return fetch({
    url: `/eem-service/${version}/connect/connection`,
    method: "POST",
    data
  });
}

// 能源损耗分析
export function queryEnergyLoss(data) {
  return fetch({
    url: `/eem-service/${version}/loss/analysis`,
    method: "POST",
    data
  });
}

// 能源损耗分析
export function queryLossLine(data, params) {
  return fetch({
    url: `/eem-service/${version}/loss/lineLoss`,
    method: "POST",
    data,
    params
  });
}

// 根据管网拓扑结构查询第一层节点
export function queryEnergyLossStartNodes(data) {
  return fetch({
    url: `/eem-service/${version}/loss/startNodes`,
    method: "POST",
    params: data
  });
}

// 获取节点对比数据
export function queryNodeCompare(data) {
  return fetch({
    url: `/eem-service/${version}/group/platform/query/ef/data`,
    method: "POST",
    data
  });
}
// 能耗对比数据
export function energyContrast(data) {
  return fetch({
    url: `/eem-service/${version}/energy/contrast`,
    method: "POST",
    data
  });
}

// 通用迭代v3.5中修改能耗查询中同环比接口查询

// 聚合同比环比数据 (查询中间图表的数据信息)
export function getV2EmergyConsumptionTbhb(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/tbhb`,
    method: "POST",
    data: data
  });
}

// 同期同比环比能耗数据 (查询左下侧两个图表的数据信息)
export function getV2EmergyConsumptionPeriod(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/period`,
    method: "POST",
    data: data
  });
}

// 能耗TOP数据 (查询top排名图表的数据信息)
export function getV2EmergyConsumptionTop(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/top`,
    method: "POST",
    data: data
  });
}

// 专门的折标煤查询接口 (查询中间图表数据)
export function getV2EmergyConsumptionStandard(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/standard`,
    method: "POST",
    data: data
  });
}

// 分时能耗（只有单能耗查询数据,查中间图表的数据）
export function getV2EmergyConsumptionTimeshareCycle(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/timeshare/child/cycle`,
    method: "POST",
    data: data
  });
}

// 分时时段能耗查询（只有单能耗查询数据,查下方图表的数据）
export function getV2EmergyConsumptionTimeshare(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/timeshare`,
    method: "POST",
    data: data
  });
}

// 总能耗详情图表数据查询
export function getV2EmergyConsumptionDetail(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/detail`,
    method: "POST",
    data: data
  });
}

// 节点对比
export function getV2EmergyConsumptionCompare(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/compare`,
    method: "POST",
    data: data
  });
}

// 节点对比导出
export function contrastExportBatch(data) {
  return fetch({
    url: `/eem-service/v1/energy/contrast/export/batch`,
    method: "POST",
    data: data
  });
}

// 综合能耗批量导出
export function energyConsumptionStandardExportBatch(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/standard/export/batch`,
    method: "POST",
    data: data
  });
}

// 同环比批量导出
export function energyConsumptionTbhbExportBatch(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/tbhb/export/batch`,
    method: "POST",
    data: data
  });
}

// 分时批量导出
export function energyConsumptionTimeshareChildCycleExportBatch(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/timeshare/child/cycle/export/batch`,
    method: "POST",
    data: data
  });
}

// 能耗详情批量导出
export function energyConsumptionDetailExportBatch(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/detail/export/batch`,
    method: "POST",
    data: data
  });
}
