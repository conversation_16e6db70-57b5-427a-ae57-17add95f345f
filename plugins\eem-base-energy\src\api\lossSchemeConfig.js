import fetch from "eem-base/utils/fetch";

const version = "v1";

// 查询损耗分摊方案数据
export function getLossShareConfigGroup(data) {
  return fetch({
    url: `/eem-service/${version}/lossShareConfig/group`,
    method: "POST",
    data
  });
}

// 点击操作一栏查询具体方案详情数据
export function getLossShareConfigInfo(id) {
  return fetch({
    url: `/eem-service/${version}/lossShareConfig/info?id=${id}`,
    method: "POST"
  });
}

// 删除损耗分摊方案列表数据
export function deleteLossShareConfig(data, params) {
  return fetch({
    url: `/eem-service/${version}/lossShareConfig/delBatch`,
    method: "DELETE",
    data,
    params
  });
}

// 新增或编辑损耗分摊方案前调用的校验功能
export function checkLossShareConfigSchemeConfig(data) {
  return fetch({
    url: `/eem-service/${version}/lossShareConfig/saveScheme/check`,
    method: "POST",
    data
  });
}

// 新增或编辑保存方案
export function saveLossShareConfigScheme(data) {
  return fetch({
    url: `/eem-service/${version}/lossShareConfig/saveScheme`,
    method: "PUT",
    data
  });
}

// 新增或编辑保存方案
export function getLossShareConfigTopologyChild(data, energyType) {
  return fetch({
    url: `/eem-service/${version}/lossShareConfig/topologyChild?energyType=${energyType}`,
    method: "POST",
    data
  });
}
