import fetch from "eem-base/utils/fetch";
const version = "v1";

//查询多维度用能分析模块tab
export function getOrgList() {
  return fetch({
    url: `/eem-service/${version}/dim/setting/detailList`,
    method: "GET"
  });
}

//查询节点树
export function getNodeList(params) {
  return fetch({
    url: `/eem-service/${version}/node/nodeTree/simple`,
    method: "POST",
    data: params
  });
}

//查询项目的能源类型
export function getEneryType(id) {
  return fetch({
    url: `/eem-service/${version}/project/projectEnergy/order?projectId=${id}`,
    method: "GET"
  });
}

//用能排名数据
export function getEneryRankData(params) {
  return fetch({
    url: `/eem-service/${version}/energy/consumption/top`,
    method: "POST",
    data: params
  });
}

//查询用能趋势
export function getEneryTrendData(params) {
  return fetch({
    url: `/eem-service/${version}/energy/energydata/time/tbhb`,
    method: "POST",
    data: params
  });
}

//多维度用能分析-获取标签数据列表
export function getPropertysList(params) {
  return fetch({
    url: `/eem-service/${version}/dim/setting/propertysByDimId`,
    method: "GET",
    params
  });
}

//多维度用能分析-获取用能分析数据
export function getEachItemized(params) {
  return fetch({
    url: `/eem-service/${version}/energy/energydata/eachItemizedByNode`,
    method: "POST",
    data: params
  });
}

//查询能耗概览同环比数据
export function getConsumption(params) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/radio`,
    method: "POST",
    data: params
  });
}

// 能耗TOP数据 (查询top排名图表的数据信息)
export function getV2EmergyConsumptionTop(data) {
  return fetch({
    url: `/eem-service/v2/energy/consumption/top`,
    method: "POST",
    data: data
  });
}
