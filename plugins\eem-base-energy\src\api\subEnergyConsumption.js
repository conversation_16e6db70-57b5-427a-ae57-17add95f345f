import fetch from "eem-base/utils/fetch";
const version = "v1";

//获取分项能耗同环比数据
export function querySubConsumption(data) {
  return fetch({
    url: `/eem-service/${version}/itemized/energy/radio`,
    method: "POST",
    data
  });
}

//获取分项能耗节点树
export function querySubConsumptionTree(data) {
  return fetch({
    url: `/eem-service/${version}/itemized/energy/node-tree`,
    method: "POST",
    data
  });
}

//获取各分项用能趋势数据
export function queryAccumItemized(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energydata/accumItemized`,
    method: "POST",
    data
  });
}

//获取
export function queryItemConsumption(data) {
  return fetch({
    url: `/eem-service/${version}/energy/energydata/queryItemConsumption`,
    method: "POST",
    data
  });
}
