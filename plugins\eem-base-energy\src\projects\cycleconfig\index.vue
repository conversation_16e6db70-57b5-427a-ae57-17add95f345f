<template>
  <div class="h-full">
    <CetAside class="cet-aside">
      <template #aside>
        <CetTree
          class="h-full"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
          :key="treeKey"
        ></CetTree>
      </template>
      <template #container>
        <CetTable
          class="h-full"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn
              :key="item.label"
              v-bind="item"
              :headerAlign="item.headerAlign || 'left'"
              :align="item.align || 'left'"
              showOverflowTooltip
            ></ElTableColumn>
          </template>
          <ElTableColumn v-bind="ElTableColumn_edit">
            <template slot-scope="scope">
              <span class="handle" @click="handleEdit(scope.$index, scope.row)">
                {{ $T("编辑") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </template>
    </CetAside>

    <el-dialog
      class="min"
      :title="$T('编辑')"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <div>
        <div class="mb-J3">
          <div class="mb-J0">{{ $T("统计周期") }}</div>
          <ElInput class="fullwidth" disabled v-model="cycleText"></ElInput>
        </div>
        <div class="mb-J3">
          <div class="mb-J0">{{ $T("时段") }}</div>
          <ElSelect
            class="fullwidth"
            v-model="value"
            :placeholder="$T('请选择')"
          >
            <ElOption
              v-for="item in options"
              :key="item.label"
              :label="item.label"
              :value="item.num"
            ></ElOption>
          </ElSelect>
        </div>
        <div>
          <div class="mb-J0">{{ $T("统计归属") }}</div>
          <ElSelect
            class="fullwidth"
            v-model="ElSelect_2.value"
            v-bind="ElSelect_2"
            v-on="ElSelect_2.event"
          >
            <ElOption
              v-for="item in ElOption_2.options_in"
              :key="item[ElOption_2.key]"
              :label="item[ElOption_2.label]"
              :value="item[ElOption_2.value]"
              :disabled="item[ElOption_2.disabled]"
            ></ElOption>
          </ElSelect>
        </div>
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";
import omegaI18n from "@omega/i18n";
export default {
  name: "cycleConfig",
  components: {},
  data() {
    const en = omegaI18n.locale === "en";
    return {
      cycleText: "",
      options: [],
      value: "",
      selectObj: {},
      treeKey: 1,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        expandWhenChecked: true, //组件节点可选择状态下，可设置选择节点是否展开expandWhenChecked,默认为true
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300)
        }
      },

      dialogVisible: false,
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      ElTableColumn_edit: {
        label: $T("操作"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        width: en ? 120 : 70 //绝对宽度
      },
      Columns_1: [
        {
          type: "index", // selection 勾选 index 序号
          label: $T("序号"), //列名
          headerAlign: "left",
          align: "left",
          width: "70" //绝对宽度
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("名称"), //列名
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "aggregationcycle1", // 支持path a[0].b
          label: $T("统计周期"), //列名
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "time", // 支持path a[0].b
          label: $T("开始时间"), //列名
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "belong1", // 支持path a[0].b
          label: $T("统计归属"), //列名
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        }
      ],
      currentNode: null,
      cycleConfigTitle: "",
      ElSelect_2: {
        value: "",
        style: {},
        event: {}
      },
      ElOption_2: {
        options_in: [
          { id: 1, text: $T("归属开始时段") },
          { id: 2, text: $T("归属结束时段") }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.confrim
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: () => {
            this.dialogVisible = false;
          }
        }
      },
      week: [
        $T("周一"),
        $T("周二"),
        $T("周三"),
        $T("周四"),
        $T("周五"),
        $T("周六"),
        $T("周日")
      ],
      day: [
        $T("1日"),
        $T("2日"),
        $T("3日"),
        $T("4日"),
        $T("5日"),
        $T("6日"),
        $T("7日"),
        $T("8日"),
        $T("9日"),
        $T("10日"),
        $T("11日"),
        $T("12日"),
        $T("13日"),
        $T("14日"),
        $T("15日"),
        $T("16日"),
        $T("17日"),
        $T("18日"),
        $T("19日"),
        $T("20日"),
        $T("21日"),
        $T("22日"),
        $T("23日"),
        $T("24日"),
        $T("25日"),
        $T("26日"),
        $T("27日"),
        $T("28日")
      ],
      month: [
        $T("1月"),
        $T("2月"),
        $T("3月"),
        $T("4月"),
        $T("5月"),
        $T("6月"),
        $T("7月"),
        $T("8月"),
        $T("9月"),
        $T("10月"),
        $T("11月"),
        $T("12月")
      ]
    };
  },
  methods: {
    // 接口 获取节点树
    async getTree() {
      const res = await customApi.rootNode();
      if (res.code !== 0) {
        return;
      }
      const data = res.data || [];
      this.CetTree_1.inputData_in = data;
      this.CetTree_1.selectNode = data[0];
      this.treeKey++;
    },
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
      this.cycleConfigTitle = val.name;
      this.getQueryUnnaturalSet();
    },
    // 查询项目关联的统计周期
    async getQueryUnnaturalSet() {
      if (!this.currentNode.id) {
        return;
      }
      const queryData = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };
      const res = await customApi.getUnnaturalSet(queryData);
      if (res.code !== 0) {
        return;
      }
      if (res.data) {
        this.disposeData(res.data);
      } else {
        this.initTable();
      }
    },
    disposeData(data) {
      let arr = [0, 0, 0, 0];
      data.forEach(item => {
        item.name = this.cycleConfigTitle;
        item.belong1 = item.belong == 1 ? $T("开始时段") : $T("结束时段");
        if (item.aggregationcycle < 13 || item.aggregationcycle == 18) {
          item.aggregationcycle1 = $T("按日");
          const min =
            item.unnaturalminutebegin == 0 ? "00" : item.unnaturalminutebegin;
          item.time = item.unnaturalhourbegin + ":" + min;
          arr[0] = item;
        } else if (item.aggregationcycle == 13) {
          const arr1 = [1, 2, 3, 4, 5, 6, 7];
          item.aggregationcycle1 = $T("按周");
          item.time = this.week[arr1.indexOf(item.unnaturaldaybegin)];
          arr[1] = item;
        } else if (item.aggregationcycle > 13 && item.aggregationcycle < 17) {
          item.aggregationcycle1 = $T("按月");
          item.time = this.day[item.unnaturaldaybegin - 1];
          arr[2] = item;
        } else {
          item.aggregationcycle1 = $T("按年度");
          item.time = this.month[item.unnaturalmonthbegin - 1];
          arr[3] = item;
        }
      });
      arr.forEach((item, index) => {
        if (item == 0) {
          let arr2 = [
            {
              name: this.cycleConfigTitle,
              aggregationcycle1: $T("按日"),
              time: "0:00",
              belong1: $T("开始时段"),
              belong: 1,
              unnaturalhourbegin: 0,
              unnaturalminutebegin: 0
            },
            {
              name: this.cycleConfigTitle,
              aggregationcycle1: $T("按周"),
              time: this.week[0],
              belong1: $T("开始时段"),
              belong: 1,
              unnaturalhourbegin: 0,
              unnaturalminutebegin: 0
            },
            {
              name: this.cycleConfigTitle,
              aggregationcycle1: $T("按月"),
              time: this.day[0],
              belong1: $T("开始时段"),
              belong: 1,
              unnaturalhourbegin: 0,
              unnaturalminutebegin: 0
            },
            {
              name: this.cycleConfigTitle,
              aggregationcycle1: $T("按年度"),
              time: this.month[0],
              belong1: $T("开始时段"),
              belong: 1,
              unnaturalhourbegin: 0,
              unnaturalminutebegin: 0
            }
          ];
          arr[index] = arr2[index];
        }
      });
      this.CetTable_1.data = arr;
    },
    initTable() {
      this.CetTable_1.data = [
        {
          name: this.cycleConfigTitle,
          aggregationcycle1: $T("按日"),
          time: "0:00",
          belong1: $T("开始时段"),
          belong: 1,
          unnaturalhourbegin: 0,
          unnaturalminutebegin: 0
        },
        {
          name: this.cycleConfigTitle,
          aggregationcycle1: $T("按周"),
          time: this.week[0],
          belong1: $T("开始时段"),
          belong: 1,
          unnaturalhourbegin: 0,
          unnaturalminutebegin: 0
        },
        {
          name: this.cycleConfigTitle,
          aggregationcycle1: $T("按月"),
          time: this.day[0],
          belong1: $T("开始时段"),
          belong: 1,
          unnaturalhourbegin: 0,
          unnaturalminutebegin: 0
        },
        {
          name: this.cycleConfigTitle,
          aggregationcycle1: $T("按年度"),
          time: this.month[0],
          belong1: $T("开始时段"),
          belong: 1,
          unnaturalhourbegin: 0,
          unnaturalminutebegin: 0
        }
      ];
    },
    confrim() {
      this.dialogVisible = false;
      let selectObj = this.selectObj;
      selectObj.unnaturalBegin = this.value;
      selectObj.belong = this.ElSelect_2.value;
      selectObj.rootNode = {
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      };
      this.submit(selectObj);
    },
    // 提交编辑
    async submit(obj) {
      const res = await customApi.updateUnnaturalSet(obj);
      if (res.code !== 0) {
        return;
      }
      this.getQueryUnnaturalSet();
      this.$message.success($T("保存成功"));
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleEdit() {
      this.dialogVisible = true;
    },
    CetTable_1_record_out(val) {
      let item = val;
      let selectObj = {
        unnaturalBegin: item.time,
        belong: item.belong
      };
      this.cycleText = item.aggregationcycle1;
      this.selectObj = selectObj;
      this.ElSelect_2.value = item.belong;
      if (item.aggregationcycle1 == $T("按日")) {
        let time =
          item.unnaturalhourbegin * 3600000 + item.unnaturalminutebegin * 60000;
        selectObj.unnaturalBegin = time;
        selectObj.cycle = 12;
        this.options = [];
        for (let i = 0; i < 24; i++) {
          for (let j = 0; j < 4; j++) {
            this.options.push({
              label: `${i}:${j === 0 ? "00" : j * 15}`,
              num: i * 3600000 + (j * 3600000) / 4
            });
          }
        }
        this.value = this.options.filter(ite => ite.label == item.time)[0].num;
      } else if (item.aggregationcycle1 == $T("按周")) {
        selectObj.cycle = 13;
        this.value = item.unnaturaldaybegin || 1;
        selectObj.unnaturalBegin = item.unnaturaldaybegin || 1;
        this.options = [];
        for (let i = 0; i < 7; i++) {
          this.options.push({ label: this.week[i], num: i + 1 });
        }
      } else if (item.aggregationcycle1 == $T("按月")) {
        selectObj.cycle = 14;
        this.value = item.unnaturaldaybegin || 1;
        selectObj.unnaturalBegin = item.unnaturaldaybegin || 1;
        this.options = [];
        for (let i = 1; i <= 28; i++) {
          this.options.push({ label: this.day[i - 1], num: i });
        }
      } else {
        selectObj.cycle = 17;
        this.value = item.unnaturalmonthbegin || 1;
        selectObj.unnaturalBegin = item.unnaturalmonthbegin || 1;
        this.options = [];
        for (let i = 1; i <= 12; i++) {
          this.options.push({ label: this.month[i - 1], num: i });
        }
      }
    }
  },
  mounted() {
    this.getTree();
  }
};
</script>
<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
.handle {
  @include font_color(ZS);
  cursor: pointer;
}
</style>
