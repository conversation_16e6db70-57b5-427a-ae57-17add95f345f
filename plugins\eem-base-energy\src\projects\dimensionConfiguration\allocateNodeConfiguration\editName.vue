<template>
  <!-- 弹窗组件 -->
  <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
    <div class="p-J4 bg1 content">
      <CetForm
        ref="form"
        class="flex-col flex"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
      >
        <el-form-item :label="$T('节点树名称')" prop="name">
          <ElInput
            v-model="CetForm_1.data.name"
            v-bind="ElInput_name"
            v-on="ElInput_name.event"
          ></ElInput>
        </el-form-item>
      </CetForm>
    </div>

    <template v-slot:footer>
      <span>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_preserve"
          v-on="CetButton_preserve.event"
        ></CetButton>
      </span>
    </template>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common";
import customApi from "@/api/custom";

export default {
  name: "addTree",
  components: {},
  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      CetDialog_pagedialog: {
        showClose: true,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: $T("重命名"),
        width: "480px",
        appendToBody: true,
        event: {}
      },
      CetForm_1: {
        labelPosition: "top",
        dataMode: "component", // 数据获取模式： backendInterface 后端接口 ；其他组件  component  ; 静态数据  static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "150px",
        rules: {
          name: [
            {
              required: true,
              message: $T("请输入名称"),
              trigger: ["blur", "change"]
            },
            common.check_space,
            common.check_name,
            common.pattern_name
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      ElInput_name: {
        placeholder: $T("请输入"),
        style: {
          width: "100%"
        },
        event: {}
      },
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = val;
      this.CetForm_1.data = {
        name: this.inputData_in.name
      };
      this.CetForm_1.resetTrigger_in = Date.now();
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = val;
    }
  },
  methods: {
    async CetForm_1_saveData_out(val) {
      const queryData = {
        name: val.name,
        id: this.inputData_in.id,
        projectId: this.projectId
      };
      const res = await customApi.editAttributedimensionTreeName(queryData);
      if (res?.code !== 0) return;
      this.$emit("finishTrigger_out");
      this.$message.success($T("保存成功"));
      this.CetDialog_pagedialog.closeTrigger_in = val;
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = val;
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = val;
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  border-radius: var(--Ra1);
  height: 200px;
  box-sizing: border-box;
}
</style>
