<template>
  <div class="fullfilled relative table-border">
    <div
      :class="{
        fullfilled: true,
        light: lightTheme,
        boxShadow: tableScrollLeft,
        editMode: editMode
      }"
      class="handsontable"
      id="dimensionConfiguration_handsontable"
      ref="dimensionConfiguration_handsontable"
    ></div>
  </div>
</template>

<script>
import omegaTheme from "@omega/theme";
export default {
  name: "batchConfigHandsontable",
  props: {
    data: Array,
    columns: Array,
    clearFilterHandle: Number,
    clearSortHandle: Number,
    clearHot: Number,
    fixedColumnsLeft: Number,
    hideSortIndex: Array,
    editMode: Boolean
  },
  computed: {
    lightTheme() {
      return omegaTheme.theme === "light";
    },
    hotLanguage() {
      if (window.localStorage.getItem("omega_language") === "en") {
        return "en-US";
      }
      return "zh-CN";
    }
  },
  data() {
    return {
      tableScrollLeft: false,
      tableData: [],
      handsontableFilter: false
    };
  },
  watch: {
    data: {
      handler: function (val) {
        this.tableData = this._.cloneDeep(val);
        this.initTable();
      },
      deep: true,
      immediate: true
    },
    clearFilterHandle() {
      this.clearFilter();
    },
    clearSortHandle() {
      const columnSorting = this.hot.getPlugin("columnSorting");
      const sortConfig = columnSorting?.getSortConfig();
      if (!sortConfig?.length) return;
      columnSorting.clearSort();
    },
    clearHot() {
      if (!this.hot) return;
      this.hot.destroy();
      this.hot = null;
    }
  },
  methods: {
    async initTable() {
      const hotElement = this.$refs.dimensionConfiguration_handsontable;
      if (!hotElement) {
        return;
      }

      const tableColumns = [
        ...this.columns,
        {
          className: "htLeft",
          data: "tree_id",
          type: "text",
          columnSorting: true,
          readOnly: true
        }
      ];
      const tableHeaders = [...this.columns.map(i => i.label), ""];
      const colWidths = [
        ...this.columns.map(i => {
          return i.minWidth ?? 150;
        }),
        0.000001
      ];
      const hotSettings = {
        // autoColumnSize: true,
        fixedColumnsLeft: this.fixedColumnsLeft,
        colWidths: colWidths,
        wordWrap: true, // 单元格文字是否换行展示
        data: this.tableData,
        columns: tableColumns,
        readOnly: false,
        stretchH: "all",
        autoWrapRow: true,
        height: "100%",
        maxRows: this.tableData.length,
        rowHeaders: true,
        colHeaders: tableHeaders,
        dropdownMenu: true, //下拉式菜单
        filters: true, //过滤器
        fillHandle: "vertical", //设置自定填充垂直方向
        manualRowMove: false, //当值为true时，行可拖拽至指定行
        language: this.hotLanguage,
        columnSorting: true,
        manualColumnResize: true,
        dragToScroll: true,
        afterGetColHeader: (col, th) => {
          if (this.hideSortIndex.includes(col)) {
            th.className = "my-custom-hideSort";
            return;
          }
        },
        afterChange: (val, source) => {
          if (source !== "edit" && source !== "CopyPaste.paste") return;
          // 修改后将这些行置为未保存状态
          let indexs = val.map(i => i[0]);
          this.setTableRowStatus(indexs);
        },
        beforeColumnSort: () => {
          const filtersPlugin = this.hot.getPlugin("filters");
          if (filtersPlugin.isEnabled() && this.handsontableFilter) {
            this.$message.warning($T("请点击重置全部过滤"));
            return false;
          }
          return true;
        },
        beforeFilter: filter => {
          // 如果存在未保存数据，提示先保存数据再进行筛选
          const data = this.getTableDataByFilter();
          const flag = data.find(i => i.status === -1);
          if (flag && this.$route.path === "/dimensionConfiguration") {
            this.$message.warning($T("数据已修改，请先保存在进行筛选"));
            return false;
          }
          this.handsontableFilter = !!filter?.length;
          return true;
        }
      };

      if (this.hot) {
        this.hot.updateSettings(hotSettings);
      } else {
        // eslint-disable-next-line no-undef
        this.hot = new Handsontable(hotElement, hotSettings);
      }

      await this.$nextTick();
      setTimeout(() => {
        const filtersPlugin = this.hot.getPlugin("filters");
        filtersPlugin.filter();
      }, 300);
      this.addScrollEvent();
    },
    getTableData() {
      // getData才能获取排序后的数据
      const data = this.hot.getData();
      const tableData = this._.cloneDeep(this.tableData);
      if (this.handsontableFilter) {
        return tableData;
      }
      const newTableData = data.map(item => {
        const tree_id = item[item.length - 1];
        return tableData.find(i => i.tree_id === tree_id);
      });
      return newTableData;
    },
    getTableDataByFilter() {
      const data = this.hot.getData();
      const tableData = this._.cloneDeep(this.tableData);
      const newTableData = data.map(item => {
        const tree_id = item[item.length - 1];
        return tableData.find(i => i.tree_id === tree_id);
      });
      return newTableData;
    },
    async clearFilter() {
      if (!this.hot) return;
      this.hot.updateSettings({ filters: false });
      await this.$nextTick();
      if (!this.hot) return;
      this.handsontableFilter = false;
      this.hot.updateSettings({ filters: true });
    },
    setTableRowStatus(indexs) {
      if (!indexs?.length) return;
      indexs.forEach(index => {
        const row = this.hot.getDataAtRow(index);
        const tree_id = row[row.length - 1];
        const tableItem = this.tableData.find(i => i.tree_id === tree_id);
        tableItem.status = -1;
      });
    },
    scrollEvent() {
      this.tableScrollLeft = !!this.scrollDom.scrollLeft;
    },
    addScrollEvent() {
      if (this.scrollDom) {
        this.scrollDom.removeEventListener("scroll", this.scrollEvent);
        this.scrollDom = null;
      }
      const scrollDom = this.$refs.dimensionConfiguration_handsontable
        .getElementsByClassName("ht_master")[0]
        .getElementsByClassName("wtHolder")[0];
      this.scrollDom = scrollDom;
      scrollDom.addEventListener("scroll", this.scrollEvent);
    },
    async loadData() {
      this.hot.loadData(this.tableData);
      await this.$nextTick();
      setTimeout(() => {
        const filtersPlugin = this.hot.getPlugin("filters");
        filtersPlugin.filter();
      }, 300);
    }
  },
  mounted() {
    this.initTable();
  },
  beforeDestroy() {
    this.scrollDom.removeEventListener("scroll", this.scrollEvent);
  },
  deactivated() {
    this.scrollDom.removeEventListener("scroll", this.scrollEvent);
  }
};
</script>
<style lang="scss" scoped>
#dimensionConfiguration_handsontable :deep() .handsontable th {
  text-align: left;
}
.table-border {
  border: 1px solid;
  @include border_color(B1);
  border-top: none;
}
#dimensionConfiguration_handsontable :deep() tr td:first-child {
  border-left: none;
}
#dimensionConfiguration_handsontable
  :deep()
  .ht_master.handsontable
  tr
  td:last-child {
  border-right: none;
}
#dimensionConfiguration_handsontable :deep() tr th:first-child {
  border-left: none;
}
#dimensionConfiguration_handsontable
  ::v-deep
  .ht_clone_top.handsontable
  tr
  th:last-child {
  border-right: none;
}
#dimensionConfiguration_handsontable :deep() table thead tr th {
  height: 30px;
  line-height: 30px;
  @include background_color(BG);
  @include font_color(T1);
  @include border_color(B1);
}
#dimensionConfiguration_handsontable :deep() table tbody tr th {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
#dimensionConfiguration_handsontable :deep() table tbody tr td {
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
#dimensionConfiguration_handsontable :deep() .handsontableHandle {
  @include font_color(Sta3);
}
#dimensionConfiguration_handsontable :deep() .required {
  @include font_color(Sta3);
}
#dimensionConfiguration_handsontable :deep() .autocompleteEditor.handsontable {
  padding-right: 0;
}

#dimensionConfiguration_handsontable :deep(table thead tr th) {
  height: 30px;
  line-height: 30px;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}
#dimensionConfiguration_handsontable :deep(.handsontable th) {
  text-align: left;
}
#dimensionConfiguration_handsontable :deep(table tbody tr th) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#dimensionConfiguration_handsontable :deep(table tbody tr td) {
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  @include background_color(BG1);
  @include font_color(T1);
  @include border_color(B1);
}

#dimensionConfiguration_handsontable.editMode :deep(table tbody tr td) {
  @include background_color(B1);
}
#dimensionConfiguration_handsontable :deep(table tbody tr td.editTd) {
  @include background_color(BG1);
}

#dimensionConfiguration_handsontable :deep(table thead tr th .changeType) {
  margin-top: 8px;
}
#dimensionConfiguration_handsontable :deep(table thead tr th .colHeader) {
  font-weight: 700;
}
#dimensionConfiguration_handsontable :deep(.handsontable .changeType) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#dimensionConfiguration_handsontable :deep(.handsontable .changeType:hover) {
  @include font_color(T1);
  @include background_color(BG1);
  @include border_color(B1);
}
#dimensionConfiguration_handsontable :deep(.handsontableHandle) {
  @include font_color(ZS);
}

#dimensionConfiguration_handsontable :deep() {
  .handsontable span.colHeader.columnSorting:before {
    top: 8px;
    width: 14px;
    height: 14px;
    padding-left: 0;
    transform: translate(6px, 0px);
    background-image: url("../assets/initial.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.descending:before {
    background-image: url("../assets/descending.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.ascending:before {
    background-image: url("../assets/ascending.png");
    background-size: 100% 100%;
  }
}

#dimensionConfiguration_handsontable.light :deep() {
  .handsontable span.colHeader.columnSorting:before {
    background-image: url("../assets/initial_light.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.descending:before {
    background-image: url("../assets/descending_light.png");
    background-size: 100% 100%;
  }
  .handsontable span.colHeader.columnSorting.ascending:before {
    background-image: url("../assets/ascending_light.png");
    background-size: 100% 100%;
  }
}

#dimensionConfiguration_handsontable.boxShadow :deep() {
  .ht_clone_left.handsontable .wtHolder .wtHider .wtSpreader .htCore tbody {
    @include box_shadow(S3);
  }
}
#dimensionConfiguration_handsontable :deep() .handsontable tr {
  @include background_color(BG1);
}
#dimensionConfiguration_handsontable :deep() .columnSorting.sortAction:hover {
  text-decoration: initial;
}
#dimensionConfiguration_handsontable
  :deep(.ht_clone_top.handsontable .htCore tr > th:nth-last-child(1)) {
  .changeType {
    display: none;
  }
  span.colHeader.columnSorting:before {
    display: none;
  }
}

#dimensionConfiguration_handsontable :deep(.my-custom-hideSort) {
  span.colHeader.columnSorting:before {
    display: none;
  }
}

#dimensionConfiguration_handsontable :deep(.ht_clone_left.handsontable) {
  padding-right: 10px;
}
</style>

<style lang="scss">
#hot-display-license-info {
  display: none;
}
.handsontable .ht_master table td.htCustomMenuRenderer {
  @include background_color(BG);
}
.htFiltersConditionsMenu table tbody tr td {
  @include background_color(BG1);
}
.htDropdownMenu table tbody tr td {
  @include background_color(BG1);
}
.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {
  @include background_color(BG2);
}
.handsontable .htUISelectCaption {
  @include background_color(BG4);
  &:hover {
    @include background_color(BG4);
  }
}
.handsontable .htUIInput.htUIButtonOK input {
  @include background_color(ZS);
}
.htFiltersConditionsMenu table tbody tr td.current,
.htFiltersConditionsMenu table tbody tr td.zeroclipboard-is-hover {
  @include background_color(BG4);
}

.htDropdownMenu
  > .handsontable
  > .wtHolder
  > .wtHider
  > .wtSpreader
  > .htCore
  > tbody {
  & > tr:nth-child(1),
  & > tr:nth-child(2),
  & > tr:nth-child(3),
  & > tr:nth-child(4),
  & > tr:nth-child(5),
  & > tr:nth-child(6),
  & > tr:nth-child(7),
  & > tr:nth-child(8),
  & > tr:nth-child(9),
  & > tr:nth-child(10) {
    display: none;
  }
  & > tr:nth-child(15) .htUIMultipleSelectSearch {
    display: none;
  }
}
ul.zTreeDragUL {
  position: fixed;
  list-style: none;
  padding: 4px;
  @include background_color(BG4);
}
.htDropdownMenu:not(.htGhostTable) {
  position: fixed;
}
.htFiltersConditionsMenu:not(.htGhostTable) {
  position: fixed;
}
</style>
