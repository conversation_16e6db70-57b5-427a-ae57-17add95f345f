<template>
  <div class="page flex-row flex">
    <div
      class="treeBox p-J4 border-solid border-r-[1px] border-t-0 border-b-0 border-l-0 border-B1"
    >
      <CetTree
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        :searchText_in.sync="CetTree_1.searchText_in"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
      ></CetTree>
    </div>
    <div class="contentBox p-J4 flex-auto flex-col flex">
      <div class="tableHeader mb-J3 flex-row flex">
        <div class="flex-auto tooltipText">
          {{ $T("该表格支持类似excel的操作") }}
          <el-tooltip
            effect="light"
            :content="
              $T(
                '使用快捷键全选或者点击列头全选，支持快捷键的方式复制粘贴和表格下拉的操作，支持delete键删除，支持筛选；'
              )
            "
          >
            <span class="el-icon-question"></span>
          </el-tooltip>
        </div>
        <CetButton
          class="ml-J3"
          v-bind="CetButton_resetFilter"
          v-on="CetButton_resetFilter.event"
          v-if="editMode"
        ></CetButton>
        <el-tooltip
          effect="light"
          :disabled="!importing"
          :content="importingStr"
          placement="top"
        >
          <CetButton
            class="ml-J3"
            v-bind="CetButton_import"
            v-on="CetButton_import.event"
            :disable_in="!currentNode?.id || importing"
            v-permission="'dimattributeconfig_nodeupdate'"
          ></CetButton>
        </el-tooltip>
        <el-tooltip
          effect="light"
          :disabled="!importing"
          :content="importingStr"
          placement="top"
        >
          <CetButton
            class="ml-J3"
            v-bind="CetButton_edit"
            v-on="CetButton_edit.event"
            v-if="!editMode"
            :disable_in="editDisable || importing"
            v-permission="'dimattributeconfig_nodeupdate'"
          ></CetButton>
        </el-tooltip>
        <CetButton
          class="ml-J3"
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
          v-if="editMode"
        ></CetButton>
        <CetButton
          class="ml-J3"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
          v-if="editMode"
        ></CetButton>
      </div>
      <div class="flex-auto">
        <HandsontableComponent
          ref="handsontable"
          v-bind="handsontable"
          v-on="handsontable.event"
          v-show="!editDisable"
          :editMode="editMode"
        />

        <el-empty
          class="fullheight"
          v-show="isLight && editDisable"
          :image-size="524"
          image="static/assets/empty_max_light.png"
        ></el-empty>
        <el-empty
          v-show="!isLight && editDisable"
          class="fullheight"
          :image-size="524"
          image="static/assets/empty_max.png"
        ></el-empty>
      </div>
    </div>
    <UploadDialog
      v-bind="uploadDialog"
      v-on="uploadDialog.event"
    ></UploadDialog>
    <DimensionConfigRecord v-bind="dimensionConfigRecord" />
    <WarningDialog v-bind="warningDialog" />
  </div>
</template>

<script>
import customApi from "@/api/custom";
import HandsontableComponent from "./handsontable.vue";
import omegaTheme from "@omega/theme";
import common from "eem-base/utils/common";
import UploadDialog from "eem-base/components/uploadDialog";
import DimensionConfigRecord from "./dimensionConfigRecord";
import WarningDialog from "./warningDialog";
import omegaI18n from "@omega/i18n";
export default {
  name: "dimensionConfig",
  components: {
    HandsontableComponent,
    UploadDialog,
    DimensionConfigRecord,
    WarningDialog
  },
  data() {
    return {
      manageModelLabels: [],
      editMode: false,
      columnConfig: [], //维度标签配置
      currentNode: null,
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this._.debounce(this.CetTree_1_currentNode_out, 300)
        }
      },
      CetButton_resetFilter: {
        visible_in: true,
        disable_in: false,
        title: $T("重置全部过滤"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_resetFilter_statusTrigger_out
        }
      },
      CetButton_import: {
        visible_in: true,
        // disable_in: false,
        title: $T("批量导入"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_import_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("保存"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_edit: {
        visible_in: true,
        disable_in: false,
        title: $T("编辑"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_edit_statusTrigger_out
        }
      },
      handsontable: {
        data: [],
        columns: [],
        hideSortIndex: [],
        clearFilterHandle: Date.now(),
        clearSortHandle: Date.now(),
        clearHot: Date.now(),
        fixedColumnsLeft: 3,
        event: {}
      },
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx"],
        downloadPermission: null,
        hideDownload: false,
        dialogTitle: $T("批量导入"),
        event: {
          uploadFile: this.uploadDialog_uploadFile,
          download: this.uploadDialog_download
        }
      },
      dimensionConfigRecord: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        node_in: null
      },
      warningDialog: {
        visibleTrigger_in: Date.now(),
        warningText: ""
      }
    };
  },
  computed: {
    editDisable() {
      return !this.handsontable.data?.length;
    },
    isLight() {
      return omegaTheme.theme === "light";
    },
    importing() {
      return this.$store.getters["importProgress/importing"](10);
    },
    importingStr() {
      return this.$store.getters["importProgress/importingStr"](10);
    },
    enLanguage() {
      return omegaI18n.locale === "en";
    }
  },
  methods: {
    init() {
      this.editMode = false;
      this.getTreeData();
    },
    // 查询项目，分区，楼栋，楼层，房间，用能设备即可
    async getTreeData() {
      const params = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: [
          { modelLabel: "sectionarea" },
          { modelLabel: "building" },
          { modelLabel: "floor" },
          {
            modelLabel: "room",
            filter: {
              composemethod: true,
              expressions: [
                {
                  limit: null,
                  operator: "EQ",
                  prop: "roomtype",
                  tagid: 1
                }
              ]
            }
          },
          { modelLabel: "manuequipment" },
          { modelLabel: "virtualbuildingnode" },
          { modelLabel: "virtualdevicenode" }
        ],
        treeReturnEnable: true
      };
      const res = await customApi.nodeTreeSimpleNoAuth(params);
      this.CetTree_1.inputData_in = res?.data ?? [];
      this.CetTree_1.selectNode = res?.data?.[0];
    },
    CetTree_1_currentNode_out(val) {
      if (this.giveUpNodeOut) {
        this.giveUpNodeOut = false;
        return;
      }
      this.saveDraftTips(
        () => {
          this.currentNode = this._.cloneDeep(val);
          this.getTableData();
        },
        () => {
          this.giveUpNodeOut = true;
          this.CetTree_1.selectNode = this._.cloneDeep(this.currentNode);
        }
      );
    },
    async getTableData() {
      this.editMode = false;
      this.handsontable.clearFilterHandle = Date.now();
      this.handsontable.clearSortHandle = Date.now();
      this.handsontable.clearHot = Date.now();
      await this.getColumn();
      await this.$nextTick();
      const res = await customApi.attributedimensionNodeConfigQuery({
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel
      });
      const data = res?.data ?? [];
      data.forEach(async item => {
        const { configNode, configVo } = item;
        item.tree_id = `${configNode.modelLabel}_${configNode.id}`;
        item.nodeId = configNode.id;
        item.nodeName = configNode.name;
        item.nodeLabel = configNode.modelLabel;
        item.nodeType$name = "等接口返回";
        // 解管理层级
        item.manageNodeName.forEach(i => {
          item[i.modelLabel] = i.name;
        });
        if (configVo?.length) {
          configVo.forEach(({ levelName, tagName }) => {
            const obj = this.columnConfig.find(i => i.name == levelName);
            const levelId = obj?.id;
            item[levelId] = tagName;
          });
        }
        item.enableTime = item.enableTime
          ? this.$moment(item.enableTime).format("YYYY-MM-DD")
          : "";
      });
      this.handsontable.data = data;
    },
    // 获取表头
    async getColumn() {
      const columnsRes =
        await customApi.attributedimensionNodeConfigColumnName();
      const columnsData = columnsRes?.data ?? [];
      this.manageModelLabels = columnsData.map(i => i.modelLabel);
      this.columnsData = columnsData;
      const columns = [
        {
          className: "htLeft",
          data: "nodeId",
          type: "text",
          label: `${$T("节点ID")}`,
          readOnly: true,
          sortable: false,
          minWidth: 80
        },
        {
          className: "htLeft",
          data: "nodeType$name",
          type: "text",
          label: `${$T("节点类型")}`,
          readOnly: true,
          minWidth: 90
        },
        {
          className: `htLeft ${this.editMode ? "editTd" : ""}`,
          data: "nodeName",
          type: "text",
          label: `${$T("节点名称")}`,
          readOnly: !this.editMode
        }
      ];
      const hideSortIndex = [0, 1, 2];
      // 补上其他层级
      columnsData.forEach((item, index) => {
        const config = {
          className: "htLeft",
          data: item.modelLabel,
          type: "text",
          label: item.name,
          readOnly: true,
          minWidth: 100
        };
        hideSortIndex.push(index + 3);
        columns.push(config);
      });
      this.handsontable.fixedColumnsLeft = 3 + columnsData.length;
      const res = await customApi.attributedimensionNodeConfigTag();
      const data = res?.data ?? [];
      this.columnConfig = data;
      data.forEach(item => {
        columns.push({
          allowInvalid: true,
          className: `htLeft ${this.editMode ? "editTd" : ""}`,
          data: item.id,
          source: item?.tagNameList ?? [],
          strict: item.tagType === 2,
          type: "autocomplete",
          label: item.name,
          readOnly: !this.editMode,
          minWidth: 150
        });
      });
      columns.push({
        allowInvalid: true,
        className: `htLeft ${this.editMode ? "editTd" : ""}`,
        data: "enableTime",
        type: "date",
        label: $T("生效时间"),
        dateFormat: "YYYY-MM-DD",
        allowEmpty: true,
        datePickerConfig: {
          disableDayFn: date => {
            return date.getTime() > Date.now();
          }
        },
        readOnly: !this.editMode
      });

      columns.push({
        allowInvalid: true,
        className: `htLeft ${this.editMode ? "editTd" : ""}`,
        data: "tableHandle",
        renderer: this.renderBtn,
        label: $T("操作"),
        minWidth: this.enLanguage ? 190 : 150,
        readOnly: true
      });
      this.handsontable.columns = columns;
      this.handsontable.hideSortIndex = hideSortIndex;
    },
    renderBtn(instance, td, row, col, prop, value, cellProperties) {
      //添加自定义的图片，并给图片的chick添加事件
      let text;

      text = document.createElement("span");
      text.innerHTML = $T("调整记录查询");
      text.className = "handsontableHandle";
      // text.width = 20;
      text.style = "cursor:pointer;width:100px;"; //鼠标移上去变手型
      // eslint-disable-next-line no-undef
      Handsontable.dom.addEvent(text, "click", () => {
        const row_index = cellProperties.row;
        const rowData = this.handsontable.data[row_index];
        this.showRecord(rowData);
      });

      // eslint-disable-next-line no-undef
      Handsontable.dom.empty(td);
      td.className = td.className + " editTd";
      td.appendChild(text);
      td.style.textAlign = "left"; //图片居中对齐
      return td;
    },
    showRecord(rowData) {
      this.dimensionConfigRecord.node_in = this._.cloneDeep(rowData.configNode);
      this.dimensionConfigRecord.openTrigger_in = Date.now();
    },
    async saveData(cb) {
      let tableData = this.$refs.handsontable.getTableDataByFilter();

      const columnsData = this._.cloneDeep(this.columnConfig);
      let checkObj = {};
      const checkTagIds = [];
      columnsData.forEach(item => {
        const { id, name, tagType } = item;
        if (tagType === 1) {
          checkTagIds.push(id);
          checkObj[id] = {
            checkTextLength: [],
            checkTextLength$text: val => {
              return `${$T(`第【{0}】行{1}名称过长`, val.join("、"), name)};`;
            },
            checkTextCharacter: [],
            checkTextCharacter$text: val => {
              return `${$T(
                `第【{0}】行{1}存在特殊字符`,
                val.join("、"),
                name
              )};`;
            }
          };
        }
      });
      const indexs = [],
        indexs2 = [];
      const checkNamePattern = common.pattern_name.pattern;
      const checkNameLength = common.check_name.max;
      let formatRowFlag = false;
      const checkTextLength = [];
      const checkTextCharacter = [];
      const checkNameEmpty = [];
      tableData.forEach((item, index) => {
        const checkTime = this.columnConfig.find(({ id }) => item[id]);
        if (!item.enableTime && checkTime) {
          indexs.push(index + 1);
        }
        if (item.enableTime && !checkTime) {
          indexs2.push(index + 1);
        }
        if (!item.nodeName) {
          checkNameEmpty.push(index + 1);
        } else if (item.nodeName?.length > checkNameLength) {
          checkTextLength.push(index + 1);
        } else if (!checkNamePattern.test(item.nodeName)) {
          checkTextCharacter.push(index + 1);
        }

        checkTagIds.forEach(id => {
          if (item[id]?.length > checkNameLength) {
            checkObj[id].checkTextLength.push(index + 1);
            formatRowFlag = true;
          }
          if (!checkNamePattern.test(item[id])) {
            checkObj[id].checkTextCharacter.push(index + 1);
            formatRowFlag = true;
          }
        });
      });
      if (
        indexs?.length ||
        indexs2?.length ||
        checkTextLength?.length ||
        checkTextCharacter?.length ||
        checkNameEmpty?.length ||
        formatRowFlag
      ) {
        let str = "";
        if (indexs?.length) {
          str += `${$T(`第【{0}】行生效时间未选择`, indexs.join("、"))};`;
        }
        if (indexs2?.length) {
          str += `${$T(
            `第【{0}】行未配置标签，不允许选择生效时间`,
            indexs2.join("、")
          )};`;
        }
        if (checkTextLength?.length) {
          str += `${$T(
            `第【{0}】行节点名称过长`,
            checkTextLength.join("、")
          )};`;
        }
        if (checkTextCharacter?.length) {
          str += `${$T(
            `第【{0}】行{1}存在特殊字符`,
            checkTextCharacter.join("、"),
            $T("节点名称")
          )};`;
        }
        if (checkNameEmpty?.length) {
          str += `${$T(
            `第【{0}】行节点名称不能为空`,
            checkNameEmpty.join("、")
          )};`;
        }

        checkTagIds.forEach(id => {
          if (checkObj[id].checkTextLength?.length) {
            str += checkObj[id].checkTextLength$text(
              checkObj[id].checkTextLength
            );
          }
          if (checkObj[id].checkTextCharacter?.length) {
            str += checkObj[id].checkTextCharacter$text(
              checkObj[id].checkTextCharacter
            );
          }
        });
        this.warningDialog.warningText = str;
        this.warningDialog.visibleTrigger_in = Date.now();
        return;
      }
      const saveData = tableData.map(item => {
        const configVo = [];
        this.columnConfig.forEach(({ id, name }) => {
          if (item[id]) {
            configVo.push({
              levelName: name,
              tagName: item[id]
            });
          }
        });
        const manageNodeName = [];
        this.manageModelLabels.forEach(i => {
          if (item[i]) {
            manageNodeName.push({
              modelLabel: i,
              name: item[i]
            });
          }
        });
        return {
          configNode: {
            ...item.configNode,
            name: item.nodeName
          },
          configVo,
          enableTime: this.$moment(item.enableTime).valueOf(),
          manageNodeName
        };
      });
      const res = await customApi.attributedimensionNodeConfigEdit(saveData);
      if (res.code !== 0) return;
      this.$message.success($T("操作成功"));
      this.getTableData();
      cb && cb();
      this.$store.dispatch("importProgress/noticeProgress", {
        vm: this,
        initialProcessInfo: res.data,
        cb: async () => {
          this.getTableData();
        }
      });
    },
    ifHandsontableFilter() {
      const handsontableFilter = this.$refs.handsontable.handsontableFilter;
      if (handsontableFilter) {
        this.$message.warning($T("请点击重置全部过滤，再进行保存！"));
        return false;
      }
      return true;
    },
    CetButton_resetFilter_statusTrigger_out() {
      this.handsontable.clearFilterHandle = Date.now();
    },
    async uploadDialog_uploadFile(val) {
      let data = new FormData();
      data.append("file", val.file);
      const res = await customApi.attributedimensionNodeConfigImport(data);
      if (res.code !== 0) return;
      this.$message.success($T("操作成功"));
      !this.editMode && this.getTableData();
      this.uploadDialog.closeTrigger_in = Date.now();
      this.$store.dispatch("importProgress/noticeProgress", {
        vm: this,
        initialProcessInfo: res.data,
        cb: async () => {
          this.getTableData();
        }
      });
    },
    uploadDialog_download() {
      common.downExcel(
        "/eem-service/v1/attributedimension/nodeconfig/export",
        {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        },
        this.$store.state.token
      );
    },
    CetButton_import_statusTrigger_out() {
      this.uploadDialog.openTrigger_in = new Date().getTime();
    },
    CetButton_confirm_statusTrigger_out() {
      this.$confirm(
        $T("维度标签赋值修改，动力分摊方案可能需要重新关联，是否确定保存？"),
        $T("提示"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          distinguishCancelAndClose: true,
          type: "warning"
        }
      )
        .then(() => {
          this.saveData();
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    CetButton_cancel_statusTrigger_out() {
      this.getTableData();
    },
    async CetButton_edit_statusTrigger_out() {
      this.editMode = true;
      await this.getColumn();
      this.handsontable.data = this._.cloneDeep(this.handsontable.data);
    },
    // 提示是否先保存草稿
    async saveDraftTips(giveUp, cancel) {
      if (!this.editMode) {
        giveUp && giveUp();
        return;
      }
      await this.$confirm(
        $T("存在未保存项，数据会丢失，是否保存?"),
        $T("提示"),
        {
          confirmButtonText: $T("保存"),
          cancelButtonText: $T("放弃保存"),
          distinguishCancelAndClose: true,
          type: "warning"
        }
      )
        .then(async () => {
          await this.saveData(giveUp);
        })
        .catch(action => {
          if (action === "cancel") {
            giveUp && giveUp();
          } else {
            this.$message.info($T("已取消"));
            cancel && cancel();
          }
        });
    },
    beforeunload(event) {
      if (!this.editMode) return;
      event.returnValue = $T("确定要离开此页面吗？你未保存的更改将丢失。");
      return;
    }
  },
  mounted() {
    this.init();
    window.addEventListener("beforeunload", this.beforeunload);
  },
  beforeDestroy() {
    window.removeEventListener("beforeunload", this.beforeunload);
  },
  deactivated() {
    window.removeEventListener("beforeunload", this.beforeunload);
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .treeBox {
    width: 315px;
    box-sizing: border-box;
  }
  .tableHeader {
    justify-content: flex-end;
    line-height: 32px;
    .tooltipText {
      @include font_color(T6);
    }
  }
}
</style>
