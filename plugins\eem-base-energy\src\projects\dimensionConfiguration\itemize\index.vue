<template>
  <el-container class="page">
    <el-aside
      class="p-J4 mainBox border-solid border-r-[1px] border-t-0 border-b-0 border-l-0 border-B1"
    >
      <el-header height="32px" style="padding: 0px; line-height: 32px">
        <span class="title">{{ $T("节点树列表") }}</span>
        <CetButton
          v-permission="'dimattributeconfig_treeupdate'"
          class="fr"
          v-bind="CetButton_addTree"
          v-on="CetButton_addTree.event"
        ></CetButton>
      </el-header>
      <el-main style="height: calc(100% - 48px); width: 100%" class="p0 mt-J3">
        <CetTable
          :data.sync="CetTable_treeList.data"
          :dynamicInput.sync="CetTable_treeList.dynamicInput"
          v-bind="CetTable_treeList"
          v-on="CetTable_treeList.event"
        >
          <ElTableColumn width="50">
            <template slot-scope="scope">
              <el-radio
                v-model="radio"
                :label="CetTable_treeList.data[scope.$index].id"
              ></el-radio>
            </template>
          </ElTableColumn>
          <ElTableColumn
            type="index"
            :label="$T('序号')"
            width="70"
          ></ElTableColumn>
          <ElTableColumn
            :label="$T('节点树名称')"
            prop="name"
            :show-overflow-tooltip="true"
          ></ElTableColumn>
          <ElTableColumn :label="$T('操作')" width="130">
            <template slot-scope="scope">
              <span
                class="text-ZS cursor-pointer"
                @click.stop="editNameHandle(scope.row)"
              >
                {{ $T("重命名") }}
              </span>
              <span
                class="text-Sta3 cursor-pointer ml-J1"
                @click.stop="dleteTree(scope.row)"
              >
                {{ $T("删除") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </el-main>
    </el-aside>
    <el-main class="p-J4 ml-J3 bg1 mainBox">
      <el-header
        height="32px"
        style="padding: 0px; line-height: 32px; display: flex"
      >
        <span
          class="title text-ellipsis"
          style="flex: 1"
          :title="(currentRow.name || '') + $T('配置')"
        >
          {{ (currentRow.name || "") + $T("配置") }}
        </span>
      </el-header>
      <el-main style="height: calc(100% - 48px); width: 100%" class="p0 mt-J3">
        <CetGiantTree
          v-bind="CetGiantTree_1"
          v-on="CetGiantTree_1.event"
        ></CetGiantTree>
      </el-main>
    </el-main>
    <addTree
      v-bind="addTree"
      @finishTrigger_out="getTableData"
      :treeDataList="CetTable_treeList.data"
    />
    <EditName v-bind="editName" @finishTrigger_out="getTableData" />
  </el-container>
</template>

<script>
import customApi from "@/api/custom";
import addTree from "./addTree.vue";
import EditName from "./editName.vue";
export default {
  name: "allocateNodeConfiguration",
  components: { addTree, EditName },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      addTree: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      editName: {
        inputData_in: {},
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      radio: "",
      currentRow: {},
      CetTable_treeList: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_treeList_record_out
        }
      },
      CetButton_addTree: {
        visible_in: true,
        disable_in: false,
        title: $T("新建分项节点树"),
        type: "primary",
        plain: false,
        icon: "el-icon-plus",
        event: {
          statusTrigger_out: this.CetButton_addTree_statusTrigger_out
        }
      },
      currentNode: {},
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out //选中单行输出
        }
      }
    };
  },
  methods: {
    async getTableData() {
      const queryData = {
        projectId: this.projectId
      };
      const res = await customApi.queryAttributedimensionTreeList(queryData);
      this.CetTable_treeList.data = (res?.data ?? []).map(item => {
        return {
          ...item,
          switchChangeDisabled: item.status === false
        };
      });
    },
    async queryTreeData() {
      if (!this.currentRow?.id || !this.currentRow.modelLabel) {
        this.CetGiantTree_1 && (this.CetGiantTree_1.inputData_in = []);
        return;
      }
      const queryData = { dimTreeConfigId: this.currentRow.id };
      const res = await customApi.queryAttributedimensionTreeNodeinfo(
        queryData
      );
      this.CetGiantTree_1.inputData_in = res?.data ?? [];
    },
    CetButton_addTree_statusTrigger_out() {
      this.addTree.openTrigger_in = Date.now();
    },
    CetTable_treeList_record_out(val) {
      this.currentRow = this._.cloneDeep(val);
      this.radio = val.id;
      this.queryTreeData();
    },
    CetGiantTree_1_currentNode_out(val) {
      this.currentNode = this._.cloneDeep(val);
    },
    async dleteTree(val) {
      if (val.status) {
        this.$message.warning($T("停用节点树后才能删除"));
        return;
      }
      this.$confirm($T("是否确认删除?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(async () => {
          const res = await customApi.deleteAttributedimensionTreeStatus([
            val.id
          ]);
          if (res?.code) return;
          this.$message.success($T("删除成功"));
          this.getTableData();
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    editNameHandle(val) {
      this.editName.inputData_in = this._.cloneDeep(val);
      this.editName.openTrigger_in = Date.now();
    }
  },
  mounted() {
    this.getTableData();
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: var(--Ra);
  .mainBox {
    border-radius: var(--Ra);
  }
  .el-aside,
  .el-main {
    width: 0;
    flex: 1;
  }
  .title {
    font-weight: bold;
    @include font_color(T2);
    @include font_size(H3);
  }
  :deep() {
    .el-radio__label {
      display: none;
    }
    .el-switch__core::after {
      @include background_color(T5);
    }
    .el-switch__core {
      @include background_color(B1);
      @include border_color(B1);
    }
    .is-checked .el-switch__core {
      @include background_color(ZS);
      @include border_color(ZS);
    }
  }
  .gianttree {
    height: calc(100% - 16px);
  }
}
</style>
