<template>
  <div class="editMonthBox general">
    <el-table :data="calendarData" border style="width: 100%">
      <el-table-column
        v-for="(day, index) in weekDays"
        :key="index"
        :prop="index.toString()"
        :label="day.label"
        align="left"
      >
        <template #default="{ row }">
          <div
            class="day-cell"
            :class="{
              weekend: index === 0 || index === 6
            }"
          >
            <div class="day-number text-T4 text-Aa mb-J0">
              {{ row[index].day }}
            </div>
            <template v-if="row[index].day">
              <el-tooltip :content="valueFormat(row[index].value)">
                <div class="day-content text-H3 text-ellipsis" v-if="disabled">
                  {{ valueFormat(row[index].value) }}
                </div>
              </el-tooltip>

              <ElInputNumber
                v-if="!disabled"
                :disabled="
                  $moment(row[index].date).startOf('month') <
                  $moment().startOf('month')
                "
                v-model="row[index].value"
                v-bind="ElInputNumber_1"
                @change="valueChange(row[index].date, row[index].value)"
              ></ElInputNumber>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import omegaI18n from "@omega/i18n";
import common from "eem-base/utils/common";
export default {
  name: "EditMonth",
  props: {
    data_in: Array,
    date: [String, Date, Number],
    disabled: Boolean,
    update_in: Number
  },
  data() {
    const en = omegaI18n.locale === "en";
    return {
      calendarData: [],
      weekDays: [
        { label: en ? "Sun" : "日" },
        { label: en ? "Mon" : "一" },
        { label: en ? "Tue" : "二" },
        { label: en ? "Wed" : "三" },
        { label: en ? "Thu" : "四" },
        { label: en ? "Fri" : "五" },
        { label: en ? "Sat" : "六" }
      ],
      ElInputNumber_1: {
        value: "",
        style: {
          width: "100%"
        },
        ...common.check_numberFloat,
        controls: false,
        placeholder: $T("请输入"),
        event: {}
      }
    };
  },
  watch: {
    update_in: {
      handler: function () {
        this.updateCalendar();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    valueFormat(val) {
      if (val == null) {
        return "--";
      }
      return val.toFixed(2);
    },
    updateCalendar() {
      const data = this.data_in || [];
      const start = +this.$moment(this.date).startOf("month");
      const end = +this.$moment(this.date).endOf("month");
      const dataMap = {};
      data.forEach(({ aggregationcycle, validtime, limitvalue }) => {
        if (validtime >= start && validtime <= end && aggregationcycle === 12) {
          dataMap[validtime] = limitvalue;
        }
      });
      this.calendarData = this.generateMonthCalendar(this.date, dataMap);
    },
    generateMonthCalendar(date, dataMap) {
      const currentMonth = this.$moment(date).startOf("month");
      const daysInMonth = currentMonth.daysInMonth();
      const firstDayOfWeek = currentMonth.day();

      const calendar = [];
      let dayCount = 1;
      const totalWeeks = Math.ceil((firstDayOfWeek + daysInMonth) / 7);

      for (let week = 0; week < totalWeeks; week++) {
        const weekDays = [];

        for (let day = 0; day < 7; day++) {
          if ((week === 0 && day < firstDayOfWeek) || dayCount > daysInMonth) {
            weekDays.push({
              date: null,
              day: "",
              moment: null,
              isCurrentMonth: false
            });
          } else {
            const currentDate = currentMonth.clone().date(dayCount);
            weekDays.push({
              date: +currentDate,
              day: dayCount,
              moment: currentDate,
              isCurrentMonth: true,
              value: dataMap[+currentDate]
            });
            dayCount++;
          }
        }

        calendar.push(weekDays);
      }

      return calendar;
    },
    valueChange(time, val) {
      this.$emit("valueChange", time, val);
    }
  }
};
</script>
<style lang="scss" scoped>
.day-cell {
  height: 80px;
  padding: var(--J0) 0;
}
</style>
