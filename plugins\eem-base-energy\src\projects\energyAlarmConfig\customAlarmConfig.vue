<template>
  <div class="h-full w-full flex flex-col">
    <div class="flex flex-row mb-J3 justify-between">
      <div class="flex flex-row items-center">
        <el-button
          size="small"
          icon="el-icon-arrow-left"
          @click="yearQueryPrv"
          class="mr-J0"
        ></el-button>
        <CustomElDatePicker
          class="yearPicker"
          :prefix_in="$T('时间')"
          v-model="yearTime"
          type="year"
          size="small"
          value-format="timestamp"
          :placeholder="$T('选择日期')"
          :clearable="false"
          :format="$T('yyyy 年')"
          @change="yearTimeChange"
        ></CustomElDatePicker>
        <el-button
          size="small"
          icon="el-icon-arrow-right"
          @click="yearQueryNext"
          class="ml-J0"
        ></el-button>
      </div>
      <div class="flex flex-row justify-end">
        <el-button
          size="small"
          icon="el-icon-arrow-left"
          @click="monthQueryPrv"
          class="mr-J0"
        ></el-button>
        <CustomElDatePicker
          :prefix_in="$T('时间')"
          v-model="monthTime"
          type="month"
          size="small"
          value-format="timestamp"
          :placeholder="$T('选择日期')"
          :clearable="false"
          :format="$T('yyyy 年 MM 月')"
          @change="monthTimeChange"
          popperClass="energyAlarmConfig_limitationCurrentYear"
        ></CustomElDatePicker>
        <el-button
          size="small"
          icon="el-icon-arrow-right"
          @click="monthQueryNext"
          class="ml-J0"
        ></el-button>
      </div>
    </div>
    <div class="flex flex-row flex-auto">
      <div class="w-[220px] mr-J3 flex flex-col">
        <div
          class="flex flex-row items-center border rounded-Ra border-B1 border-solid"
        >
          <span class="w-[90px] text-center p-J0">
            {{ $T("年度") }}
          </span>
          <ElInputNumber
            class="text-center flex-auto h-full"
            v-model="yearConfigValue"
            :disabled="$moment(yearTime).year() < $moment().year()"
            v-bind="ElInputNumber_1"
            @change="yearValueChange"
          ></ElInputNumber>
        </div>
        <el-table
          class="flex-auto mt-J3"
          :data="monthData"
          highlight-current-row
          border
          height="true"
        >
          <el-table-column
            prop="key1"
            :label="$T('时间')"
            align="left"
            width="80"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="key2"
            align="left"
            :label="$T('能耗阈值')"
            show-overflow-tooltip
          >
            <div class="flex flex-row" slot-scope="scope">
              <ElInputNumber
                v-model="scope.row.key2"
                :disabled="monthChangeDisabled(scope.$index)"
                v-bind="ElInputNumber_1"
                @change="monthValueChange(scope.$index, scope.row.key2)"
              ></ElInputNumber>
              <div
                class="iconImg ml-J0"
                v-if="!monthChangeDisabled(scope.$index)"
                @click="monthSplit(scope.row.key2, scope.$index)"
              ></div>
            </div>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex-auto flex flex-col">
        <div class="flex-auto flex flex-row">
          <div class="w-[210px] mr-J3">
            <el-table class="h-full" :data="weekTableData" border height="true">
              <el-table-column
                prop="key1"
                :label="$T('周数')"
                align="left"
                width="70"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="key2"
                align="left"
                :label="$T('周能耗阈值')"
                show-overflow-tooltip
              >
                <div class="flex flex-row" slot-scope="scope">
                  <ElInputNumber
                    v-model="scope.row.key2"
                    :disabled="
                      $moment(monthTime).startOf('month') <
                      $moment().startOf('month')
                    "
                    v-bind="ElInputNumber_1"
                    @change="weekValueChange(scope.row.time, scope.row.key2)"
                  ></ElInputNumber>
                  <div
                    class="iconImg ml-J0"
                    v-if="
                      $moment(monthTime).startOf('month') >=
                      $moment().startOf('month')
                    "
                    @click="weekSplit(scope.row.key2, scope.row.time)"
                  ></div>
                </div>
              </el-table-column>
            </el-table>
          </div>
          <div class="flex-auto overflow-auto">
            <EditMonth
              v-bind="editMonth"
              :data_in="benchmarksetConfig"
              @valueChange="dateValueChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EditMonth from "./EditMonth.vue";
import common from "eem-base/utils/common";
export default {
  name: "aiAlarmConfig",
  props: {
    data_in: Object,
    selectYearTime: Number,
    initDate_in: Number
  },
  components: {
    EditMonth
  },
  data() {
    const initMonthData = [
      {
        key1: $T("1月"),
        key2: undefined
      },
      {
        key1: $T("2月"),
        key2: undefined
      },
      {
        key1: $T("3月"),
        key2: undefined
      },
      {
        key1: $T("4月"),
        key2: undefined
      },
      {
        key1: $T("5月"),
        key2: undefined
      },
      {
        key1: $T("6月"),
        key2: undefined
      },
      {
        key1: $T("7月"),
        key2: undefined
      },
      {
        key1: $T("8月"),
        key2: undefined
      },
      {
        key1: $T("9月"),
        key2: undefined
      },
      {
        key1: $T("10月"),
        key2: undefined
      },
      {
        key1: $T("11月"),
        key2: undefined
      },
      {
        key1: $T("12月"),
        key2: undefined
      }
    ];
    return {
      benchmarksetConfig: [],
      yearTime: +this.$moment(),
      monthTime: +this.$moment(),
      yearConfigValue: undefined,
      initMonthData: initMonthData,
      monthData: this._.cloneDeep(initMonthData),
      weekTableData: [],
      calendarValue: Date.now(),
      editMonth: {
        data: [],
        date: Date.now(),
        update_in: Date.now()
      },
      ElInputNumber_1: {
        value: "",
        style: {
          width: "100%"
        },
        ...common.check_numberFloat,
        controls: false,
        placeholder: $T("请输入"),
        event: {}
      }
    };
  },
  watch: {
    data_in: {
      handler(val) {
        this.initData(val);
      },
      deep: true,
      immediate: true
    },
    initDate_in: {
      handler() {
        this.initDate();
      }
    }
  },
  methods: {
    initDate() {
      this.yearTime = +this.$moment();
      this.monthTime = +this.$moment();
      this.yearTimeChange();
    },
    initData() {
      this.benchmarksetConfig = this._.cloneDeep(
        this.data_in?.benchmarkset_model ?? []
      );
      this.yearDataHandle();
      this.monthTimeChange();
    },
    yearQueryPrv() {
      const value = this.$moment(this.yearTime);
      this.yearTime = +value.subtract(1, "years");
      this.yearTimeChange();
    },
    yearQueryNext() {
      const value = this.$moment(this.yearTime);
      this.yearTime = +value.add(1, "years");
      this.yearTimeChange();
    },
    monthQueryPrv() {
      const value = this.$moment(this.monthTime);

      const year = this.$moment(this.yearTime).year();
      this.monthTime = +value.subtract(1, "months").year(year);
      this.monthTimeChange();
    },
    monthQueryNext() {
      const value = this.$moment(this.monthTime);

      const year = this.$moment(this.yearTime).year();
      this.monthTime = +value.add(1, "months").year(year);
      this.monthTimeChange();
    },
    yearTimeChange() {
      this.$emit("update:selectYearTime", this.yearTime);
      this.$emit("yearTimeChange", this.yearTime);

      const value = this.$moment(this.monthTime);
      const year = this.$moment(this.yearTime).year();
      this.monthTime = +value.year(year);
    },
    yearDataHandle() {
      const year = +this.$moment(this.yearTime).startOf("year");

      // 处理年数据
      const yearConfig = this.benchmarksetConfig.find(
        item => item.validtime === year
      );
      this.yearConfigValue =
        yearConfig?.limitvalue != null ? yearConfig.limitvalue : undefined;

      // 处理月数据
      this.monthData = this._.cloneDeep(this.initMonthData);
      this.benchmarksetConfig.forEach(
        ({ aggregationcycle, validtime, limitvalue }) => {
          if (
            +this.$moment(validtime).startOf("year") === year &&
            aggregationcycle === 14
          ) {
            this.monthData[this.$moment(validtime).month()].key2 =
              limitvalue == null ? undefined : limitvalue;
          }
        }
      );
    },
    async monthTimeChange() {
      this.$emit("monthTimeChange", this.monthTime);
      // 处理周数据
      const startDay = this.$moment(this.monthTime)
        .startOf("month")
        .day(1)
        .startOf("date");
      const month = this.$moment(this.monthTime).month();
      const weekTableData = [
        {
          key1: startDay.week(),
          key2: undefined,
          time: +this.$moment(startDay)
        }
      ];
      while (startDay.add("days", 7).month() == month) {
        const time = +this.$moment(startDay);
        const weekConfig = this.benchmarksetConfig.find(
          ({ validtime, aggregationcycle }) => {
            return validtime === time && aggregationcycle === 13;
          }
        );
        weekTableData.push({
          key1: this.$moment(startDay).week(),
          key2:
            weekConfig?.limitvalue == null ? undefined : weekConfig?.limitvalue,
          time
        });
      }
      this.weekTableData = weekTableData;

      this.editMonth.date = this.monthTime;
      this.editMonth.update_in = Date.now();
    },
    /**
     * 年阈值变化
     */
    yearValueChange(val) {
      this.valueChange(this.$moment(this.yearTime).startOf("year"), 17, val);
    },
    /**
     * 月阈值变化
     */
    monthValueChange(month, val) {
      const time = +this.$moment(this.yearTime).month(month).startOf("month");
      this.valueChange(time, 14, val);
    },
    /**
     * 周阈值变化
     */
    weekValueChange(time_in, val) {
      const time = +this.$moment(time_in).startOf("date");
      this.valueChange(time, 13, val);
    },
    /**
     * 周阈值变化
     */
    dateValueChange(time_in, val) {
      const time = +this.$moment(time_in).startOf("date");
      this.valueChange(time, 12, val);
    },

    /**
     * 阈值变化
     * @param time 时间戳
     * @param cycle 周期
     * @param val 值
     */
    valueChange(time, cycle, val) {
      let oldData = this.benchmarksetConfig.find(
        item => item.validtime === time && item.aggregationcycle === cycle
      );
      const value = val === undefined ? null : val;
      if (!oldData) {
        oldData = {
          validtime: time,
          limitvalue: value,
          aggregationcycle: cycle,
          limittype: 1,
          modelLabel: "benchmarkset"
        };
        this.benchmarksetConfig.push(oldData);
        return;
      }
      oldData.limitvalue = value;
    },
    /**
     * 给外部送数据
     */
    getSaveData() {
      return { benchmarkset_model: this.benchmarksetConfig };
    },
    /**
     * 分摊到月
     */
    monthSplit(val, month) {
      if (val == null) {
        this.$message.warning($T("请输入阈值"));
        return;
      }
      const start = this.$moment(this.yearTime).month(month).startOf("month");
      const end = this.$moment(this.yearTime).month(month).endOf("month");
      const num = end.date();

      const value = Number((val / num).toFixed(2));
      this.thresholdSplit(value, +start, +end);
      // 跳转到目标月份
      this.monthTime = +this.$moment(this.monthTime).month(month);
      this.monthTimeChange();
    },
    /**
     * 分摊到周
     */
    weekSplit(val, time) {
      if (val == null) {
        this.$message.warning($T("请输入阈值"));
        return;
      }
      const year = this.$moment(this.yearTime).year();
      const start = this.$moment(time).startOf("date");
      let end = this.$moment(time).add(6, "d").endOf("date");

      if (end.year() != year) {
        end = this.$moment(this.yearTime).endOf("year");
      }
      const value = Number((val / 7).toFixed(2));
      this.thresholdSplit(value, +start, +end);
    },
    /**
     * 分摊
     */
    thresholdSplit(val, start, end) {
      let copyStart = this.$moment(start);
      while (copyStart.isBefore(end)) {
        this.valueChange(+copyStart, 12, val);
        copyStart = copyStart.add(1, "day");
      }
      this.editMonth.update_in = Date.now();
    },
    /**
     * 月阈值是否允许编辑
     * @param index 索引
     */
    monthChangeDisabled(index) {
      return (
        this.$moment(this.yearTime).year() < this.$moment().year() ||
        (this.$moment(this.yearTime).year() === this.$moment().year() &&
          index < this.$moment().month())
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.yearPicker :deep(.el-date-editor.el-input) {
  width: 160px;
}
.calendar :deep(.el-calendar__header) {
  display: none;
}
.iconImg {
  background: url(./assets/u20578.png) no-repeat center center;
  @include background_color(ZS);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  margin-top: 4px;
}
</style>

<style>
.energyAlarmConfig_limitationCurrentYear .el-date-picker__header {
  display: none;
}
</style>
