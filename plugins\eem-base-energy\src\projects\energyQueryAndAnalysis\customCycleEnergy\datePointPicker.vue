<template>
  <div class="wrap el-date-picker__custom">
    <span class="plJ1 prJ1 label" v-if="type !== 'times'">
      {{ prefix_in }}
    </span>
    <el-date-picker
      v-if="type === 'times'"
      value-format="timestamp"
      :format="dateFormat"
      v-model="dateValue"
      type="datetimerange"
      :range-separator="$T('至')"
      :start-placeholder="$T('开始日期')"
      :end-placeholder="$T('结束日期')"
      :picker-options="timeOptions"
      @change="timePickerChange"
      @focus="timePickerFocus"
      popper-class="customCycleEnergy_timePicker"
    ></el-date-picker>
    <customDatePointPicker
      v-else
      class="datePicker"
      ref="datePicker"
      value-format="timestamp"
      :format="dateFormat"
      :type="dateType"
      :clearable="true"
      v-model="dateValue"
      :placeholder="$T('请选择')"
      :picker-options="pickerOptions"
      @change="pickerChange"
      :weeksModel="type === 'weeks'"
      :selectNumMax="selectNumMax"
    ></customDatePointPicker>
  </div>
</template>

<script>
import Vue from "vue";
import customDatePointPicker from "./date-picker/index.js";
Vue.component("customDatePointPicker", customDatePointPicker);
const TYPES = ["dates", "weeks", "months", "years", "times"];
/*
  times单独处理
*/
export default {
  props: {
    value: Array,
    type: String,
    selectNumMax: {
      type: Number,
      default: () => 20
    },
    prefix_in: String
  },
  data(vm) {
    return {
      dateValue: [],
      dateType: "dates",
      pickerOptions: {
        firstDayOfWeek: 1
      },
      dateFormat: "",
      timePickerStart: null,
      timePickerEnd: null,
      timeOptions: {
        firstDayOfWeek: 1,
        disabledDate: time => {
          if (!vm.timePickerEnd && !vm.timePickerStart) {
            return false;
          }
          return (
            time.getTime() > vm.timePickerEnd ||
            time.getTime() < vm.timePickerStart
          );
        },
        onPick({ maxDate, minDate }) {
          if (!maxDate && minDate) {
            // 第一次选择前后取两天
            let startTime = vm.$moment(minDate).add(-2, "d").valueOf(),
              endTime = vm.$moment(minDate).add(2, "d").valueOf();
            vm.timePickerEnd = endTime;
            vm.timePickerStart = startTime;
          } else if (maxDate && minDate) {
            vm.timePickerEnd = null;
            vm.timePickerStart = null;
          }
        }
      }
    };
  },
  watch: {
    type: {
      handler(val) {
        if (TYPES.includes(val)) {
          if (val === "weeks") {
            this.weeksHandle();
          } else {
            this.dateType = val;
          }
        } else {
          this.dateType = "dates";
        }
        this.setDateFormat(val);
        if (val !== "times") {
          this.init();
        }
      },
      immediate: true
    },
    value: {
      handler(val) {
        this.dateValue = val;
      },
      deep: true,
      immediate: true
    },
    prefix_in() {
      this.init();
    }
  },
  methods: {
    // 周单独处理，换成日
    weeksHandle() {
      this.dateType = "dates";
    },
    setDateFormat(val) {
      switch (val) {
        case "times":
          this.dateFormat = "yyyy-MM-dd HH:mm";
          break;
        case "dates":
          this.dateFormat = "yyyy-MM-DD";
          break;
        case "weeks":
          this.dateFormat = ""; //周在内部进行处理
          break;
        case "months":
          this.dateFormat = "yyyy-MM";
          break;
        case "years":
          this.dateFormat = "yyyy";
          break;

        default:
          this.dateFormat = "yyyy-MM-DD";
          break;
      }
    },
    pickerChange() {
      this.dateOut();
    },
    dateOut() {
      if (!this.dateValue) {
        this.$emit("update:value", []);
        this.$emit("change", []);
        return;
      }
      if (this.dateValue.length > this.selectNumMax) {
        this.$message({
          type: "warning",
          message: $T('选择时间个数超过限制{0}个', this.selectNumMax)
        });
        this.dateValue = this.value;
        return;
      }
      // 排序
      this.dateValue = this.dateValue.sort();
      this.$emit("update:value", this.dateValue);
      this.$emit("change", this.dateValue);
    },
    timePickerChange() {
      if (
        this.$moment(this.dateValue[0]).add(72, "H").valueOf() <
        this.$moment(this.dateValue[1]).valueOf()
      ) {
        this.$message({
          type: "warning",
          message: $T('选择时间个数超过限制')
        });
        this.dateValue = this.value;
        return;
      }
      this.dateOut();
    },
    timePickerFocus() {
      this.timePickerEnd = null;
      this.timePickerStart = null;
    },
    async init() {
      if (!this.prefix_in) return;
      let span = document.createElement("span");
      span.innerText = this.prefix_in;
      span.style.fontSize = "12px";
      document.getElementsByTagName("body")[0].append(span);
      let offsetWidth = span.offsetWidth;
      span.remove();
      await this.$nextTick();
      if (!this.$refs.datePicker) {
        return;
      }
      let dom = $(this.$refs.datePicker.$el);
      if (dom.attr("class").includes("el-range-editor")) {
        $(dom).css({
          "padding-left": offsetWidth + 22 + "px"
        });
      } else {
        $(dom)
          .find(".el-input__inner")
          .css({
            "padding-left": offsetWidth + 30 + "px"
          });
      }
    }
  },
  mounted() {
    this.init();
  },
  activated() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  display: inline-block;
}
.el-date-picker__custom {
  display: inline-block;
  position: relative;
  .label {
    position: absolute;
    left: 4px;
    @include font_color(ZS);
    @include font_size("Aa");
    @include line_height(Hm);
    z-index: 1;
  }
  .datePicker {
    box-sizing: border-box;
    :deep(.el-input__inner) {
      @include font_size("Aa");
    }
    :deep(.el-input__prefix) {
      left: auto;
      right: 5px;
    }
    :deep(.el-input__icon.el-range__icon.el-icon-date) {
      position: absolute;
      right: 5px;
    }
    :deep(.el-input__suffix) {
      right: 25px;
    }
  }
}
</style>
<style lang="scss">
.customCycleEnergy_timePicker .el-time-spinner {
  .el-time-spinner__wrapper:nth-child(1) {
    width: 100%;
  }
  .el-time-spinner__wrapper:nth-child(2) {
    display: none;
  }
}
</style>
