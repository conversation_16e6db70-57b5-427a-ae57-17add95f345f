<template>
  <div class="page bg-BG1 rounded-Ra p-J4 box-border">
    <div class="fullheight flex-col flex">
      <publicHeader
        :isDelete="deletePlanIds && deletePlanIds.length > 0"
        @update_tableData="update_tableData"
        @new_plan="new_plan"
        @delete_scheme="delete_scheme"
      ></publicHeader>
      <div class="mt-J3 rounded-Ra bg-BG5 p-J1">
        <span>
          {{
            $T(
              "配置规则：未配置分摊方案的节点，会基于拓扑结构，根据子级节点能耗的比例分摊父级与子级之间的损耗数据！"
            )
          }}
        </span>
      </div>
      <div class="mt-J3 flex-auto flex-col flex">
        <CetTable
          ref="table"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn
            :label="$T('操作')"
            width="190"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <span class="handle mr-J0" @click.stop="toDetail(scope)">
                {{ $T("详情") }}
              </span>
              <span
                class="handle mr-J0"
                @click.stop="toEdit(scope)"
                v-permission="'lossshareconfig_update'"
              >
                {{ $T("编辑") }}
              </span>
              <span
                class="handle"
                @click.stop="copyScheme(scope)"
                v-permission="'lossshareconfig_update'"
              >
                {{ $T("复制方案") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
        <div class="text-right mt-J3">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
          ></el-pagination>
        </div>
      </div>
    </div>
    <addPlan v-bind="addPlan" @finishTrigger_out="finishTrigger_out"></addPlan>
    <detailView v-bind="detailView"></detailView>
  </div>
</template>

<script>
import publicHeader from "./subcomponents/publicHeader.vue";
import addPlan from "./subcomponents/addPlan.vue";
import detailView from "./subcomponents/detailView.vue";
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import omegaTheme from "@omega/theme";

export default {
  name: "publicSchemeConfig",
  components: { publicHeader, addPlan, detailView },
  computed: {
    themeLight() {
      return omegaTheme.theme === "light";
    }
  },
  data() {
    return {
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        isTreeData: true,
        event: {}
      },
      Columns_1: [
        {
          type: "",
          prop: "index", // selection 勾选 index 序号
          label: $T("序号"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: "70" //绝对宽度
        },
        {
          prop: "objectnameCopy", // 支持path a[0].b
          label: $T("被分摊对象"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "140" //该宽度会自适应
        },
        {
          prop: "objectEnergyTypeNameCopy", // 支持path a[0].b
          label: $T("被分摊能源类型"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120" //该宽度会自适应
        },
        {
          prop: "name", // 支持path a[0].b
          label: $T("方案名称"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "energyShareMethodName", // 支持path a[0].b
          label: $T("分摊方式"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "120", //该宽度会自适应
          formatter: common.formatTextCol()
        },
        {
          prop: "timeText", // 支持path a[0].b
          label: $T("生效时间"), //列名
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          minWidth: "200", //该宽度会自适应
          formatter: common.formatTextCol()
        }
      ],
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      addPlan: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      detailView: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      energyOptions: [],
      detailInfoData: {},
      timeOptions: [
        {
          id: 7,
          text: $T("小时")
        },
        {
          id: 12,
          text: $T("日")
        },
        {
          id: 14,
          text: $T("月")
        }
      ],
      // 删除方案id集合
      deletePlanIds: [],
      // 筛选数据
      param: {}
    };
  },
  methods: {
    // 筛选条件数据更新
    update_tableData(val, options) {
      this.energyOptions = options;
      this.param = val;
      this.getTableData(val);
    },
    // 打开详情页面
    async toDetail(scope) {
      const res = await this.getDetailInfo(scope.row);
      if (!res) return;
      this.detailView.openTrigger_in = new Date().getTime();
      this.detailView.inputData_in = this._.cloneDeep(this.detailInfoData);
    },
    async toEdit(scope) {
      const res = await this.getDetailInfo(scope.row);
      if (!res) return;
      this.addPlan.openTrigger_in = new Date().getTime();
      this.addPlan.inputData_in = this._.cloneDeep(this.detailInfoData);
    },
    async copyScheme(scope) {
      const res = await this.getDetailInfo(scope.row);
      if (!res) return;
      this.addPlan.openTrigger_in = new Date().getTime();
      let data = this._.cloneDeep(this.detailInfoData);
      data.id = 0;
      data.name = data.name + "-" + $T("副本");
      this.addPlan.inputData_in = data;
    },
    // 批量选中进行删除操作
    handleSelectionChange(val) {
      this.deletePlanIds = [];
      if (val.length) {
        val.forEach(item => {
          this.deletePlanIds.push(item.id);
        });
      }
    },
    // 调用删除接口
    delete_scheme() {
      if (!this.deletePlanIds.length) {
        return this.$message.warning($T("请选择要删除的方案！"));
      }
      this.$confirm($T("确定要删除所选方案吗？"), $T("删除确认"), {
        type: "warning",
        distinguishCancelAndClose: true,
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消")
      })
        .then(() => {
          customApi
            .deleteLossShareConfig(this.deletePlanIds, { withCheck: true })
            .then(res => {
              if (res.code === 0) {
                this.$message.success($T("删除成功"));
                this.getTableData();
              }
            });
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    // 分段操作
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.param.page.index = 0;
      this.param.page.limit = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.param.page.index = (val - 1) * this.pageSize;
      this.getTableData();
    },
    // 新增方案
    new_plan() {
      this.addPlan.openTrigger_in = new Date().getTime();
      this.addPlan.inputData_in = {};
    },
    getTableData() {
      customApi.getLossShareConfigGroup(this.param).then(res => {
        if (res.code === 0) {
          let data = this._.get(res, "data.lossShareConfigGroupVoList", []);
          const nodeDeleteInfoVos = res.data?.nodeDeleteInfoVos ?? [];
          let tableTestData = [];
          if (data && data.length > 0) {
            data.forEach((item, index) => {
              item.lossShareConfigFirst.index = index + 1;
              item.lossShareConfigFirst.objectnameCopy =
                item.lossShareConfigFirst.shareObjectName;
              item.lossShareConfigFirst.objectEnergyTypeNameCopy =
                item.lossShareConfigFirst.shareEnergyTypeName;

              item.lossShareConfigFirst.timeText = this.getTimeText(
                item.lossShareConfigFirst
              );
              if (item.lossShareConfigVos && item.lossShareConfigVos.length) {
                item.lossShareConfigVos.forEach(key => {
                  key.timeText = this.getTimeText(key);
                });
                item.lossShareConfigFirst.children = item.lossShareConfigVos;
              }
              tableTestData.push(item.lossShareConfigFirst);
            });
          }
          this.CetTable_1.data = tableTestData;
          this.totalCount = res.total;
          this.$nextTick(() => {
            this.$refs.table.$refs.cetTable.doLayout();
          });

          this.deleteTips(nodeDeleteInfoVos);
        }
      });
    },
    // 提示是否删除有问题的方案
    async deleteTips(nodeDeleteInfoVos) {
      if (!nodeDeleteInfoVos?.length) return;
      let deleteFlag = false;
      await this.$confirm(
        `${nodeDeleteInfoVos.map(i => i.desc).join(",")},${$T("是否删除？")}`,
        $T("提示"),
        {
          type: "warning",
          distinguishCancelAndClose: true,
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消")
        }
      )
        .then(async () => {
          deleteFlag = true;
          const res = await customApi.deleteLossShareConfig(
            nodeDeleteInfoVos.map(i => i.lossShareConfigId),
            { withCheck: false }
          );
          if (res.code !== 0) return;
          this.$message.success($T("删除成功"));
          this.getTableData();
        })
        .catch(() => {
          deleteFlag = false;
          this.$message.info($T("已取消"));
        });
      return !deleteFlag;
    },
    getTimeText(item) {
      let timeText = "";
      if (item.endTime) {
        if (item.endTime == 9999000000000) {
          timeText =
            this.$moment(item.startTime).format("YYYY-MM-DD") +
            ` ${$T("至")} ` +
            ` ${$T("永远")}`;
        } else {
          timeText =
            this.$moment(item.startTime).format("YYYY-MM-DD") +
            ` ${$T("至")} ` +
            this.$moment(item.endTime).format("YYYY-MM-DD");
        }
      } else {
        timeText = this.$moment(item.startTime).format("YYYY-MM-DD");
      }
      return timeText;
    },
    // 获取分摊方案的详细数据信息
    async getDetailInfo(val) {
      if (!(val && val.id)) {
        return;
      }
      this.detailInfoData = {};
      let nodeDeleteInfoVos = [];
      await customApi.getLossShareConfigInfo(val.id).then(res => {
        if (res.code == 0) {
          let data = this._.get(res, "data.lossShareConfigWithLayerVos", []);
          nodeDeleteInfoVos = res.data?.nodeDeleteInfoVos ?? [];
          if (data.length > 0) {
            data.forEach(item => {
              item.energysharemethodText =
                item.energyShareMethod == 1
                  ? $T("固定比例")
                  : item.energyShareMethod == 2
                  ? $T("动态分摊")
                  : "";
              item.energytypeText = this.energyOptions.find(
                x => x.id === item.shareEnergyType
              )?.text;
              item.objectenergytypeText = this.energyOptions.find(
                x => x.id === item.shareEnergyType
              )?.text;
              item.cycleText = this.timeOptions.find(
                x => x.id === item.shareCalcCycle
              )?.text;
              item.objectname = val.shareObjectName;
              if (item.endTime) {
                item.isForever = false;
                if (item.endTime == 9999000000000) {
                  item.isForever = true;
                  item.timeText =
                    this.$moment(item.startTime).format("YYYY-MM-DD") +
                    ` ${$T("至")} ` +
                    ` ${$T("永远")}`;
                } else {
                  item.timeText =
                    this.$moment(item.startTime).format("YYYY-MM-DD") +
                    ` ${$T("至")} ` +
                    this.$moment(item.endTime).format("YYYY-MM-DD");
                }
              } else {
                item.timeText = this.$moment(item.startTime).format(
                  "YYYY-MM-DD"
                );
              }
            });
          }
          this.detailInfoData = data[0];
        }
      });
      if (!nodeDeleteInfoVos?.length) {
        return true;
      }
      return await this.deleteTips(nodeDeleteInfoVos);
    },
    finishTrigger_out() {
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.getTableData(this.param);
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  .handle {
    cursor: pointer;
    @include font_color(ZS);
  }
}
</style>
