<template>
  <div class="flex-row flex items-center">
    <div class="flex-auto flex-row flex items-center">
      <ElInput
        class="mr-J1"
        v-model="ElInput_1.value"
        v-bind="ElInput_1"
        v-on="ElInput_1.event"
      ></ElInput>
      <customElSelect
        class="mr-J1"
        :prefix_in="$T('被分摊能源类型')"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </customElSelect>
      <customElSelect
        class="mr-J1"
        :prefix_in="$T('分摊方式')"
        v-model="ElSelect_2.value"
        v-bind="ElSelect_2"
        v-on="ElSelect_2.event"
      >
        <ElOption
          v-for="item in ElOption_2.options_in"
          :key="item[ElOption_2.key]"
          :label="item[ElOption_2.label]"
          :value="item[ElOption_2.value]"
          :disabled="item[ElOption_2.disabled]"
        ></ElOption>
      </customElSelect>
      <CustomElDatePicker
        class="mr-J1"
        :prefix_in="$T('生效时间')"
        v-bind="CetDatePicker_time.config"
        v-model="CetDatePicker_time.val"
        @change="updateDate"
      />
    </div>
    <CetButton
      v-permission="'lossshareconfig_update'"
      class="mr-J1"
      v-bind="CetButton_recalculation"
      v-on="CetButton_recalculation.event"
    ></CetButton>
    <CetButton
      v-permission="'lossshareconfig_update'"
      class="mr-J1"
      v-bind="CetButton_2"
      v-on="CetButton_2.event"
    ></CetButton>
    <el-dropdown @command="handleCommand">
      <span class="el-dropdown-link">
        {{ $T("更多") }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="query">
          {{ $T("高级查询") }}
        </el-dropdown-item>
        <el-dropdown-item
          v-permission="'lossshareconfig_update'"
          command="delete"
          :class="isDelete ? 'delete' : 'disabled'"
        >
          {{ $T("批量删除") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <advancedQuery
      v-bind="advancedQuery"
      @query_update="query_update"
    ></advancedQuery>
    <recalculation
      v-bind="recalculation"
      @updataReCalcState_out="updataReCalcState_out"
    ></recalculation>
  </div>
</template>

<script>
import advancedQuery from "./advancedQuery.vue";
import customApi from "@/api/custom";
import recalculation from "./recalculation.vue";

export default {
  name: "publicHeader",
  components: { advancedQuery, recalculation },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },
  props: {
    isDelete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "180px"
        },
        event: {
          change: this.ElInput_1_change_out
        }
      },
      ElSelect_1: {
        value: "",
        style: {
          width: "250px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 分摊方式
      ElSelect_2: {
        value: 0,
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_2_change_out
        }
      },
      ElOption_2: {
        options_in: [
          {
            id: 0,
            text: $T("全部")
          },
          {
            id: 1,
            text: $T("固定比例")
          },
          {
            id: 2,
            text: $T("动态分摊")
          }
        ],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 选择时段
      CetDatePicker_time: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          unlinkPanels: true,
          rangeSeparator: $T("至")
        }
      },
      // 重算按钮
      CetButton_recalculation: {
        visible_in: true,
        disable_in: false,
        title: $T("重算"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_recalculation_statusTrigger_out
        }
      },
      // 新增方案
      CetButton_2: {
        visible_in: true,
        disable_in: false,
        title: $T("新增方案"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      // 高级查询
      advancedQuery: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        energyOptions: null
      },
      // 筛选条件集合
      param: {
        key: "",
        shareEnergyType: 0,
        energyShareMethod: 0,
        startTime: this.$moment().startOf("year").valueOf(),
        endTime: this.$moment().endOf("year").valueOf(),
        page: {
          index: 0,
          limit: 20
        }
      },
      // 重算
      recalculation: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      }
    };
  },
  methods: {
    // 初始化
    async init() {
      await this.getApportionedEnergy();
      this.ElSelect_2.value = 0;
      this.CetDatePicker_time.val = [
        this.$moment().startOf("year").valueOf(),
        this.$moment().endOf("year").valueOf()
      ];
      this.ElInput_1.value = "";
      delete this.param.baseVo;
      this.updateParam();
    },
    // 获取被分摊能源类型
    async getApportionedEnergy() {
      await customApi.getProjectEnergy().then(res => {
        if (res.code === 0) {
          let data = this._.get(res, "data", []);
          let selectData = [];
          if (data.length > 0) {
            data.forEach(item => {
              if (![13, 18, 22].includes(item.energytype)) {
                selectData.push({
                  id: item.energytype,
                  text: item.name
                });
              }
            });
            let param = {
              id: 0,
              text: $T("全部")
            };
            selectData.unshift(param);
          }
          this.ElOption_1.options_in = this._.cloneDeep(selectData);
          this.ElSelect_1.value = selectData.length ? selectData[0].id : null;
        }
      });
    },
    updateParam() {
      this.param.key = this.ElInput_1.value;
      this.param.shareEnergyType = this.ElSelect_1.value;
      this.param.energyShareMethod = this.ElSelect_2.value;
      this.param.startTime = this.$moment(
        this.CetDatePicker_time.val[0]
      ).valueOf();
      this.param.endTime = this.$moment(
        this.CetDatePicker_time.val[1]
      ).valueOf();
      this.$emit("update_tableData", this.param, this.ElOption_1.options_in);
    },
    // 更多触发
    handleCommand(command) {
      if (command === "query") {
        this.advancedQuery.openTrigger_in = new Date().getTime();
        this.advancedQuery.inputData_in = this._.cloneDeep(this.param);
        this.advancedQuery.energyOptions = this.ElOption_1.options_in;
      } else if (command === "delete") {
        this.$emit("delete_scheme");
      }
    },
    ElInput_1_change_out(val) {
      this.param.key = val;
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    ElSelect_1_change_out(val) {
      this.param.shareEnergyType = val;
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    ElSelect_2_change_out(val) {
      this.param.energyShareMethod = val;
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    updateDate(val) {
      this.param.startTime = this.$moment(val[0]).valueOf();
      this.param.endTime = this.$moment(val[1]).valueOf();
      this.param.page.index = 0;
      delete this.param.baseVo;
      this.updateParam();
    },
    CetButton_recalculation_statusTrigger_out(val) {
      this.recalculation.visibleTrigger_in = this._.cloneDeep(val);
      let projectNode = {
        objectId: this.projectId,
        objectLabel: "project"
      };
      this.recalculation.inputData_in = this._.cloneDeep(projectNode);
    },
    // 获取当前项目的重算状态并展示进度值
    getReCalcState() {
      customApi.getEnergyReCalcState(this.projectId).then(response => {
        if (response.code === 0) {
          if (response.data && response.data.state === 2) {
            let num = response.data.ratio.toFixed(1);
            this.CetButton_recalculation.disable_in = true;
            this.CetButton_recalculation.title = `${$T("重算中（{0}%）", num)}`;
          } else {
            this.CetButton_recalculation.disable_in = false;
            this.CetButton_recalculation.title = $T("重算");
          }
        }
      });
    },
    // 更新项目的重算状态
    updataReCalcState_out() {
      this.CetButton_recalculation.disable_in = true;
      setTimeout(() => {
        this.getReCalcState();
      }, 2500);
    },
    CetButton_2_statusTrigger_out() {
      this.$emit("new_plan");
    },
    query_update(val) {
      this.ElSelect_1.value = val.shareEnergyType;
      this.ElSelect_2.value = val.energyShareMethod;
      this.CetDatePicker_time.val = [val.startTime, val.endTime];
      this.param.shareEnergyType = val.shareEnergyType;
      this.param.energyShareMethod = val.energyShareMethod;
      this.param.startTime = val.startTime;
      this.param.endTime = val.endTime;
      this.param.baseVo = val.baseVo;
      this.param.page.index = 0;
      this.updateParam();
    }
  },
  mounted() {
    this.init();
    this.CetButton_recalculation.disable_in = true;
    // 定时器
    this.getReCalcState();
    this.setInterval = setInterval(() => {
      this.getReCalcState();
    }, 60000);
  },
  destroyed: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  },
  deactivated: function () {
    // 关闭定时器
    clearInterval(this.setInterval);
  }
};
</script>

<style lang="scss" scoped>
.delete {
  cursor: pointer;
  @include font_color(Sta3);
}
.disabled {
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
}
</style>
