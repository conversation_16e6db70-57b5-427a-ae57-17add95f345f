<template>
  <div :class="`overview-card-${type}__${theme} overview-card`">
    <div class="top">
      <div class="title text-ellipsis" :title="item.energyTypeName || '--'">
        {{ item.energyTypeName || "--" }}
      </div>
      <div class="top-right">
        <div :class="`amount-${type}__${theme} amount`">
          {{ item.value || item.value === 0 ? item.value.toFixed(2) : "--" }}
        </div>
        <div class="unit">{{ item.unit || "--" }}</div>
      </div>
    </div>
    <div :class="`inner-card_${theme} inner-card`">
      <div class="items-center">{{ $T("同比") }}</div>
      <div class="items-center">{{ $T("环比") }}</div>
      <div class="items-end" :class="getAmountClass(positiveY)">
        <div class="items-center">
          <omega-icon :symbolId="getSymbol(positiveY)" />
          <div class="value">
            {{ getYOY(item) }}
          </div>
        </div>
        <div class="unit">%</div>
      </div>
      <div class="items-end" :class="getAmountClass(positiveC)">
        <div class="items-center">
          <omega-icon :symbolId="getSymbol(positiveC)" />
          <div class="value">
            {{ getChain(item) }}
          </div>
        </div>
        <div class="unit">%</div>
      </div>
    </div>
  </div>
</template>
<script>
import omegaTheme from "@omega/theme";

export default {
  name: "OverviewCard",
  props: {
    type: {
      type: String
    },
    item: {
      type: Object
    },
    cycle: {
      type: Number
    }
  },
  data: function () {
    return {
      positiveY: false,
      positiveC: false
    };
  },
  methods: {
    getSymbol(val) {
      return val ? "up" : "down";
    },
    getAmountClass(val) {
      return val ? "percentage-up" : "percentage-down";
    },
    //同比
    getYOY(item) {
      if (item.value == null || !item.tbValue) {
        return "--";
      } else {
        let num = ((item.value - item.tbValue) / item.tbValue) * 100;
        this.positiveY = num >= 0;
        let str = num.toFixed(2);
        return str[0] === "-" ? str.substring(1, str.length) : str;
      }
    },
    //环比
    getChain(item) {
      if (item.value == null || !item.hbValue || this.cycle === 17) {
        return "--";
      } else {
        let num = ((item.value - item.hbValue) / item.hbValue) * 100;
        this.positiveC = num >= 0;
        let str = num.toFixed(2);
        return str[0] === "-" ? str.substring(1, str.length) : str;
      }
    }
  },
  computed: {
    theme: function () {
      return omegaTheme.theme === "light" ? "light" : "dark";
    }
  }
};
</script>
<style lang="scss" scoped>
.overview-card {
  display: flex;
  flex-direction: column;
  width: 322px;
  height: 100%;
  padding: 20px 16px;
  border-radius: 10px;
  box-sizing: border-box;
  background-size: 100% 100%;
  &-one__dark {
    background-image: url("../assets/one__dark.png");
  }
  &-two__dark {
    background-image: url("../assets/two__dark.png");
  }
  &-one__light {
    background-image: url("../assets/one__light.png");
  }
  &-two__light {
    background-image: url("../assets/two__light.png");
  }
}
.top {
  flex: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    @include font_color(T1);
    font-size: 14px;
    line-height: 22px;
    font-weight: bold;
  }
  .top-right {
    flex: none;
    display: flex;
    align-items: center;
    margin-left: 4px;
  }
}
.amount {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  background-clip: text;
  color: transparent;
  &-one__dark {
    background-image: linear-gradient(#ffffff, #57abff);
  }
  &-two__dark {
    background-image: linear-gradient(#ffffff, #b182fc);
  }
  &-one__light {
    background-image: linear-gradient(#6aefa1, #29b061);
  }
  &-two__light {
    background-image: linear-gradient(#c5bcff, #8d7bfe);
  }
}
.unit {
  margin-left: 4px;
  @include font_color(T2);
  line-height: 18px;
  font-size: 14px;
}
.items-center {
  display: flex;
  align-items: center;
}
.items-end {
  display: flex;
  align-items: flex-end;
}
.inner-card {
  height: 72px;
  box-sizing: border-box;
  margin-top: 10px;
  border-radius: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  padding: 10px 24px;
  row-gap: 6px;
  .unit {
    font-size: 10px;
  }
  .omega-icon {
    width: 11px;
    height: 11px;
    margin-right: 4px;
  }
  .value {
    font-weight: 500;
    font-size: 20px;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent;
  }
}
.inner-card_light {
  background-color: rgba(255, 255, 255, 0.3);
  .percentage-up {
    .omega-icon {
      color: #f95e5a;
    }
    .value {
      background: linear-gradient(180deg, #ffa8a6 0%, #f95e5a 100%);
    }
  }
  .percentage-down {
    .omega-icon {
      color: #29b061;
    }
    .value {
      background: linear-gradient(180deg, #58db8f 0%, #29b061 100%);
    }
  }
}
.inner-card_dark {
  background-color: rgba(255, 255, 255, 0.1);
  .percentage-up {
    .omega-icon {
      color: #ff8b8b;
    }
    .value {
      background: linear-gradient(180deg, #ffe1e1 0%, #ff8b8b 100%);
    }
  }
  .percentage-down {
    .omega-icon {
      color: #05f974;
    }
    .value {
      background: linear-gradient(180deg, #e5fff1 0%, #78ffb6 100%);
    }
  }
}
</style>
