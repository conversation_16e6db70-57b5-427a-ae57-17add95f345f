<template>
  <CustomElDrawer
    class="drawer"
    :title="$T('高级查询')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
    :confirmText_in="$T('确定')"
    @confirm_out="CetButton_confirm_statusTrigger_out"
  >
    <div class="flex-auto m-J1 flex-col flex">
      <div>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="label">
              {{ $T("被分摊能源类型") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1">
              <ElSelect
                class="mr-J1"
                v-model="ElSelect_1.value"
                v-bind="ElSelect_1"
                v-on="ElSelect_1.event"
              >
                <ElOption
                  v-for="item in ElOption_1.options_in"
                  :key="item[ElOption_1.key]"
                  :label="item[ElOption_1.label]"
                  :value="item[ElOption_1.value]"
                  :disabled="item[ElOption_1.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="label">
              {{ $T("分摊方式") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1">
              <ElSelect
                class="mr-J1"
                v-model="ElSelect_2.value"
                v-bind="ElSelect_2"
                v-on="ElSelect_2.event"
              >
                <ElOption
                  v-for="item in ElOption_2.options_in"
                  :key="item[ElOption_2.key]"
                  :label="item[ElOption_2.label]"
                  :value="item[ElOption_2.value]"
                  :disabled="item[ElOption_2.disabled]"
                ></ElOption>
              </ElSelect>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="16" class="mt-J3">
          <el-col :span="12">
            <div class="label">
              {{ $T("筛选时段") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1">
              <CustomElDatePicker
                class="mr-J1 w-full"
                v-bind="CetDatePicker_time.config"
                v-model="CetDatePicker_time.val"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="label">
              {{ $T("所属项目") }}
              <span style="color: red">*</span>
            </div>
            <div class="value mt-J1">
              <customElSelect
                v-model="ElSelect_4.value"
                v-bind="ElSelect_4"
                v-on="ElSelect_4.event"
                class="mr-J1 w-full"
              >
                <ElOption
                  v-for="item in rootNodes_in"
                  :key="item[ElOption_4.key]"
                  :label="item[ElOption_4.label]"
                  :value="item[ElOption_4.value]"
                  :disabled="item[ElOption_4.disabled]"
                ></ElOption>
              </customElSelect>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="mt-J3 flex-auto">
        <customElSelect
          class="mr-J1"
          prefix_in="能源类型"
          v-if="false"
          v-model="ElSelect_3.value"
          v-bind="ElSelect_3"
          v-on="ElSelect_3.event"
        >
          <ElOption
            v-for="item in ElOption_3.options_in"
            :key="item[ElOption_3.key]"
            :label="item[ElOption_3.label]"
            :value="item[ElOption_3.value]"
            :disabled="item[ElOption_3.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="tree-style"
          style="height: calc(100% - 32px)"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          :searchText_in.sync="CetTree_1.searchText_in"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetTree>
      </div>
    </div>
  </CustomElDrawer>
</template>

<script>
import customApi from "@/api/custom";
import CustomElDrawer from "eem-base/components/customElComponent/customElDrawer.vue";

export default {
  name: "advancedQuery",
  components: {
    CustomElDrawer
  },
  props: {
    openTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    energyOptions: Array,
    rootNodeId_in: Object,
    rootNodes_in: Array
  },
  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },
  data() {
    return {
      openDrawer: false,
      ElSelect_1: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 分摊方式
      ElSelect_2: {
        value: "",
        style: {
          width: "100%"
        },
        event: {}
      },
      // 能源类型
      // 3组件
      ElSelect_3: {
        value: "",
        style: {
          width: "100%"
        },
        event: {
          // change: this.ElSelect_3_change_out
        }
      },
      ElOption_2: {
        options_in: [
          {
            id: 0,
            text: $T("全部")
          },
          {
            id: 1,
            text: $T("固定比例")
          },
          {
            id: 2,
            text: $T("动态分摊")
          }
        ],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        label: "text",
        value: "id",
        disabled: "disabled"
      },
      // 选择时段
      CetDatePicker_time: {
        val: null,
        config: {
          valueFormat: "timestamp",
          type: "daterange",
          clearable: false,
          unlinkPanels: true,
          rangeSeparator: $T("至")
        }
      },
      // 1树组件
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      // 当前选中节点
      currentNode: {},
      ElSelect_4: {
        value: 0,
        style: {},
        event: {
          change: this.ElSelect_4_change_out
        }
      },
      ElOption_4: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    openTrigger_in() {
      let updateEnergyOptions = this._.cloneDeep(this.energyOptions);
      updateEnergyOptions.shift();
      this.ElOption_1.options_in = updateEnergyOptions;
      // this.ElOption_3.options_in = updateEnergyOptions;
      this.ElSelect_1.value =
        updateEnergyOptions && updateEnergyOptions.length > 0
          ? updateEnergyOptions[0].id
          : "";
      this.ElSelect_4.value = this.rootNodeId_in;
      this.getTreeData();
      this.init();
      this.openDrawer = true;
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },
  methods: {
    // 初始化数据
    init() {
      this.ElSelect_2.value = 0;
      this.CetDatePicker_time.val = [
        this.$moment().startOf("year").valueOf(),
        this.$moment().endOf("year").valueOf()
      ];
    },
    // 获取节点树
    async getTreeData() {
      const rootNode = this.rootNodes_in.find(
        item => item.id === this.ElSelect_4.value
      );
      const queryData = {
        nodeTreeGroupId: 2,
        energyType: this.ElSelect_1.value,
        rootNode: {
          id: rootNode?.id,
          modelLabel: rootNode?.modelLabel
        }
      };
      const res = await customApi.getNodeTree(queryData);
      if (res.code !== 0) {
        return;
      }
      this.CetTree_1.inputData_in = res.data;
      this.CetTree_1.selectNode = res?.data?.[0] ?? null;
    },
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
    },
    ElSelect_1_change_out() {
      this.getTreeData();
    },
    ElSelect_4_change_out() {
      this.getTreeData();
    },
    CetButton_confirm_statusTrigger_out() {
      if (!this.CetDatePicker_time.val) {
        this.$message.warning("请选择筛选时段");
      }
      if (!(this.currentNode && this.currentNode.id)) {
        this.$message.warning("请选择查询节点");
      }
      let params = {
        objectEnergyType: this.ElSelect_1.value,
        energyShareMethod: this.ElSelect_2.value,
        startTime: this.CetDatePicker_time.val[0],
        endTime: this.CetDatePicker_time.val[1],
        baseVo: {
          id: this.currentNode.id,
          modelLabel: this.currentNode.modelLabel
        },
        rootNodeId: this.ElSelect_4.value
      };
      this.$emit("query_update", params);
      this.openDrawer = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}
.label {
  font-weight: 400;
  font-size: 14px;
  @include font_color(T3);
}
.tree-style {
  :deep(.el-tree) {
    width: 100%;
  }
}
</style>
