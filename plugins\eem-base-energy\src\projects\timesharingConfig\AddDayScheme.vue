<template>
  <div>
    <AddTime
      :visibleTrigger_in="AddTime.visibleTrigger_in"
      :closeTrigger_in="AddTime.closeTrigger_in"
      :queryId_in="AddTime.queryId_in"
      :inputData_in="AddTime.inputData_in"
      @confirm_out="AddTime_confirm_out"
    />
    <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event" class="CetDialog">
      <div class=" ">
        <div class="clearfix mb-J3">
          <div class="mb-J1">
            {{ $T("方案名称") }}
            <span style="color: red">*</span>
          </div>
          <ElInput
            v-model="ElInput_1.value"
            v-bind="ElInput_1"
            v-on="ElInput_1.event"
          ></ElInput>
        </div>
        <div class="clearfix mb-J3">
          <CetButton
            class="fr ml-J1"
            v-bind="CetButton_1"
            v-on="CetButton_1.event"
          ></CetButton>
          <CetButton
            class="fr ml-J1"
            v-bind="CetButton_2"
            v-on="CetButton_2.event"
          ></CetButton>
        </div>
        <CetTable
          style="height: 200px"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <ElTableColumn v-bind="ElTableColumn_name">
            <template slot-scope="scope">
              <span :style="ElTableColumn_name.styleFormat(scope)">
                {{ scope.row[ElTableColumn_name.prop] }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn v-bind="ElTableColumn_timeInterval"></ElTableColumn>
        </CetTable>
      </div>
      <div>
        <div class="text-H3 font-bold mb-J1 ml-J3">{{ $T("时段") }}</div>
        <AddDaySchemeTimePicker
          :inputData_in="AddDaySchemeTimePicker.inputData_in"
          :visibleColor="AddDaySchemeTimePicker.visibleColor"
          v-show="AddDaySchemeTimePicker.visibleTrigger_in"
          @confirm_out="AddDaySchemeTimePicker_confirm_out"
          ref="AddDaySchemeTimePicker"
        />
      </div>
      <span slot="footer">
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>
<script>
import AddTime from "./AddTime.vue";
import AddDaySchemeTimePicker from "./AddDaySchemeTimePicker.vue";
import common from "eem-base/utils/common";
import omegaTheme from "@omega/theme";
export default {
  name: "AddDayScheme",
  components: {
    AddTime,
    AddDaySchemeTimePicker
  },
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    // 完整的方案信息
    timeShareSchemeDetail: {
      type: Object
    }
  },

  computed: {
    currentTheme() {
      return omegaTheme.theme;
    }
  },

  data() {
    return {
      currentTabItem: null,
      AddDaySchemeTimePicker: {
        inputData_in: [],
        visibleColor: "",
        visibleTrigger_in: true
      },
      CetDialog_1: {
        title: $T("新增时段方案"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {
          width: "200px"
        },
        event: {}
      },
      AddTime: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      },
      CetButton_1: {
        visible_in: true,
        disable_in: false,
        title: $T("新增时段"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_1_statusTrigger_out
        }
      },
      CetButton_2: {
        visible_in: true,
        disable_in: true,
        title: $T("删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_2_statusTrigger_out
        }
      },
      deleteRows: [],
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort:null, // { prop: "code"  order: "descending" },
        event: {
          record_out: this.CetTable_1_record_out
        }
      },
      ElTableColumn_name: {
        prop: "name", // 支持path a[0].b
        label: $T("时段名称"), //列名
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "200", //绝对宽度
        styleFormat: function (val) {
          if (!val) {
            return;
          }
          // 颜色值与AddDaySchemeTimePicker中展示的演示一一对应，通过visibleColor字段传入
          const colorObj = {
            light: [
              "display: block;background: rgba(249, 94, 90, 1);",
              "display: block;background: rgba(255, 157, 9, 1);",
              "display: block;background: rgba(76, 166, 255, 1);",
              "display: block;background: rgba(106, 222, 154, 1);",
              "display: block;background: rgb(1, 82, 217);"
            ],
            dark: [
              "display: block;background: rgba(255, 93, 93, 1);",
              "display: block;background: rgba(255, 143, 39, 1);",
              "display: block;background: rgba(12, 130, 248, 1);",
              "display: block;background: rgba(69, 235, 136, 1);",
              "display: block;background: rgb(1, 82, 217);"
            ]
          };
          const color = colorObj[this.currentTheme] ?? colorObj.light;
          return color[val.$index];
        }
      },
      ElTableColumn_timeInterval: {
        prop: "timeInterval", // 支持path a[0].b
        label: $T("时段"), //列名
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        formatter: function (val) {
          if (val.timeInterval) {
            return val.timeInterval;
          } else {
            return "--";
          }
        } //格式化列的函数, 在commmon.js中定义, 直接配置函数 例: common.formatDateColumn
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      var vm = this;
      vm.CetDialog_1.closeTrigger_in = val;
    },
    inputData_in(val) {
      console.log(val);
      if (val.name) {
        this.deleteRows = [];
        this.CetDialog_1.title = $T("编辑时段方案");
        this.ElInput_1.value = val.name;
        this.CetTable_1.data = this._.cloneDeep(val.timeInterval);
        // this.AddDaySchemeTimePicker.visibleTrigger_in = true;
      } else {
        this.CetDialog_1.title = $T("新增时段方案");
        this.ElInput_1.value = "";
        this.CetTable_1.data = [];
        // this.AddDaySchemeTimePicker.visibleTrigger_in = false;
      }
    },
    "CetTable_1.data": {
      handler: function (val) {
        if (val && val.length > 0) {
          var inputData_in = [];
          val.forEach((item, index) => {
            inputData_in.push({
              data: item.timeInterval ? item.timeInterval.split(";") : [],
              color: "color" + index
            });
          });
          this.$nextTick(() => {
            this.AddDaySchemeTimePicker.inputData_in =
              this._.cloneDeep(inputData_in);
          });

          if (val.length >= 5) {
            this.CetButton_1.disable_in = true;
          } else {
            this.CetButton_1.disable_in = false;
          }
        } else {
          this.AddDaySchemeTimePicker.inputData_in = [];
          this.CetButton_1.disable_in = false;
        }
      },
      deep: true
    }
  },

  methods: {
    // 新增时段名称的输出
    AddTime_confirm_out(val) {
      console.log("新增的时段名称为" + val);
      if (this.CetTable_1.data.filter(item => item.name == val).length > 0) {
        this.$message({
          message: $T("已存在相同时段名称"),
          type: "warning"
        });
      } else {
        // 如果已有时段，需要将时段的日方案同步过来
        let copyData;
        if (this.CetTable_1.data && this.CetTable_1.data.length) {
          let maxLengthIndex = 0;
          this.CetTable_1.data.forEach(item => {
            if (!maxLengthIndex && item.dayset_model) {
              copyData = item;
              maxLengthIndex = item.dayset_model.length;
            } else if (
              item.dayset_model &&
              item.dayset_model.length > maxLengthIndex
            ) {
              copyData = item;
              maxLengthIndex = item.dayset_model.length;
            }
          });
        }

        let newData = {
          name: val,
          timeInterval: "",
          id: -(this.CetTable_1.data.length + 5)
        };
        if (copyData) {
          newData.daysetObj = this._.cloneDeep(copyData.daysetObj);
          newData.dayset_model = this._.cloneDeep(copyData.dayset_model);
        }
        this.CetTable_1.data.push(newData);
      }
    },
    // 时段选择的输出
    AddDaySchemeTimePicker_confirm_out(val) {
      var data = this._.cloneDeep(this.CetTable_1.data);
      val.forEach((item, index) => {
        if (data[index]) {
          data[index].timeInterval = item.join(";");
        }
      });
      this.CetTable_1.data = data;
    },
    CetButton_1_statusTrigger_out(val) {
      this.AddTime.visibleTrigger_in = this._.cloneDeep(val);
    },
    CetButton_2_statusTrigger_out() {
      var vm = this;
      vm.$confirm($T("确定要删除所选项吗？"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action == "confirm") {
            //找到当前行，并从表格数据中删除，将删除的数据打上delete_flag标签，存入deleteRows数组中
            let delIndex = -1;
            vm.CetTable_1.data.forEach((item, index) => {
              if (item.id === vm.currentTabItem.id) {
                vm.currentTabItem.delete_flag = true;
                delIndex = index;
              }
            });
            vm.deleteRows.push(vm.currentTabItem);
            vm.CetTable_1.data.splice(delIndex, 1);
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: $T("取消删除")
            });
          }
        }
      });
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      if (!this.ElInput_1.value) {
        this.$message({
          message: $T("请填写日时段方案名称"),
          type: "warning"
        });
        return;
      } else if (this.ElInput_1.value.length > 20) {
        this.$message({
          message: $T("方案名称长度在 1 到 20 个字符"),
          type: "warning"
        });
        return false;
      } else if (
        !common.check_pattern_name.pattern.test(this.ElInput_1.value)
      ) {
        this.$message({
          message: $T("方案名称不能输入特殊字符"),
          type: "warning"
        });
        return false;
      }

      // 判断日期是否选择完整
      if (
        this.$refs.AddDaySchemeTimePicker.dateArr.filter(item => !item.color)
          .length > 0
      ) {
        this.$confirm($T("存在未配置费率时段，是否保存？"), $T("提示"), {
          confirmButtonText: $T("确认"),
          cancelButtonText: $T("取消"),
          type: "warning",
          closeOnClickModal: false,
          showClose: false,
          beforeClose: (action, instance, done) => {
            if (action == "confirm") {
              this.confirm();
            }
            instance.confirmButtonLoading = false;
            done();
          },
          callback: action => {
            if (action != "confirm") {
              this.$message({
                type: "info",
                message: $T("已取消！")
              });
            }
          }
        });
      } else {
        this.confirm();
      }
    },
    confirm() {
      var obj = {
        name: this.ElInput_1.value,
        timeInterval: this.CetTable_1.data,
        old: false
      };
      if (this.inputData_in.name) {
        // 编辑输出
        obj.old = true;
      } else {
        // 新增输出
        obj.old = false;
      }
      this.$emit("confirm_out", obj);
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
    },
    CetTable_1_record_out(val) {
      if (val.id != -1) {
        this.CetButton_2.disable_in = false;
        this.CetTable_1.data.forEach((item, index) => {
          if (item.id == val.id && item.name == val.name) {
            this.AddDaySchemeTimePicker.visibleColor = "color" + index;
          }
        });
        this.currentTabItem = val;
      } else {
        this.CetButton_2.disable_in = true;
        this.currentTabItem = null;
      }
    }
  }
};
</script>
