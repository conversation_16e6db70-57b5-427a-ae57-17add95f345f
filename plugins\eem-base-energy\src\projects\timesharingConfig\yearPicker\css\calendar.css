.fullYearPicker{
	margin-top: 10px;
	-webkit-user-select: none; 
	
}

.fullYearPicker .month-container{
	min-width: 180px;
	float: left;
	margin-top: 15px;
}
.fullYearPicker .date_clear{
	clear: both;
}

.fullYearPicker div.year {
	text-align: center;
	font-size: 22px;
	color: #aaa;
	margin-left: 5px;
	margin-right: 5px;
	border-radius: 5px;
	border:1px #ddd solid;
}

.fullYearPicker div.year a {
	color: #aaa;
}


.fullYearPicker div.year a.next {
	margin-right: 0;
}

.fullYearPicker .year table{
	width: 100%;
	height: 45px;
} 
.fullYearPicker .year table tr th{
	text-align: center;
	font-size: 22px;
}
.fullYearPicker .year table tr th:hover{
	background: #eee;
}

.fullYearPicker .year table .year-operation-btn{
	width: 40px;
}

.fullYearPicker .year table .year_btn{
	cursor:Default;
}

.fullYearPicker .year table .left_sencond_year,.fullYearPicker .year table .right_sencond_year{
	color: #ddd;
}
.fullYearPicker .year table .left_first_year,.fullYearPicker .year table .right_first_year{
	color: #aaa;
}

@media only screen and (max-width: 640px){
	.fullYearPicker .year table .left_first_year,.fullYearPicker .year table .right_first_year{
		display: none;
	}
}

@media only screen and (max-width: 1024px){
	.fullYearPicker .year table .left_sencond_year,.fullYearPicker .year table .right_sencond_year{
		display: none;
	}
}

.fullYearPicker .picker table {
	border: 1px dotted;
	margin-left: auto;
	margin-right: auto;
	font-size: 12px;
}

.fullYearPicker table th.head {
	text-align: center;
	line-height: 12px;
	cursor: default;
	background: #fff;
}

.fullYearPicker table td {
	background: #fff;
	text-align: center;
	line-height: 13px;
	cursor: pointer;
	padding: 5px;
	margin: 1px;
}

.fullYearPicker .picker table .able_day:hover{
	background: #ccc;
	border-radius: 2px;
}


.fullYearPicker table th {
	color: #80808f;
	font-size: 14px;
	padding: 5px;
}

.fullYearPicker table td.weekend,.fullYearPicker table th.weekend {

	
}

.fullYearPicker table td.disabled {
	
	
}


/*����ʱ�����ʽ */
.fullYearPicker table td.color1 {
	background: rgb(0, 151, 86);
}
/*�ǹ����յ���ʽ */
.fullYearPicker table td.color2 {
	background: rgb(253, 202, 1);
}
/*�Ƕ����յ���ʽ */
.fullYearPicker table td.color3 {
	background: rgb(0, 152, 254);
}
/*�����յ���ʽ */
.fullYearPicker table td.color4 {
	background: rgb(253, 51, 254);
}
/*�����յ���ʽ */
.fullYearPicker table td.color5 {
	background: rgb(1, 82, 217);
}

/*ѡ����ʽ */
.fullYearPicker table td.selected {
	background: #ccc;
}

.fullYearPicker br {
	clear: both
}

.month-width-1{
	width: 100%;
}
.month-width-2{
	width: 50%;
}
.month-width-3{
	width: 33.33%;
}
.month-width-4{
	width: 25%;
}
.month-width-6{
	width: 16.66%;
}




/*��Ϣ������*/
.hd_info_modal label{
	float: left;
	width: 50px;
	text-align: left;
	height: 40px;
	line-height: 40px;
	color: #808080;
	font-weight: 100;
}

.hd_info_modal .am-modal-hd{
	border-bottom: 2px solid #31aaef; 
}

body .hd_info_modal .radius{
	border-radius:5px;
}
.hd_info_modal .hd-type-select{
	float:left;
}

.hd_info_modal .am-selected{
	width: 180px;
}

.hd-work-date{
	float: left;
}
.hd_info_modal .am-form-group{
	float: left;
	width: 180px;
}
.hd_info_modal .hd-work-block{
	width: 235px;
	float: left;
	margin-bottom: 10px;
}
.hd_info_modal .hd-work-btns{
	float: right;
}
.hd_info_modal form{
	margin-top: 50px;
}
.hd_info_modal .stone{
	clear: both;
}