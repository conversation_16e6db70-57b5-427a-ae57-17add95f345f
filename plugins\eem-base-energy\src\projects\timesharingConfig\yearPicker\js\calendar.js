import moment from "moment";
let callbackObj = {};
var aim_div = ""; //目标div 放置日历的位置
var m = 0; //使用标识,之前页面记录的几列
var n = 0; //使用标识,根据页面宽度决定日历分为几列
var language = "cn"; //语言选择
var month_arry;
var week_arry;
var month_cn = new Array(
  $T("一月"),
  $T("二月"),
  $T("三月"),
  $T("四月"),
  $T("五月"),
  $T("六月"),
  $T("七月"),
  $T("八月"),
  $T("九月"),
  $T("十月"),
  $T("十一月"),
  $T("十二月")
); //月
var month_en = new Array(
  "Jan.",
  "Feb.",
  "Mar.",
  "Apr.",
  "May",
  "Jun.",
  "Jul.",
  "Aug.",
  "Sep.",
  "Oct.",
  "Nov.",
  "Dec."
); //月
var week_cn = new Array("日", "一", "二", "三", "四", "五", "六"); //星期
var week_en = new Array("Su", "<PERSON>", "<PERSON>", "We", "Th", "Fr", "Sa"); //星期
month_arry = month_cn;
week_arry = week_cn;
var drap_select;
var m_color = "color1";
var actionYear = new Date().getFullYear();
var disable = true;
var renderYear;

function loading_calendar(id, lan) {
  aim_div = "#" + id;
  language = lan;
  if (lan == "cn") {
    month_arry = month_cn;
    week_arry = week_cn;
  } else {
    month_arry = month_en;
    week_arry = week_en;
  }
  // //开始
  $(aim_div).fullYearPicker({
    disabledDay: "",
    value: [
      /* '2016-6-25', '2016-8-26'  */
    ],
    cellClick: function (dateStr, isDisabled) {
      /* console.log("单击日期:"+dateStr); */
      /* arguments[0] */
    }
  });
}
(function () {
  window.onload = function () {
    window.onresize = change;
    change();
  };

  function change() {
    var obj = $(aim_div);
    var m_obj = $(aim_div + ".fullYearPicker .month-container");
    var width = obj.width();
    var class_width = "month-width-";
    n = parseInt(width / 200);
    if (n == 5) n = 4;
    if (n > 6) n = 6;

    if (n != m) {
      m_obj.removeClass(class_width + m);
      m_obj.addClass(class_width + n);
      m = n;
    }
  }

  function changeHandle() {
    m = 0;
    change();
  }

  //设置年份菜单
  function setYearMenu(year) {
    $(".year .left_first_year").text(year - 1 + "");
    $(".year .left_sencond_year").text(year - 2 + "");
    $(".year .cen_year").text(year);
    $(".year .right_first_year").text(year + 1 + "");
    $(".year .right_sencond_year").text(year + 2 + "");
  }

  //设置开始日期和结束日期
  function setDateInfo(start_date, end_date) {
    console.log("设置开始日期和结束日期", start_date, end_date);
    // $("#hd-start-date").datepicker('setValue', start_date);
    // $("#hd-end-date").datepicker('setValue',end_date);
  }

  function tdClass(i, disabledDay, sameMonth, values, dateStr) {
    var cls = i == 0 || i == 6 ? "weekend" : "";
    if (disabledDay && disabledDay.indexOf(i) != -1)
      cls += (cls ? " " : "") + "disabled";
    if (!sameMonth) {
      cls += (cls ? " " : "") + "empty";
    } else {
      cls += (cls ? " " : "") + "able_day";
    }
    if (
      sameMonth &&
      values &&
      cls.indexOf("disabled") == -1 &&
      values.indexOf("," + dateStr + ",") != -1
    )
      cls += (cls ? " " : "") + "selected";
    return cls == "" ? "" : ' class="' + cls + '"';
  }
  function renderMonth(year, month, clear, disabledDay, values) {
    var d = new Date(year, month - 1, 1),
      s =
        "<div class='month-container'>" +
        '<table cellpadding="3" cellspacing="1" border="0"' +
        (clear ? ' class="right"' : "") +
        ">" +
        '<tr><th colspan="7" class="head"  index="' +
        month +
        '">' /* + year + '年'  */ +
        month_arry[month - 1] +
        "</th></tr>" +
        '<tr><th class="weekend">' +
        week_arry[0] +
        "</th><th>" +
        week_arry[1] +
        "</th><th>" +
        week_arry[2] +
        "</th><th>" +
        week_arry[3] +
        "</th><th>" +
        week_arry[4] +
        "</th><th>" +
        week_arry[5] +
        '</th><th class="weekend">' +
        week_arry[6] +
        "</th></tr>";
    var dMonth = month - 1;
    var firstDay = d.getDay(),
      hit = false;
    s += "<tr>";
    for (var i = 0; i < 7; i++)
      if (firstDay == i || hit) {
        s +=
          "<td" +
          tdClass(
            i,
            disabledDay,
            true,
            values,
            year + "-" + month + "-" + d.getDate()
          ) +
          ">" +
          d.getDate() +
          "</td>";
        d.setDate(d.getDate() + 1);
        hit = true;
      } else s += "<td" + tdClass(i, disabledDay, false) + ">&nbsp;</td>";
    s += "</tr>";
    for (var i = 0; i < 5; i++) {
      s += "<tr>";
      for (var j = 0; j < 7; j++) {
        s +=
          "<td" +
          tdClass(
            j,
            disabledDay,
            d.getMonth() == dMonth,
            values,
            year + "-" + month + "-" + d.getDate()
          ) +
          ">" +
          (d.getMonth() == dMonth ? d.getDate() : "&nbsp;") +
          "</td>";
        d.setDate(d.getDate() + 1);
      }
      s += "</tr>";
    }
    return s + "</table></div>" + (clear ? "<br>" : "");
  }
  function getDateStr(td) {
    //console.log("----"+td.parentNode.parentNode.rows[0].cells[0].getAttribute('index')+"-"+ td.innerHTML);
    return (
      td.parentNode.parentNode.rows[0].cells[0].getAttribute("index") +
      "-" +
      td.innerHTML
    );
  }

  renderYear = function renderYear(year, el, disabledDay, value) {
    actionYear = year;
    console.log("选择年份变化为", year);

    el.find("td").unbind();
    var s = "",
      values = "," + value.join(",") + ",";
    for (var i = 1; i <= 12; i++)
      s += renderMonth(year, i, false, disabledDay, values);
    s += "<div class='date_clear'></div>";
    el.find("div.picker")
      .html(s)
      .find("td")
      .click /*单击日期单元格*/
      /* 
						function() {
							if (!/disabled|empty/g.test(this.className))
								$(this).toggleClass('selected');
							if (this.className.indexOf('empty') == -1
									&& typeof el.data('config').cellClick == 'function')
								el
										.data('config')
										.cellClick(
												getDateStr(this),
												this.className
														.indexOf('disabled') != -1);
						} */
      ();
    changeHandle();
    day_drap_listen();
  };
  //监听日期拖在
  function day_drap_listen() {
    var is_drap = 0;
    var start_date = "";
    var end_date = "";
    $(aim_div + ".fullYearPicker .picker table td").mousedown(function (event) {
      if (disable) {
        return;
      }
      /*判断是左键才触发  */
      if (event.button == 0 && $(this).html() != "&nbsp;") {
        is_drap = 1;
        start_date = getDateStr($(this)[0]);
        /*console.log("开始值:"+start_date); */
      }
    });
    $(aim_div + ".fullYearPicker .picker table td").mouseup(function (event) {
      if (disable) {
        return;
      }
      /*判断是左键才触发  */
      if (event.button == 0 && $(this).html() != "&nbsp;") {
        is_drap = 0;
        end_date = getDateStr($(this)[0]);
        /* console.log("结束值:"+end_date); */
        if (checkDate(start_date, end_date)) {
          open_modal(start_date, end_date, true);
        } else {
          open_modal(end_date, start_date, true);
        }
      }
    });
    $(aim_div + ".fullYearPicker .picker table td").mouseover(function () {
      if (disable) {
        return;
      }
      var day = $(this).html();
      if (is_drap == 1 && day != "&nbsp;") {
        var min_date = getDateStr($(this)[0]);
        drap_select(start_date, min_date, "selected");
        /*console.log("拖拽中:"+min_date); */
      }
    });
  }
  /*根据日期判断大小 开始值小于结束值返回true  */
  function checkDate(start, end) {
    var rs = false;
    var start_month = parseInt(start.split("-")[0]);
    var start_day = parseInt(start.split("-")[1]);
    var end_month = parseInt(end.split("-")[0]);
    var end_day = parseInt(end.split("-")[1]);
    if (start_month == end_month) {
      if (start_day < end_day) {
        rs = true;
      }
    } else if (start_month < end_month) {
      rs = true;
    }
    return rs;
  }
  /*窗口添加按钮*/
  $("#calendar_confirm_btn").click(function () {
    var start_date = $("#hd-start-date").val();
    start_date = start_date.split("-")[1] + "-" + start_date.split("-")[2];
    var end_date = $("#hd-end-date").val();
    end_date = end_date.split("-")[1] + "-" + end_date.split("-")[2];
    // var m_type=$("#hd-type-option").val();
    /*drap_select(start_date,end_date,"workday");*/
    // m_type 颜色
    drap_select(start_date, end_date, m_color);
    close_modal();
  });

  /*按月加载样式*/
  function select_month(month, start, end, new_class, mouseup, deleteAll) {
    month = month - 1;
    $(
      aim_div + ".fullYearPicker .picker .month-container:eq(" + month + ") td"
    ).each(function () {
      var num = $(this).text();
      if (num >= start && num <= end) {
        /* $(this).addClass("selected"); */
        // $(this).addClass(new_class);
        if (mouseup) {
          var arr = $(this)[0]
            .className.split(" ")
            .filter(item => item.indexOf("color") !== -1);
          arr.forEach(item => {
            if (item != new_class) {
              $(this).removeClass(item);
            } else if (deleteAll) {
              $(this).removeClass(item);
            }
          });
        }
        if (!deleteAll) {
          $(this).toggleClass(new_class);
        } else {
          var arr = $(this)[0]
            .className.split(" ")
            .filter(item => item.indexOf("color") !== -1);
          arr.forEach(item => {
            $(this).removeClass(item);
          });
        }
      }
    });
  }
  /*拖拽选着  */
  drap_select = function drap_select(
    start,
    end,
    new_class,
    mouseup,
    deleteAll
  ) {
    var max = 60; //当天数要选择到最后一天取一个大于所以月份的值
    /* console.log("选择:"+start+","+end); */
    //清除选中单元格的样式
    $(".month-container .selected").removeClass("selected");
    var start_month = parseInt(start.split("-")[0]);
    var start_day = parseInt(start.split("-")[1]);
    var end_month = parseInt(end.split("-")[0]);
    var end_day = parseInt(end.split("-")[1]);
    /* console.log("start_month:"+start_month);
		console.log("start_day:"+start_day);
		console.log("end_month:"+end_month);
		console.log("end_day:"+end_day); */
    if (start_month == end_month) {
      if (start_day < end_day) {
        select_month(
          start_month,
          start_day,
          end_day,
          new_class,
          mouseup,
          deleteAll
        );
      } else {
        select_month(
          start_month,
          end_day,
          start_day,
          new_class,
          mouseup,
          deleteAll
        );
      }
    } else if (start_month < end_month) {
      select_month(start_month, start_day, max, new_class, mouseup, deleteAll);
      for (var i = start_month + 1; i < end_month; i++) {
        select_month(i, 1, max, new_class, mouseup, deleteAll);
      }
      select_month(end_month, 1, end_day, new_class, mouseup, deleteAll);
    } else if (start_month > end_month) {
      select_month(start_month, 1, start_day, new_class, mouseup, deleteAll);
      for (var i = end_month + 1; i < start_month; i++) {
        select_month(i, 1, max, new_class, mouseup, deleteAll);
      }
      select_month(end_month, end_day, max, new_class, mouseup, deleteAll);
    }
    // 向外触发日期选择完成事件
    callbackObj.selectHandle.callback();
  };
  function open_modal(start_date, end_date, mouseup) {
    var year = $("#cen_year").text();
    // start_date=year+"-"+start_date;
    // end_date=year+"-"+end_date;
    // if(start_date!=null){
    // 	setDateInfo(start_date,end_date);
    // }
    console.log("打开弹框");
    // $("#calendar-modal-1").modal();
    // $(".month-container .selected").removeClass("selected");
    if (disable) {
      return;
    }
    Object.keys(callbackObj).forEach(item => {
      if (callbackObj[item].type === "click") {
        callbackObj[item].callback();
      }
    });
    const allArr = Object.keys(getAll());
    // 颜色序号
    const colorNum = m_color.split("color")[1] % 12;
    let repeat = false;
    allArr.forEach(item => {
      if (item !== m_color && item.split("color")[1] % 12 === colorNum) {
        repeat = true;
      }
    });
    if (repeat) {
      callbackObj.message.callback($T("已存在相同颜色的选择"));
      return;
    }
    drap_select(start_date, end_date, m_color, mouseup);
  }

  //@config：配置，具体配置项目看下面
  //@param：为方法时需要传递的参数
  $.fn.fullYearPicker = function (config, param) {
    if (
      config === "setDisabledDay" ||
      config === "setYear" ||
      config === "getSelected" ||
      config === "acceptChange"
    ) {
      //方法
      var me = $(this);
      if (config == "setYear") {
        //重置年份
        me.data("config").year = param; //更新缓存数据年份
        me.find("div.year a:first").trigger("click", true);
      } else if (config == "getSelected") {
        //获取当前当前年份选中的日期集合 注意不更新默认传入的值，要更新值请调用acceptChange方法
        return me
          .find("td.selected")
          .map(function () {
            return getDateStr(this);
          })
          .get();
      } else if (config == "acceptChange") {
        //更新日历值，这样才会保存选中的值，更换其他年份后，再切换到当前年份才会自动选中上一次选中的值
        me.data("config").value = me.fullYearPicker("getSelected");
      } else {
        me.find("td.disabled").removeClass("disabled");
        me.data("config").disabledDay = param; //更新不可点击星期
        if (param) {
          me.find("table tr:gt(1)")
            .find("td")
            .each(function () {
              if (param.indexOf(this.cellIndex) != -1)
                this.className =
                  (this.className || "").replace("selected", "") +
                  (this.className ? " " : "") +
                  "disabled";
            });
        }
      }
      return this;
    }
    //@year:显示的年份
    //@disabledDay:不允许选择的星期列，注意星期日是0，其他一样
    //@cellClick:单元格点击事件/可缺省。事件有2个参数，第一个@dateStr：日期字符串，格式“年-月-日”，第二个@isDisabled，此单元格是否允许点击
    //@value:选中的值，注意为数组字符串，格式如['2016-6-25','2016-8-26'.......]
    config = $.extend(
      {
        year: new Date().getFullYear(),
        disabledDay: "",
        value: []
      },
      config
    );
    return this.addClass("fullYearPicker").each(function () {
      var me = $(this),
        year = config.year || new Date().getFullYear(),
        newConifg = {
          cellClick: config.cellClick,
          disabledDay: config.disabledDay,
          year: year,
          value: config.value
        };
      me.data("config", newConifg);

      // me.append('<div class="year">'
      // 						+'<table>'
      // 						+'<th class="year-operation-btn"><i class="el-icon-arrow-left"></i></th>'
      // 						+'<th class="left_sencond_year year_btn">'+ ''+'</th>'
      // 						+'<th class="left_first_year year_btn">'+ ''+'</th>'
      // 						+'<th id="cen_year" class="cen_year year_btn">'+ year+'</th>'
      // 						+'<th class="right_first_year year_btn">'+ ''+'</th>'
      // 						+'<th class="right_sencond_year year_btn">'+ ''+'</th>'
      // 						+'<th class="year-operation-btn"><i class="next el-icon-arrow-right"></i></th>'
      // 						+'</table>'
      // 						+'<div class="stone"></div></div><div class="picker"></div>')
      me.append('<div class="picker"></div>')
        .find(".year-operation-btn")
        .click(function (e, setYear) {
          if (setYear) year = me.data("config").year;
          else
            $(this).children("a").attr("class") == "el-icon-arrow-left"
              ? year--
              : year++;
          setYearMenu(year);
          renderYear(
            year,
            $(this).closest(aim_div + "div.fullYearPicker"),
            newConifg.disabledDay,
            newConifg.value
          );
          document.getElementById("cen_year").firstChild.data = year;
          return false;
        });
      setYearMenu(year);
      //年份选择
      $(".year .year_btn").click(function () {
        var class_name = $(this).attr("class");
        if (class_name.indexOf("cen_year") < 0) {
          var year = parseInt($(this).text());
          setYearMenu(year);
          renderYear(year, me, newConifg.disabledDay, newConifg.value);
        }
      });
      renderYear(year, me, newConifg.disabledDay, newConifg.value);
    });
  };
})();

function close_modal() {
  $("#calendar-modal-1").modal("close");
}
function getAll() {
  var obj = {};
  var nodeArr = $(aim_div + ".fullYearPicker .picker table td");
  Array.from(nodeArr).forEach(item => {
    if ($(item).attr("class").indexOf("color") != -1) {
      var colorName = $(item)
        .attr("class")
        .split(" ")
        .filter(ite => ite.indexOf("color") != -1)[0];
      if (!obj[colorName]) {
        obj[colorName] = [
          actionYear +
            "-" +
            item.parentNode.parentNode.rows[0].cells[0].getAttribute("index") +
            "-" +
            item.innerHTML
        ];
      } else {
        obj[colorName].push(
          actionYear +
            "-" +
            item.parentNode.parentNode.rows[0].cells[0].getAttribute("index") +
            "-" +
            item.innerHTML
        );
      }
    }
  });
  // Object.keys(obj).forEach(item => {
  // 	var arr = [],str1="",startStr,endStr;
  // 	obj[item].forEach((i,inde) => {
  // 		if(!str1){
  // 			str1 = actionYear + '-' + i;
  // 			endStr = i;
  // 			startStr = i;
  // 		}else{
  // 			if(moment(str1).add(1,'day').format("MM-DD") != moment(actionYear + '-' + i).format("MM-DD")){
  // 				arr.push(startStr+'~'+endStr);
  // 				startStr = i;
  // 				str1 = actionYear + '-' + i;
  // 			}else{
  // 				str1 = actionYear + '-' + i;
  // 				endStr = i;
  // 			}
  // 		}
  // 		if(inde == (obj[item].length-1)){
  // 			arr.push(startStr+'~'+endStr);
  // 		}
  // 	})
  // 	obj[item] = arr;
  // })
  return obj;
}

export default {
  drap_select: drap_select, //赋值方法
  loading_calendar: loading_calendar, // 初始化
  // 设置激活的颜色，需要在css中先写好颜色值
  actionColor: function (val) {
    m_color = val;
  },
  // 获取勾选的所有节点
  getAll: getAll,
  //禁止操作
  disable_in: function (val) {
    if (val) {
      disable = true;
    } else {
      disable = false;
    }
  },
  // 设置当前年
  setActionYear(val) {
    actionYear = val;
    renderYear(val, $(aim_div + ".fullYearPicker"), "", []);
  },
  setAim_div(val) {
    aim_div = "#" + val;
  },
  $on(id_in, callback) {
    callbackObj[id_in] = callback;
  }
};
