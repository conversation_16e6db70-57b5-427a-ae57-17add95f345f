<template>
  <ElDrawer
    :title="$T('损耗率')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <div class="h-full flex flex-col">
      <div class="chartTitle">
        {{ $T("{0}级损耗率排名", inputData_in ? inputData_in.index + 1 : "") }}
        TOP5
      </div>
      <CetChart v-bind="CetChart_1" class="mt-J3 chart"></CetChart>
    </div>
  </ElDrawer>
</template>
<script>
const COLORSOne = ["#3B42D9", "#5160FF", "#7A8AFF", "#A3B1FF", "#CCD5FF"];
export default {
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    }
  },
  computed: {},
  data() {
    return {
      openDrawer: false,
      CetChart_1: {
        //组件输入项
        inputData_in: null,
        options: {
          grid: {
            top: 0,
            left: 1,
            right: 32,
            bottom: 1,
            containLabel: true
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            formatter: "{b}：{c}%",
            confine: true
          },
          xAxis: {
            name: "%"
          },
          yAxis: {
            type: "category",
            data: [],
            axisTick: {
              show: false
            },
            axisLabel: {
              width: 150,
              overflow: "truncate"
            }
          },
          series: [
            {
              type: "bar",
              data: [],
              itemStyle: {
                color: function (param) {
                  return COLORSOne[4 - param.dataIndex];
                }
              },
              barWidth: "12px"
            }
          ]
        }
      }
    };
  },
  watch: {
    visibleTrigger_in() {
      this.init();
      this.openDrawer = true;
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },

  methods: {
    init() {
      this.getData();
    },
    getData() {
      let rankings = this._.get(this.inputData_in, "rankings", []) || [];
      if (rankings.length > 5) {
        rankings = rankings.slice(0, 5);
      }
      let yAxisData = [],
        serieData = [];
      rankings.forEach(item => {
        yAxisData.push(item.name);
        if (item.lossRate || item.lossRate === 0) {
          serieData.push(Number((item.lossRate * 100).toFixed2(2)));
        } else {
          serieData.push(null);
        }
      });
      yAxisData.reverse();
      serieData.reverse();
      this.CetChart_1.options.yAxis.data = yAxisData;
      this.CetChart_1.options.series[0].data = serieData;
    }
  },
  created: function () {}
};
</script>
<style lang="scss" scoped>
.chart {
  height: 360px;
  box-sizing: border-box;
  .chartTitle {
    @include font_size(H2);
    font-weight: bold;
  }
}
</style>
