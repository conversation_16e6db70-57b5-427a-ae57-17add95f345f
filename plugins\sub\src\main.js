import omegaApp from "@omega/app";
import "./omega/index";
import "./resources/index.css";
import "./resources/index.scss";
import "./icons/index.js";

import Vue from "vue";

import lodash from "lodash";
import moment from "moment";
import ElementUI from "element-ui";

import routerOption from "./router";
import storeOption from "./store";

import customApi from "./api/custom";
import CetCommon from "cet-common";
import Cet<PERSON><PERSON>, { registerTheme } from "cet-chart";
import { CustomElSelect, CustomElDatePicker } from "eem-base/components";
Vue.component("CustomElDatePicker", CustomElDatePicker);
Vue.component("customElSelect", CustomElSelect);
Vue.component("CustomElSelect", CustomElSelect);

import { toFixed2 } from "eem-base/utils/number.js";
Number.prototype.toFixed2 = toFixed2;

Vue.use(CetCommon, {
  api: customApi,
  CetDialog: {
    isDraggable: false
  },
  CetTable: {
    isColumsHeaderDoLayout: true
  }
});
Vue.use(CetChart, {
  // themeConf: {
  // backgroundColor: "#000"
  // }
});

Vue.config.productionTip = false;

// 工具库
Vue.prototype._ = lodash;
Vue.prototype.$moment = moment;

// ElementUI
Vue.use(ElementUI, { size: "small" });

//引入趋势曲线组件
import OmegaTend from "@omega/trend";
Vue.use(OmegaTend);

ElementUI.Pagination.props.background = {
  default: true,
  type: Boolean
};

// 启动
omegaApp.createApp({
  el: "#app",
  config: {},
  routerOption,
  storeOption
});
