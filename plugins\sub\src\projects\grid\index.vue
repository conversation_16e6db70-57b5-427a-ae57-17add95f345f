<template>
  <cet-aside class="pecreport">
    <template #aside>
      <CetTree
        :selectNode.sync="CetTree_1.selectNode"
        :checkedNodes.sync="CetTree_1.checkedNodes"
        :searchText_in.sync="CetTree_1.searchText_in"
        v-bind="CetTree_1"
        v-on="CetTree_1.event"
      ></CetTree>
    </template>
    <template #container>
      <CustomElSelect
        v-model="ElSelect_treeType.value"
        v-bind="ElSelect_treeType"
        v-on="ElSelect_treeType.event"
        :prefix_in="$T('节点树类型')"
      >
        <ElOption
          v-for="item in ElOption_treeType.options_in"
          :key="item[ElOption_treeType.key]"
          :label="item[ElOption_treeType.label]"
          :value="item[ElOption_treeType.value]"
          :disabled="item[ElOption_treeType.disabled]"
        ></ElOption>
      </CustomElSelect>

      <CustomElDatePicker
        style="width: 200px"
        :prefix_in="$T('选择年份')"
        v-model="value3"
        type="year"
        :clearable="false"
        :placeholder="$T('选择年份')"
      />

      <CustomSteps :active="stepActive" :steps="steps" />
      <div @click="openUploadDialog">打开导入弹框</div>
      <UploadDialog
        v-bind="uploadDialog"
        v-on="uploadDialog.event"
      ></UploadDialog>
    </template>
  </cet-aside>
</template>

<script>
import {
  CustomElSelect,
  CustomElDatePicker,
  CustomSteps,
  UploadDialog
} from "eem-base/components";
export default {
  name: "gridDemo",
  components: {
    CustomElSelect,
    CustomElDatePicker,
    CustomSteps,
    UploadDialog
  },

  data() {
    return {
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        expandWhenChecked: true, //组件节点可选择状态下，可设置选择节点是否展开expandWhenChecked,默认为true
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {}
      },
      ElSelect_treeType: {
        value: "",
        style: {},
        event: {}
      },
      ElOption_treeType: {
        options_in: [
          {
            id: 1,
            name: "节点树类型1"
          },
          {
            id: 2,
            name: "节点树类型2"
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      value3: "",

      stepActive: 1,
      steps: [
        {
          title: "选择节点及能源类型",
          description: "进行能源及节点类型配置"
        },
        {
          title: "层级信息配置",
          description: "配置管网、管理层级信息"
        },
        {
          title: "层级信息配置",
          description: "配置管网、管理层级信息"
        }
      ],
      uploadDialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        extensionNameList_in: [".xlsx", ".xls"],
        hideDownload: false,
        dialogTitle: "上传专家知识",
        event: {}
      }
    };
  },
  methods: {
    openUploadDialog() {
      debugger;
      this.uploadDialog.openTrigger_in = new Date().getTime();
    }
  }
};
</script>
