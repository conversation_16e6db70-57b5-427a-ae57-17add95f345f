# 虚拟电厂用户管理API接口调整说明

## 概述

根据业务需求，对虚拟电厂用户管理相关的API接口进行了调整，统一使用 `getUserPage` 接口，不再使用 `getUserByUsername` 和 `getUserById` 接口。

## 变更详情

### 1. 删除的接口

#### 1.1 getUserByUsername
- **接口路径**: `GET /vpp/api/v1/resource-manager/user/username/{username}`
- **功能**: 根据用户名查询用户
- **删除原因**: 功能可通过 `getUserPage` 实现

#### 1.2 getUserById  
- **接口路径**: `GET /vpp/api/v1/resource-manager/user/{id}`
- **功能**: 根据用户ID查询用户
- **删除原因**: 功能可通过 `getUserPage` 实现

### 2. 保留并增强的接口

#### 2.1 getUserPage
- **接口路径**: `POST /vpp/api/v1/resource-manager/user/page`
- **功能**: 分页查询用户列表
- **增强内容**: 新增支持通过 `userId` 参数查询特定用户

**新增参数**:
```javascript
{
  // ... 其他现有参数
  userId: number // 可选，用于查询特定用户
}
```

## 代码变更

### 1. API文件变更

**文件**: `plugins/virtual-power-plant/src/api/user-management/index.js`

**变更内容**:
- 删除 `getUserByUsername` 函数
- 删除 `getUserById` 函数  
- 更新 `getUserPage` 函数文档，新增 `userId` 参数说明

### 2. 组件文件变更

**文件**: `plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/UserManagement.vue`

**变更内容**:
- 移除 `getUserById` 的导入
- 修改 `onDetail` 方法，使用 `getUserPage` 替代 `getUserById`
- 修改 `onEdit` 方法，使用 `getUserPage` 替代 `getUserById`

**变更前**:
```javascript
// 获取用户详情
const response = await getUserById(row.id);
```

**变更后**:
```javascript
// 使用getUserPage通过用户ID获取用户详细信息
const response = await getUserPage({
  index: 0,
  limit: 1,
  userId: row.id
});

if (response.code === 0 && response.data.records.length > 0) {
  // 使用 records[0] 获取用户数据
  const userData = response.data.records[0];
  // ...
}
```

## 使用示例

### 1. 查询特定用户

```javascript
import { getUserPage } from "@/api/user-management";

// 通过用户ID查询特定用户
const response = await getUserPage({
  index: 0,
  limit: 1,
  userId: 123
});

if (response.code === 0 && response.data.records.length > 0) {
  const user = response.data.records[0];
  console.log("用户信息:", user);
}
```

### 2. 分页查询用户列表

```javascript
// 查询用户列表（保持原有功能不变）
const response = await getUserPage({
  index: 0,
  limit: 10,
  vppId: 1,
  userName: "张三"
});

if (response.code === 0) {
  const users = response.data.records;
  const total = response.data.total;
  console.log("用户列表:", users);
  console.log("总数:", total);
}
```

## 影响范围

### 1. 前端组件
- ✅ `UserManagement.vue` - 已更新
- ✅ `vppResource.js` (Vuex store) - 无影响，已使用 `getUserPage`

### 2. API接口
- ✅ `user-management/index.js` - 已更新

### 3. 其他模块
- ✅ 无其他模块使用被删除的接口

## 兼容性说明

1. **向后兼容**: `getUserPage` 接口保持原有功能不变，新增的 `userId` 参数为可选参数
2. **数据格式**: 返回数据格式保持一致，通过 `response.data.records[0]` 获取单个用户数据
3. **错误处理**: 需要检查 `records.length > 0` 确保查询到数据

## 注意事项

1. 使用 `getUserPage` 查询单个用户时，需要检查返回的 `records` 数组长度
2. 单个用户查询建议设置 `limit: 1` 以提高性能
3. 后端需要确保 `getUserPage` 接口支持 `userId` 参数的查询功能

## 测试建议

1. 测试用户详情查看功能
2. 测试用户编辑功能
3. 测试用户列表分页功能
4. 测试通过用户ID查询功能
5. 验证错误处理逻辑

---

**变更日期**: 2024年
**变更人员**: AI Assistant
**审核状态**: 待审核
