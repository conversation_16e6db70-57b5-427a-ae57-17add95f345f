# 资源创建API参数调整说明

## 调整概述

根据后端提供的创建资源API入参结构，对前端的数据转换逻辑进行了调整，确保前端表单数据能够正确映射到后端API参数。

## 后端API参数结构

### 必填字段
- `electricityUserNumbers` (String) - 电户号
- `type` (Integer) - 资源类型（1-发电设备，2-储能设备，3-负荷设备）
- `userId` (Long) - 所属用户ID
- `vppId` (Long) - 所属电厂ID
- `resourceName` (String) - 资源名称

### 可选字段
- `resourceType` (Integer) - 资源类型(发电/储电/用电)
- `registeredCapacity` (BigDecimal) - 报装容量
- `responseMode` (Integer) - 响应方式(自动/手动)
- `platformDirectControl` (Boolean) - 平台直控
- `maximumOperablePower` (Double) - 最大可运行功率
- `minmumOperablePower` (Double) - 最小可运行功率
- `maximumUpscalingRate` (Double) - 最大上调速率
- `maximumDownwardRate` (Double) - 最大下调速率
- `longitude` (Double) - 经度
- `latitude` (Double) - 纬度
- `contactPerson` (String) - 联系人
- `phoneNumber` (String) - 联系电话
- `district` (Integer) - 区域
- `address` (String) - 地址

## 前端调整内容

### 1. 数据转换方法更新 (`ResourceManagement.vue`)

```javascript
transformFormDataToApi(formData) {
  // 资源类型映射（1-发电设备，2-储能设备，3-负荷设备）
  const resourceTypeMap = {
    generation: 1, // 发电设备
    storage: 2, // 储能设备
    consumption: 3 // 负荷设备
  };

  // 响应方式映射
  const responseModeMap = {
    auto: 1, // 自动
    manual: 2 // 手动
  };

  // 区域映射（district字段）
  const districtMap = {
    guangzhou: 1,
    shenzhen: 2,
    foshan: 3,
    dongguan: 4,
    zhongshan: 5,
    zhuhai: 6
  };

  return {
    // 必填字段
    electricityUserNumbers: formData.electricityUserNumbers, // 电户号
    type: resourceTypeMap[formData.resourceType] || 1, // 资源类型
    userId: this.userId, // 所属用户ID（必填）
    vppId: this.vppId, // 所属电厂ID（必填）
    resourceName: formData.resourceName, // 资源名称

    // 可选字段
    resourceType: resourceTypeMap[formData.resourceType] || null,
    registeredCapacity: formData.registeredCapacity ? parseFloat(formData.registeredCapacity) : null,
    responseMode: responseModeMap[formData.responseMode] || null,
    platformDirectControl: formData.platformDirectControl || null,
    maximumOperablePower: formData.maxRunningPower ? parseFloat(formData.maxRunningPower) : null,
    minmumOperablePower: formData.minRunningPower ? parseFloat(formData.minRunningPower) : null,
    maximumUpscalingRate: formData.maxUpRate ? parseFloat(formData.maxUpRate) : null,
    maximumDownwardRate: formData.maxDownRate ? parseFloat(formData.maxDownRate) : null,
    longitude: formData.longitude ? parseFloat(formData.longitude) : null,
    latitude: formData.latitude ? parseFloat(formData.latitude) : null,
    contactPerson: formData.contactPerson || null,
    phoneNumber: formData.contactPhone || null,
    district: districtMap[formData.region] || null,
    address: formData.address || null
  };
}
```

### 2. API文档更新 (`resource-management/index.js`)

更新了createResource函数的参数文档，使其与后端API保持一致：

```javascript
/**
 * 创建资源
 * @param {Object} data - 资源数据对象
 * @param {string} data.electricityUserNumbers - 电户号（必填）
 * @param {number} data.type - 资源类型（1-发电设备，2-储能设备，3-负荷设备）（必填）
 * @param {number} data.userId - 所属用户ID（必填）
 * @param {number} data.vppId - 所属电厂ID（必填）
 * @param {string} data.resourceName - 资源名称（必填）
 * // ... 其他可选参数
 */
```

## 字段映射关系

### 前端表单字段 → 后端API参数

| 前端字段 | 后端参数 | 类型转换 | 说明 |
|---------|---------|---------|------|
| `electricityUserNumbers` | `electricityUserNumbers` | String | 电户号 |
| `resourceName` | `resourceName` | String | 资源名称 |
| `resourceType` | `type` | String→Integer | 资源类型映射 |
| `resourceType` | `resourceType` | String→Integer | 资源类型(重复字段) |
| `registeredCapacity` | `registeredCapacity` | String→Float | 报装容量 |
| `responseMode` | `responseMode` | String→Integer | 响应方式映射 |
| `platformDirectControl` | `platformDirectControl` | Boolean | 平台直控 |
| `maxRunningPower` | `maximumOperablePower` | String→Float | 最大可运行功率 |
| `minRunningPower` | `minmumOperablePower` | String→Float | 最小可运行功率 |
| `maxUpRate` | `maximumUpscalingRate` | String→Float | 最大上调速率 |
| `maxDownRate` | `maximumDownwardRate` | String→Float | 最大下调速率 |
| `longitude` | `longitude` | String→Float | 经度 |
| `latitude` | `latitude` | String→Float | 纬度 |
| `contactPerson` | `contactPerson` | String | 联系人 |
| `contactPhone` | `phoneNumber` | String | 联系电话 |
| `region` | `district` | String→Integer | 区域映射 |
| `address` | `address` | String | 地址 |

## 注意事项

1. **移除了不需要的字段**：后端API中没有`status`字段，因此移除了对`resourceStatus`的处理

2. **数据类型转换**：所有数值类型字段都进行了适当的类型转换（parseFloat）

3. **空值处理**：所有可选字段都使用`|| null`进行空值处理

4. **字段名称映射**：部分字段名称在前后端不一致，已进行正确映射

5. **枚举值映射**：资源类型和响应方式使用了枚举值映射

## 验证建议

建议在实际使用时验证以下内容：

1. 必填字段的验证是否正确
2. 数值类型转换是否成功
3. 枚举值映射是否准确
4. 区域编码映射是否完整
