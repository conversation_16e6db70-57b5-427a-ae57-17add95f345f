# 虚拟电厂树接口字段更新说明

## 更新概述

树接口返回的数据结构新增了以下字段：

1. **roomId** - 站点对应房间id
2. **siteType** - 站点类型  
3. **resourceType** - 资源类型
4. **province** - 电厂所属省份

## 字段详细说明

### roomId (站点对应房间id)
- **类型**: `Long`
- **适用节点**: 站点节点 (site)
- **说明**: 站点对应的房间ID，用于关联站点与房间的关系

### siteType (站点类型)
- **类型**: `Integer`
- **适用节点**: 站点节点 (site)
- **说明**: 站点类型编码，参考站点类型枚举

### resourceType (资源类型)
- **类型**: `Integer`
- **适用节点**: 资源节点 (resource)
- **说明**: 资源类型编码
  - 1 = 发电设备
  - 2 = 储能设备
  - 3 = 负荷设备

### province (电厂所属省份)
- **类型**: `Integer`
- **适用节点**: VPP节点 (vpp)
- **说明**: 电厂所属省份编码

## 代码更新内容

### 1. API接口文档更新 (`vpp-tree-cache/index.js`)

```javascript
/**
 * TreeNodeDTO结构：
 * {
 *   id: number,              // 节点ID
 *   parentId: number,        // 父节点ID
 *   name: string,            // 节点名称
 *   label: string,           // 节点类型标签(vpp/user/resource/site/device)
 *   isLeaf: boolean,         // 是否为叶子节点
 *   childrenCount: number,   // 子节点数量
 *   nodePath: string,        // 节点路径
 *   roomId: number,          // 站点对应房间id（站点节点特有）
 *   siteType: number,        // 站点类型（站点节点特有）
 *   resourceType: number,    // 资源类型（资源节点特有）
 *   province: number,        // 电厂所属省份（VPP节点特有）
 *   children: Array<TreeNodeDTO> // 子节点列表
 * }
 */
```

### 2. 数据转换函数更新

#### transformTreeNode函数
```javascript
// 新增字段支持
roomId: apiNode.roomId || null, // 站点对应房间id
siteType: apiNode.siteType || null, // 站点类型
resourceType: apiNode.resourceType || null, // 资源类型
province: apiNode.province || null // 电厂所属省份
```

#### flattenTreeData函数
```javascript
// 新增字段
roomId: node.roomId,
siteType: node.siteType,
resourceType: node.resourceType,
province: node.province
```

### 3. 组件props传递更新 (`index.vue`)

```vue
<component
  :is="currentComponent"
  :key="componentKey"
  :node="selectedNode"
  :vppId="currentVppId"
  :userId="currentUserId"
  :resourceId="currentResourceId"
  :siteId="currentSiteId"
  :roomId="currentRoomId"
  :siteType="currentSiteType"
  :resourceType="currentResourceType"
  :province="currentProvince"
/>
```

### 4. 计算属性更新

```javascript
// 当前电厂所属省份
currentProvince() {
  if (!this.selectedNode) return null;
  
  // 如果当前选中的是VPP节点，直接返回province
  if (this.selectedNode.type === "vpp") {
    return this.selectedNode.province || null;
  }
  
  // 从树形结构中向上查找VPP节点的province
  const vppNode = this.findParentNode("vpp");
  return vppNode ? vppNode.province : null;
}
```

## 使用场景

### roomId
- 在站点管理中用于关联房间信息
- 创建设备时可能需要房间上下文

### siteType
- 在站点管理中用于区分不同类型的站点
- 影响站点的功能和配置选项

### resourceType
- 在资源管理中用于区分发电、储能、负荷等不同类型
- 影响资源的管理界面和功能

### province
- 在VPP管理中用于地域划分
- 可能影响政策配置和数据统计

## 兼容性说明

- 所有新增字段都是可选的，使用 `|| null` 进行默认值处理
- 现有功能不受影响，向后兼容
- 组件可以根据需要选择性使用这些新字段

## 注意事项

1. 不同类型的节点包含不同的特有字段：
   - VPP节点：province
   - 资源节点：resourceType
   - 站点节点：roomId, siteType

2. 在使用这些字段时，建议先检查节点类型，确保字段的有效性

3. 这些字段的具体枚举值和含义需要参考后端API文档
