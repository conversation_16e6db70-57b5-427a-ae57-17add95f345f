# Tech Context

## 核心技术栈

### 前端框架层
- **Vue.js 2.7.8** - 渐进式 JavaScript 框架
- **Vue Router 3.x** - 官方路由管理器
- **Vuex 3.x** - 状态管理模式，支持模块化
- **Element UI 2.15.6** - 基础UI组件库

### 样式解决方案
- **Tailwind CSS 3.x** - 原子化CSS框架，支持主题定制
- **SCSS** - CSS预处理器，支持变量、嵌套、混入
- **CSS变量** - 动态主题切换，支持明暗模式

### 微前端架构
- **@omega/app** - 主应用容器，负责插件注册和生命周期管理
- **@altair/lord** - 微前端框架，支持独立开发部署
- **@altair/blade** - 插件通信机制
- **omegaApp.plugin.register** - 插件注册机制

### 业务组件体系
- **cet-chart** - 图表组件库，支持主题定制
- **cet-common** - 通用业务组件（CetTable、CetButton、CetTree等）
- **@omega/widget** - 小部件组件
- **@omega/trend** - 趋势曲线组件
- **@omega/dashboard** - 仪表盘组件

### 工具库
- **Lodash 4.x** - 实用工具库
- **Moment.js 2.x** - 时间处理库
- **Crypto-js 4.x** - 加密解密工具
- **jQuery 3.x** - DOM操作库

## 开发环境

### 构建工具链
- **Vue CLI 5.x** - 前端构建工具
- **Webpack 5.x** - 模块打包器
- **Babel 7.x** - JavaScript编译器
- **PostCSS** - CSS后处理器

### 代码质量
- **ESLint 8.x** - JavaScript代码检查
- **Prettier 2.x** - 代码格式化
- **eslint-config-prettier** - ESLint与Prettier集成

### 包管理
- **pnpm** - 高效的包管理器
- **pnpm workspace** - 多包管理，支持monorepo架构

### 开发服务器
- **@omega/cli-devserver** - 开发服务器，支持热重载
- **webpack-dev-server** - 开发调试服务器
- **代理配置** - 支持多服务代理，便于本地开发

## 国际化与主题

### 国际化方案
- **@omega/i18n** - 国际化框架
- **auto-i18n-cli** - 自动国际化工具
- **支持语言**：中文、英文
- **使用方式**：`$T("文本内容")`

### 主题系统
- **@omega/theme** - 主题管理
- **CSS变量** - 动态主题切换
- **明暗模式** - 支持`class="dark"`切换
- **主题定制** - 支持品牌色、间距、字体等定制

## 依赖架构

### 基础能力包
- **eem-base** - 基础组件和工具库
  - `eem-base/utils/fetch` - 统一HTTP请求
  - `eem-base/components` - 基础业务组件
  - `eem-base/utils/common` - 通用工具函数

### Omega生态系统
- **@omega/app** - 应用容器
- **@omega/auth** - 认证授权
- **@omega/layout** - 布局组件
- **@omega/http** - HTTP请求封装
- **@omega/icon** - 图标组件

### 第三方依赖
- **compression-webpack-plugin** - 打包压缩
- **svg-sprite-loader** - SVG图标处理
- **nprogress** - 进度条组件

## 虚拟电厂模块架构

### API接口层
```javascript
// 位置：src/api/vpp-resource.js
// 功能：统一管理所有RESTful API接口
- 虚拟电厂CRUD：createVpp, getVpp, updateVpp, deleteVpp, listVpp
- 用户管理CRUD：createUser, getUser, updateUser, deleteUser, listUser
- 资源管理CRUD：createResource, getResource, updateResource, deleteResource, listResource
- 站点管理CRUD：createSite, getSite, updateSite, deleteSite, listSite
- 设备管理CRUD：createDevice, getDevice, updateDevice, deleteDevice, listDevice
- 数据点管理：associateDatapoints, getDeviceDatapoints, updateDatapoint, deleteDatapoint
```

### 状态管理层
```javascript
// 位置：src/store/modules/vppResource.js
// 功能：Vuex模块，统一管理业务数据状态
- state：vpps, users, resources, sites, devices, loading
- mutations：SET_VPPS, SET_USERS, SET_RESOURCES, SET_SITES, SET_DEVICES, SET_LOADING
- actions：fetchVpps, fetchUsers, fetchResources, fetchSites, fetchDevices
```

### 组件架构层
```
src/projects/vpp-resource-manager/
├── resource-config/
│   ├── index.vue                 # 主页面，动态组件切换
│   ├── mock.js                   # Mock数据
│   └── components/
│       ├── VppTree.vue           # 虚拟电厂树形组件
│       ├── VppManagement.vue     # 虚拟电厂管理
│       ├── UserManagement.vue    # 用户管理
│       ├── ResourceManagement.vue # 资源管理
│       ├── SiteManagement.vue    # 站点管理
│       └── DeviceManagement.vue  # 设备管理
└── aggregation/
    ├── index.vue                 # 聚合管理主页面
    └── components/
        ├── AddUnitDialog.vue     # 添加单元对话框
        └── DetailDrawer.vue      # 详情抽屉
```

## 技术约束与规范

### 插件开发规范
- 插件需遵循统一目录结构：`src/omega/index.js`注册
- 必须支持多语言、主题切换、权限管理
- 组件命名采用大驼峰，文件名采用小驼峰
- 使用`@`别名指向`src`目录

### 代码规范
- 使用ESLint + Prettier统一代码风格
- 组件props必须定义类型和默认值
- 所有文本使用`$T()`函数包装
- API请求统一使用`eem-base/utils/fetch`

### 性能优化
- 路由懒加载，减少初始包大小
- 图片资源压缩，支持WebP格式
- 代码分割，按需加载第三方库
- 缓存策略，减少重复请求

### 兼容性要求
- 支持现代浏览器（Chrome 60+, Firefox 55+, Safari 12+）
- 移动端适配，支持响应式设计
- 支持键盘导航，满足无障碍访问
