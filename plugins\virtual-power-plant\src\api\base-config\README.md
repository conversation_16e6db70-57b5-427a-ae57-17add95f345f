# 基础配置管理 API

本模块提供虚拟电厂基础配置管理相关的 API 接口，包括资源-站点类型关联关系、站点-设备类型关联关系、地理数据、图片管理和设备查询等功能。

## 功能特性

- ✅ 自动缓存机制（30 分钟过期）
- ✅ 强制刷新缓存选项
- ✅ 缓存状态查询
- ✅ 详细的类型注释
- ✅ 符合项目规范
- ✅ 文件上传下载支持
- ✅ 地理数据管理
- ✅ 设备查询功能

## API 接口

### getResourceSiteRelations(forceRefresh)

获取资源-站点类型关联关系列表。

**参数：**

- `forceRefresh` (boolean, 可选): 是否强制刷新缓存，默认 false

**返回值：**

```javascript
{
  code: 0,
  data: [
    {
      id: number,              // 主键ID
      resource_type_id: number, // 资源类型ID
      site_type_id: number     // 站点类型ID
    }
  ],
  msg: string,
  total: number
}
```

### getSiteDeviceRelations(forceRefresh)

获取站点-设备类型关联关系列表。

**参数：**

- `forceRefresh` (boolean, 可选): 是否强制刷新缓存，默认 false

**返回值：**

```javascript
{
  code: 0,
  data: [
    {
      id: number,              // 主键ID
      device_type_id: number,  // 设备类型ID
      site_iype_id: number     // 站点类型ID（注意：API文档中此字段名有拼写错误）
    }
  ],
  msg: string,
  total: number
}
```

### getGeographicalData(forceRefresh)

获取所有地理数据（省份、城市、区县）。

**参数：**

- `forceRefresh` (boolean, 可选): 是否强制刷新缓存，默认 false

**返回值：**

```javascript
{
  code: 0,
  data: {
    provinces: Array, // 省份列表
    cities: Array,    // 城市列表
    districts: Array  // 区县列表
  },
  msg: string,
  total: number
}
```

### uploadImage(file, fileName)

上传图片文件到服务器。

**参数：**

- `file` (File, 必填): 图片文件对象
- `fileName` (string, 可选): 文件名称

**返回值：**

```javascript
{
  code: 0,
  data: {
    imagePath: string, // 图片路径，可直接存储到设备picture字段
    fileName: string,  // 文件名
    fileSize: number   // 文件大小
  },
  msg: string
}
```

### downloadImage(imagePath)

下载图片文件。

**参数：**

- `imagePath` (string, 必填): 图片路径

**返回值：**

- 返回图片文件流（blob 格式）

### getMonitorDevicesByRoom(queryData)

根据房间查询管网设备。

**参数：**

- `queryData` (Object, 必填): 查询参数
  - `roomId` (string): 房间 ID
  - `roomType` (string): 房间类型
  - `deviceTypes` (Array<string>): 管网设备类型列表

**返回值：**

```javascript
{
  code: 0,
  data: [
    {
      id: string,           // 设备ID
      deviceName: string,   // 设备名称
      deviceType: string,   // 设备类型
      roomId: string,       // 房间ID
      roomType: string      // 房间类型
    }
  ],
  msg: string,
  total: number
}
```

### clearBaseConfigCache(type)

清除基础配置缓存。

**参数：**

- `type` (string, 可选): 缓存类型，可选值：'resourceSiteRelations', 'siteDeviceRelations', 'geographicalData', 'all'，默认 'all'

### getCacheStatus()

获取缓存状态信息。

**返回值：**

```javascript
{
  resourceSiteRelations: {
    cached: boolean,    // 是否已缓存
    timestamp: number,  // 缓存时间戳
    expired: boolean    // 是否已过期
  },
  siteDeviceRelations: {
    cached: boolean,
    timestamp: number,
    expired: boolean
  },
  geographicalData: {
    cached: boolean,
    timestamp: number,
    expired: boolean
  }
}
```

## 使用示例

```javascript
import {
  getResourceSiteRelations,
  getSiteDeviceRelations,
  getGeographicalData,
  uploadImage,
  downloadImage,
  getMonitorDevicesByRoom,
  clearBaseConfigCache,
  getCacheStatus
} from "@/api/base-config";

// 获取资源-站点类型关联关系（使用缓存）
const resourceSiteRelations = await getResourceSiteRelations();
if (resourceSiteRelations.code === 0) {
  console.log("资源-站点关联关系:", resourceSiteRelations.data);
}

// 强制刷新缓存并获取数据
const freshData = await getResourceSiteRelations(true);

// 获取站点-设备类型关联关系
const siteDeviceRelations = await getSiteDeviceRelations();
if (siteDeviceRelations.code === 0) {
  console.log("站点-设备关联关系:", siteDeviceRelations.data);
}

// 清除所有缓存
clearBaseConfigCache();

// 清除特定缓存
clearBaseConfigCache("resourceSiteRelations");

// 查看缓存状态
const cacheStatus = getCacheStatus();
console.log("缓存状态:", cacheStatus);

// 获取地理数据
const geoData = await getGeographicalData();
if (geoData.code === 0) {
  console.log("地理数据:", geoData.data);
}

// 上传图片
const fileInput = document.getElementById("fileInput");
const file = fileInput.files[0];
if (file) {
  const uploadResult = await uploadImage(file, "device-image.jpg");
  if (uploadResult.code === 0) {
    console.log("图片上传成功:", uploadResult.data.imagePath);
  }
}

// 下载图片
const imagePath = "uploads/device-image.jpg";
const imageBlob = await downloadImage(imagePath);
// 创建下载链接
const url = URL.createObjectURL(imageBlob);
const a = document.createElement("a");
a.href = url;
a.download = "device-image.jpg";
a.click();

// 查询管网设备
const deviceQuery = {
  roomId: "room_001",
  roomType: "equipment_room",
  deviceTypes: ["sensor", "controller"]
};
const devices = await getMonitorDevicesByRoom(deviceQuery);
if (devices.code === 0) {
  console.log("管网设备:", devices.data);
}
```

## 注意事项

1. 缓存有效期为 30 分钟，过期后会自动重新请求
2. 可以通过 `forceRefresh` 参数强制刷新缓存
3. 接口返回的数据结构遵循项目统一规范
4. 建议在应用启动时预加载这些基础配置数据
5. API 文档中 `site_iype_id` 字段名存在拼写错误，实际使用时请注意
6. 图片上传支持 jpg、png、gif 等格式，最大文件大小 10MB
7. 图片下载返回的是 blob 格式，需要创建 URL 对象进行处理
8. 地理数据包含省份、城市、区县的完整层级结构
9. 管网设备查询支持多种设备类型同时查询
