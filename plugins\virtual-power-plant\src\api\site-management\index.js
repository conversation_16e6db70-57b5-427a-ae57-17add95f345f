import { httping } from "@omega/http";

/**
 * 创建站点
 * @param {Object} data - 站点数据对象
 * @param {string} data.siteId - 站点编号
 * @param {string} data.siteName - 站点名称
 * @param {number} data.siteType - 站点类型
 * @param {number} data.vppId - 所属VPP ID（必填）
 * @param {number} data.resourceId - 所属资源ID（必填）
 * @param {number} [data.roomId] - 对应房间ID
 * @param {string} [data.siteAddress] - 站点地址
 * @param {string} [data.contactPerson] - 联系人
 * @param {string} [data.phoneNumber] - 联系电话
 * @param {number} [data.longitude] - 经度
 * @param {number} [data.latitude] - 纬度
 * @param {number} [data.deviceCount] - 设备数量
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppSiteBO, // 站点详细信息(实际返回驼峰命名)
 *   msg: string,
 *   total: number
 * }
 */
export function createSite(data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/site`,
    method: "POST",
    data
  });
}

/**
 * 检查站点编号是否存在
 * @param {string} code - 站点编号
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示编号已存在，false表示不存在
 *   msg: string,
 *   total: number
 * }
 */
export function checkSiteCodeExists(code) {
  return httping({
    url: `/vpp/api/v1/resource-manager/site/check-code/${code}`,
    method: "GET"
  });
}

/**
 * 统计站点数量
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: number, // 站点总数
 *   msg: string,
 *   total: number
 * }
 */
export function countSites() {
  return httping({
    url: `/vpp/api/v1/resource-manager/site/count`,
    method: "GET"
  });
}

/**
 * 批量删除站点
 * @param {Array<number>} ids - 站点ID数组
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示删除成功
 *   msg: string,
 *   total: number
 * }
 */
export function deleteSitesByIds(ids) {
  return httping({
    url: `/vpp/api/v1/resource-manager/site/delete-by-ids`,
    method: "DELETE",
    data: ids
  });
}

/**
 * 分页查询站点列表
 * @param {Object} data - 查询参数
 * @param {number} data.pageNum - 页码（从1开始）
 * @param {number} data.pageSize - 每页数量
 * @param {string} [data.siteName] - 站点名称（可选）
 * @param {string} [data.siteId] - 站点编号（可选）
 * @param {number} [data.siteType] - 站点类型（可选）
 * @param {string} [data.siteStatus] - 站点状态（可选）
 * @param {number} [data.vppId] - 所属VPP ID（可选）
 * @param {number} [data.resourceId] - 所属资源ID（可选）
 * @param {string} [data.location] - 地理位置（可选）
 * @param {string} [data.orderBy] - 排序字段（可选）
 * @param {string} [data.orderDirection] - 排序方向（可选）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     records: Array<VppSiteBO>, // 站点列表
 *     total: number, // 总记录数
 *     pageNum: number, // 当前页码
 *     pageSize: number, // 每页数量
 *     pages: number, // 总页数
 *     hasNext: boolean, // 是否有下一页
 *     hasPrevious: boolean // 是否有上一页
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function getSitePage(data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/site/page`,
    method: "POST",
    data
  });
}

/**
 * 根据ID查询站点
 * @param {number} id - 站点ID
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppSiteBO, // 站点详细信息
 *   msg: string,
 *   total: number
 * }
 */
export function getSiteById(id) {
  return httping({
    url: `/vpp/api/v1/resource-manager/site/${id}`,
    method: "GET"
  });
}

/**
 * 更新站点
 * @param {number} id - 站点ID
 * @param {Object} data - 站点数据对象（同创建站点的参数）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppSiteBO, // 更新后的站点信息
 *   msg: string,
 *   total: number
 * }
 */
export function updateSite(id, data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/site/${id}`,
    method: "PUT",
    data
  });
}
