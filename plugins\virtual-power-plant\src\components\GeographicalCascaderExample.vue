<template>
  <div class="geographical-cascader-example">
    <div class="example-header">
      <h2>{{ $T("地理级联选择器示例") }}</h2>
      <p>{{ $T("基于getGeographicalData接口数据的省市区级联选择器") }}</p>
    </div>

    <div class="example-content">
      <!-- 基础用法 -->
      <div class="example-section">
        <h3>{{ $T("基础用法") }}</h3>
        <div class="form-row">
          <label>{{ $T("选择省市区") }}：</label>
          <GeographicalCascader
            v-model="basicValue"
            :placeholder="$T('请选择省份/城市/区县')"
            @change="handleBasicChange"
            @node-change="handleBasicNodeChange"
          />
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("选中值") }}：</strong>
            {{ JSON.stringify(basicValue) }}
          </p>
          <p>
            <strong>{{ $T("选中节点") }}：</strong>
            {{ JSON.stringify(basicNodes) }}
          </p>
        </div>
      </div>

      <!-- 只选择省份 -->
      <div class="example-section">
        <h3>{{ $T("只选择省份") }}</h3>
        <div class="form-row">
          <label>{{ $T("选择省份") }}：</label>
          <GeographicalCascader
            v-model="provinceValue"
            :level="1"
            :placeholder="$T('请选择省份')"
            @change="handleProvinceChange"
          />
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("选中值") }}：</strong>
            {{ JSON.stringify(provinceValue) }}
          </p>
        </div>
      </div>

      <!-- 只选择到城市 -->
      <div class="example-section">
        <h3>{{ $T("只选择到城市") }}</h3>
        <div class="form-row">
          <label>{{ $T("选择省市") }}：</label>
          <GeographicalCascader
            v-model="cityValue"
            :level="2"
            :placeholder="$T('请选择省份/城市')"
            @change="handleCityChange"
          />
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("选中值") }}：</strong>
            {{ JSON.stringify(cityValue) }}
          </p>
        </div>
      </div>

      <!-- 显示编码 -->
      <div class="example-section">
        <h3>{{ $T("显示编码") }}</h3>
        <div class="form-row">
          <label>{{ $T("选择省市区（显示编码）") }}：</label>
          <GeographicalCascader
            v-model="codeValue"
            :show-code="true"
            :placeholder="$T('请选择省份/城市/区县')"
            @change="handleCodeChange"
          />
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("选中值") }}：</strong>
            {{ JSON.stringify(codeValue) }}
          </p>
        </div>
      </div>

      <!-- 懒加载模式 -->
      <div class="example-section">
        <h3>{{ $T("懒加载模式") }}</h3>
        <div class="form-row">
          <label>{{ $T("懒加载选择") }}：</label>
          <GeographicalCascader
            v-model="lazyValue"
            :lazy="true"
            :placeholder="$T('请选择省份/城市/区县（懒加载）')"
            @change="handleLazyChange"
          />
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("选中值") }}：</strong>
            {{ JSON.stringify(lazyValue) }}
          </p>
        </div>
      </div>

      <!-- 多选模式 -->
      <div class="example-section">
        <h3>{{ $T("多选模式") }}</h3>
        <div class="form-row">
          <label>{{ $T("多选省市区") }}：</label>
          <GeographicalCascader
            v-model="multipleValue"
            :multiple="true"
            :collapse-tags="true"
            :placeholder="$T('请选择多个省份/城市/区县')"
            @change="handleMultipleChange"
          />
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("选中值") }}：</strong>
            {{ JSON.stringify(multipleValue) }}
          </p>
        </div>
      </div>

      <!-- 非严格模式 -->
      <div class="example-section">
        <h3>{{ $T("非严格模式") }}</h3>
        <div class="form-row">
          <label>{{ $T("可选择任意级别") }}：</label>
          <GeographicalCascader
            v-model="nonStrictValue"
            :check-strictly="true"
            :placeholder="$T('可选择省份、城市或区县')"
            @change="handleNonStrictChange"
          />
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("选中值") }}：</strong>
            {{ JSON.stringify(nonStrictValue) }}
          </p>
        </div>
      </div>

      <!-- 工具方法示例 -->
      <div class="example-section">
        <h3>{{ $T("工具方法示例") }}</h3>

        <!-- 根据编码获取路径 -->
        <div class="form-row">
          <label>{{ $T("根据编码获取路径") }}：</label>
          <ElInput
            v-model="provinceCode"
            :placeholder="$T('省份编码')"
            style="width: 120px; margin-right: 10px"
          />
          <ElInput
            v-model="cityCode"
            :placeholder="$T('城市编码')"
            style="width: 120px; margin-right: 10px"
          />
          <ElInput
            v-model="districtCode"
            :placeholder="$T('区县编码')"
            style="width: 120px; margin-right: 10px"
          />
          <ElButton @click="getPathByCode">{{ $T("获取路径") }}</ElButton>
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("路径结果") }}：</strong>
            {{ JSON.stringify(pathResult) }}
          </p>
        </div>

        <!-- 获取完整名称路径 -->
        <div class="form-row">
          <label>{{ $T("获取完整名称路径") }}：</label>
          <ElButton @click="getFullNamePath">
            {{ $T("获取基础选择的完整路径") }}
          </ElButton>
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("完整路径") }}：</strong>
            {{ fullNamePath }}
          </p>
          <p>
            <strong>{{ $T("最后一级名称") }}：</strong>
            {{ lastLevelName }}
          </p>
        </div>

        <!-- 搜索地区 -->
        <div class="form-row">
          <label>{{ $T("搜索地区") }}：</label>
          <ElInput
            v-model="searchKeyword"
            :placeholder="$T('输入地区名称搜索')"
            style="width: 200px; margin-right: 10px"
          />
          <ElButton @click="searchRegions">{{ $T("搜索") }}</ElButton>
        </div>
        <div class="result-display" v-if="searchResults.length > 0">
          <p>
            <strong>{{ $T("搜索结果") }}：</strong>
          </p>
          <ul style="margin: 10px 0; padding-left: 20px">
            <li
              v-for="(result, index) in searchResults"
              :key="index"
              style="margin: 5px 0"
            >
              <span style="color: var(--primary-color)">{{ result.type }}</span>
              :
              {{ result.fullPath }}
              <ElButton
                size="mini"
                style="margin-left: 10px"
                @click="selectSearchResult(result)"
              >
                {{ $T("选择") }}
              </ElButton>
            </li>
          </ul>
        </div>

        <!-- 验证值 -->
        <div class="form-row">
          <label>{{ $T("验证值") }}：</label>
          <ElInput
            v-model="validateInput"
            :placeholder="$T('输入JSON格式的值进行验证')"
            style="width: 300px; margin-right: 10px"
          />
          <ElButton @click="validateInputValue">{{ $T("验证") }}</ElButton>
        </div>
        <div class="result-display">
          <p>
            <strong>{{ $T("验证结果") }}：</strong>
            <span :style="{ color: validateResult ? 'green' : 'red' }">
              {{ validateResult ? $T("有效") : $T("无效") }}
            </span>
          </p>
        </div>
      </div>

      <!-- 刷新数据 -->
      <div class="example-section">
        <h3>{{ $T("数据管理") }}</h3>
        <div class="form-row">
          <ElButton @click="refreshData" :loading="refreshing">
            {{ $T("刷新地理数据") }}
          </ElButton>
          <ElButton @click="clearAllValues">
            {{ $T("清空所有选择") }}
          </ElButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GeographicalCascader from "./GeographicalCascader.vue";

export default {
  name: "GeographicalCascaderExample",
  components: {
    GeographicalCascader
  },
  data() {
    return {
      // 各种模式的选中值
      basicValue: [],
      provinceValue: [],
      cityValue: [],
      codeValue: [],
      lazyValue: [],
      multipleValue: [],
      nonStrictValue: [],

      // 选中的节点信息
      basicNodes: [],

      // 工具方法测试
      provinceCode: "",
      cityCode: "",
      districtCode: "",
      pathResult: [],

      // 新增的工具方法测试数据
      fullNamePath: "",
      lastLevelName: "",
      searchKeyword: "",
      searchResults: [],
      validateInput: "",
      validateResult: false,

      // 状态
      refreshing: false
    };
  },
  methods: {
    /**
     * 基础用法改变事件
     */
    handleBasicChange(value) {
      console.log("基础选择改变:", value);
    },

    /**
     * 基础用法节点改变事件
     */
    handleBasicNodeChange(nodes) {
      this.basicNodes = nodes;
      console.log("基础选择节点:", nodes);
    },

    /**
     * 省份选择改变事件
     */
    handleProvinceChange(value) {
      console.log("省份选择改变:", value);
    },

    /**
     * 城市选择改变事件
     */
    handleCityChange(value) {
      console.log("城市选择改变:", value);
    },

    /**
     * 编码显示改变事件
     */
    handleCodeChange(value) {
      console.log("编码选择改变:", value);
    },

    /**
     * 懒加载改变事件
     */
    handleLazyChange(value) {
      console.log("懒加载选择改变:", value);
    },

    /**
     * 多选改变事件
     */
    handleMultipleChange(value) {
      console.log("多选改变:", value);
    },

    /**
     * 非严格模式改变事件
     */
    handleNonStrictChange(value) {
      console.log("非严格模式改变:", value);
    },

    /**
     * 根据编码获取路径
     */
    getPathByCode() {
      const cascader =
        this.$refs.basicCascader ||
        this.$children.find(
          child => child.$options.name === "GeographicalCascader"
        );

      if (cascader) {
        this.pathResult = cascader.getPathByCode(
          this.provinceCode,
          this.cityCode || null,
          this.districtCode || null
        );
      }
    },

    /**
     * 刷新地理数据
     */
    async refreshData() {
      this.refreshing = true;
      try {
        // 刷新所有级联选择器的数据
        const cascaders = this.$children.filter(
          child => child.$options.name === "GeographicalCascader"
        );

        await Promise.all(cascaders.map(cascader => cascader.refresh()));
        this.$message.success(this.$T("地理数据刷新成功"));
      } catch (error) {
        console.error("刷新地理数据失败:", error);
        this.$message.error(this.$T("地理数据刷新失败"));
      } finally {
        this.refreshing = false;
      }
    },

    /**
     * 获取完整名称路径
     */
    getFullNamePath() {
      const cascader = this.getBasicCascader();
      if (cascader) {
        this.fullNamePath = cascader.getFullNamePath(this.basicValue);
        this.lastLevelName = cascader.getLastLevelName(this.basicValue);
      }
    },

    /**
     * 搜索地区
     */
    searchRegions() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning(this.$T("请输入搜索关键字"));
        return;
      }

      const cascader = this.getBasicCascader();
      if (cascader) {
        this.searchResults = cascader.searchByName(this.searchKeyword.trim());
      }
    },

    /**
     * 选择搜索结果
     */
    selectSearchResult(result) {
      this.basicValue = result.value;
      this.$message.success(this.$T("已选择：") + result.fullPath);
    },

    /**
     * 验证输入值
     */
    validateInputValue() {
      try {
        const value = JSON.parse(this.validateInput);
        const cascader = this.getBasicCascader();
        if (cascader) {
          this.validateResult = cascader.validateValue(value);
        }
      } catch (error) {
        this.$message.error(this.$T("输入格式错误，请输入有效的JSON数组"));
        this.validateResult = false;
      }
    },

    /**
     * 获取基础级联选择器实例
     */
    getBasicCascader() {
      return this.$children.find(
        child => child.$options.name === "GeographicalCascader"
      );
    },

    /**
     * 清空所有选择
     */
    clearAllValues() {
      this.basicValue = [];
      this.provinceValue = [];
      this.cityValue = [];
      this.codeValue = [];
      this.lazyValue = [];
      this.multipleValue = [];
      this.nonStrictValue = [];
      this.basicNodes = [];
      this.pathResult = [];
      this.provinceCode = "";
      this.cityCode = "";
      this.districtCode = "";
      this.fullNamePath = "";
      this.lastLevelName = "";
      this.searchKeyword = "";
      this.searchResults = [];
      this.validateInput = "";
      this.validateResult = false;

      this.$message.success(this.$T("已清空所有选择"));
    }
  }
};
</script>

<style scoped>
.geographical-cascader-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-header {
  margin-bottom: 30px;
  text-align: center;
}

.example-header h2 {
  color: var(--T1);
  margin-bottom: 10px;
}

.example-header p {
  color: var(--T2);
  font-size: 14px;
}

.example-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.example-section {
  background: var(--BG1);
  border: 1px solid var(--BD1);
  border-radius: var(--Ra);
  padding: 20px;
}

.example-section h3 {
  color: var(--T1);
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.form-row label {
  color: var(--T1);
  font-weight: 500;
  min-width: 120px;
  white-space: nowrap;
}

.form-row .geographical-cascader {
  flex: 1;
  min-width: 300px;
}

.result-display {
  background: var(--BG2);
  border: 1px solid var(--BD2);
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
}

.result-display p {
  margin: 5px 0;
  color: var(--T2);
  font-size: 14px;
  word-break: break-all;
}

.result-display strong {
  color: var(--T1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-row label {
    min-width: auto;
    margin-bottom: 5px;
  }

  .form-row .geographical-cascader {
    width: 100%;
    min-width: auto;
  }
}
</style>
