# 新增资源弹窗组件 (AddResourceDialog)

## 功能描述

这是一个用于新增资源的弹窗组件，基于项目的Element UI和@omega主题系统开发，支持主题切换和国际化。

## 组件特性

- 🎨 **主题支持**: 使用 @omega-theme 主题系统，支持亮色/暗色主题切换
- 🌍 **国际化**: 使用 @omega-i18n 国际化系统，支持中英文切换
- ✅ **表单验证**: 完整的表单验证规则，包括必填项、格式验证等
- 📱 **响应式**: 支持不同屏幕尺寸的响应式布局

## 表单字段

| 字段名 | 类型 | 必填 | 验证规则 | 说明 |
|--------|------|------|----------|------|
| 用户名 | 下拉选择 | ✅ | 必须选择一个用户 | 资源所属用户 |
| 资源名称 | 文本 | ✅ | 长度1-50字符 | 资源的名称 |
| 资源类型 | 下拉选择 | ✅ | 必须选择一个类型 | 发电/储电/用电 |
| 用电户号 | 文本 | ❌ | 最大50字符 | 用电户号信息 |
| 报装容量 | 数字 | ❌ | 数字格式，单位kVA | 报装容量 |
| 资源分类 | 下拉选择 | ❌ | 可选择分类 | 风电/光伏/储能/负荷 |
| 响应方式 | 下拉选择 | ❌ | 可选择方式 | 自动/手动 |
| 平台直控 | 下拉选择 | ❌ | 是/否 | 是否平台直控 |
| 最大上调功率 | 数字 | ❌ | 数字格式，单位kW/min | 最大上调功率 |
| 最大下调功率 | 数字 | ❌ | 数字格式，单位kW/min | 最大下调功率 |
| 控制 | 文本 | ❌ | 最大100字符 | 控制相关信息 |
| 移动 | 文本 | ❌ | 最大100字符 | 移动相关信息 |
| 联系方式 | 文本 | ❌ | 最大50字符 | 联系方式 |
| 地址 | 文本 | ❌ | 最大200字符 | 地址信息 |

## 使用方法

### 1. 引入组件

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="showAddResourceDialog">新增资源</el-button>
    
    <!-- 弹窗组件 -->
    <AddResourceDialog
      :visible="dialogVisible"
      :userOptions="userOptions"
      @close="handleClose"
      @save="handleSave"
    />
  </div>
</template>

<script>
import AddResourceDialog from "./AddResourceDialog.vue";

export default {
  components: {
    AddResourceDialog
  },
  data() {
    return {
      dialogVisible: false,
      userOptions: [
        { label: "用户A", value: "user_a" },
        { label: "用户B", value: "user_b" },
        // ... 更多用户选项
      ]
    };
  },
  methods: {
    showAddResourceDialog() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave(resourceData) {
      // 处理保存逻辑
      console.log("资源数据:", resourceData);
      // 调用API保存数据
      // ...
      this.dialogVisible = false;
    }
  }
};
</script>
```

### 2. Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 控制弹窗显示/隐藏 |
| userOptions | Array | [] | 用户选项数组，格式：[{label, value}] |

### 3. Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| close | - | 弹窗关闭时触发 |
| save | resourceData | 保存资源数据时触发，参数为表单数据对象 |

### 4. 返回的资源数据格式

```javascript
{
  userId: "user_a",
  resourceName: "资源名称",
  resourceType: "generation",
  electricityUserNumbers: "914403007852",
  registeredCapacity: "5000",
  resourceCategory: "wind",
  responseMode: "auto",
  platformDirectControl: true,
  maxUpRate: "100",
  maxDownRate: "80",
  control: "控制信息",
  mobile: "移动信息",
  contactInfo: "13800138000",
  address: "广州市天河区"
}
```

## 测试

可以使用 `test-add-resource-dialog.vue` 文件来测试弹窗功能：

```bash
# 在浏览器中访问测试页面
# 点击"打开新增资源弹窗"按钮测试功能
```

## 技术栈

- Vue 2.x
- Element UI
- @omega-theme (主题系统)
- @omega-i18n (国际化系统)

## 注意事项

1. 确保项目中已正确配置主题系统和国际化系统
2. 确保国际化文件中包含所需的文本翻译
3. 表单验证规则可根据实际业务需求进行调整
4. 组件使用了项目的主题变量，确保主题系统正常工作
