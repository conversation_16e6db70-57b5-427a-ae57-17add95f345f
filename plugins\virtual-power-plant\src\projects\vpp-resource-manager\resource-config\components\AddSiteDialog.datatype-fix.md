# AddSiteDialog 数据类型修复

## 问题描述

创建站点时，resourceId等字段传递的是字符串类型，但API要求数字类型，导致数据不正确。

## 修复内容

### 1. 核心字段类型转换

```javascript
const saveData = {
  siteId: siteId,                                    // string - 保持字符串
  resourceId: Number(this.resourceId),               // number - 修复 ✅
  vppId: Number(this.vppId),                        // number - 修复 ✅
  siteType: Number(this.formData.site_type),        // number - 修复 ✅
  siteName: this.formData.site_name,                // string - 保持字符串
  siteAddress: this.formData.site_address,          // string - 保持字符串
  contactPerson: this.formData.contact_person,      // string - 保持字符串
  phoneNumber: this.formData.phone_number,          // string - 保持字符串
  longitude: this.formData.longitude ? Number(this.formData.longitude) : null,  // number - 修复 ✅
  latitude: this.formData.latitude ? Number(this.formData.latitude) : null      // number - 修复 ✅
};
```

### 2. 特有字段类型转换

#### 存储类站点 (STORAGE)
```javascript
saveData.voltageLevel = this.formData.voltage_level ? Number(this.formData.voltage_level) : null;    // number ✅
saveData.gridVoltage = this.formData.grid_voltage ? Number(this.formData.grid_voltage) : null;      // number ✅
saveData.totalCapacity = this.formData.total_capacity ? Number(this.formData.total_capacity) : null; // number ✅
saveData.totalStorage = this.formData.total_storage ? Number(this.formData.total_storage) : null;   // number ✅
saveData.operationDate = this.formData.operation_date;                                              // string - 日期
saveData.roomId = this.formData.related_room ? Number(this.formData.related_room) : null;          // number ✅
saveData.imageUrl = this.formData.imageUrl;                                                         // string - URL
```

#### 可再生能源类站点 (RENEWABLE)
```javascript
saveData.voltageLevel = this.formData.voltage_level ? Number(this.formData.voltage_level) : null;    // number ✅
saveData.generationMode = this.formData.generation_mode;                                            // string - 发电模式
saveData.gridVoltage = this.formData.grid_voltage ? Number(this.formData.grid_voltage) : null;      // number ✅
saveData.totalCapacity = this.formData.total_capacity ? Number(this.formData.total_capacity) : null; // number ✅
saveData.operationDate = this.formData.operation_date;                                              // string - 日期
saveData.roomId = this.formData.related_room ? Number(this.formData.related_room) : null;          // number ✅
saveData.imageUrl = this.formData.imageUrl;                                                         // string - URL
```

#### 其他类站点 (OTHER)
```javascript
saveData.voltageLevel = this.formData.voltage_level ? Number(this.formData.voltage_level) : null;    // number ✅
saveData.operationDate = this.formData.operation_date;                                              // string - 日期
saveData.roomId = this.formData.related_room ? Number(this.formData.related_room) : null;          // number ✅
saveData.imageUrl = this.formData.imageUrl;                                                         // string - URL
```

## API要求的数据类型

根据 `/src/api/site-management/index.js` 中的文档：

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| siteId | string | ✅ | 站点编号 |
| siteName | string | ✅ | 站点名称 |
| siteType | **number** | ✅ | 站点类型 |
| vppId | **number** | ✅ | 所属VPP ID |
| resourceId | **number** | ✅ | 所属资源ID |
| roomId | **number** | ❌ | 对应房间ID |
| siteAddress | string | ❌ | 站点地址 |
| contactPerson | string | ❌ | 联系人 |
| phoneNumber | string | ❌ | 联系电话 |
| longitude | **number** | ❌ | 经度 |
| latitude | **number** | ❌ | 纬度 |
| deviceCount | **number** | ❌ | 设备数量 |

## 修复前后对比

### 修复前 ❌
```javascript
{
  resourceId: "123",        // 字符串 - 错误
  vppId: "456",            // 字符串 - 错误
  siteType: "1",           // 字符串 - 错误
  longitude: "116.404",    // 字符串 - 错误
  latitude: "39.915",      // 字符串 - 错误
  roomId: "789"            // 字符串 - 错误
}
```

### 修复后 ✅
```javascript
{
  resourceId: 123,         // 数字 - 正确
  vppId: 456,             // 数字 - 正确
  siteType: 1,            // 数字 - 正确
  longitude: 116.404,     // 数字 - 正确
  latitude: 39.915,       // 数字 - 正确
  roomId: 789             // 数字 - 正确
}
```

## 安全处理

- 使用 `Number()` 进行类型转换
- 对可选字段使用三元运算符：`value ? Number(value) : null`
- 避免 `NaN` 值传递给API
- 保持字符串字段不变（如siteName、siteAddress等）

## 测试验证

1. 打开新增站点弹窗
2. 填写完整信息
3. 提交前检查console.log输出的saveData
4. 确认所有数字字段都是number类型
5. 确认API调用成功

现在所有数据类型都符合API要求！
