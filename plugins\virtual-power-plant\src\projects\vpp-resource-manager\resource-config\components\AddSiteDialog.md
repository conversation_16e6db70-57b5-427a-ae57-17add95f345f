# 新增站点弹窗组件 (AddSiteDialog)

## 功能概述

新增站点弹窗组件用于在虚拟电厂资源管理系统中创建新的站点。该组件根据不同的站点类型显示相应的表单字段，支持三种不同的页面布局。

## 站点类型分组

### 第一组：储能类

- **2**: 用户侧储能
- **10**: 分布式独立储能

**特有字段**：

- 额定容量(kWh)
- 额定功率(kW)

### 第二组：新能源类

- **8**: 分布式光伏
- **9**: 分散式风电

**特有字段**：

- 装机容量(kW)
- 预计年发电量(kWh)

### 第三组：其他类

- **1**: 自备电源
- **3**: 电动汽车
- **4**: 充电站
- **5**: 换电站
- **6**: 楼宇空调
- **7**: 工商业可调节负荷
- **11**: 非可调节负荷
- **99**: 其他

**特有字段**：

- 设备规模
- 服务范围

## 基础字段（所有类型共有）

- 站点名称（必填）
- 站点地址（必填）
- 联系人（必填）
- 联系电话（必填，手机号格式验证）
- 经度（数值，-180 到 180）
- 纬度（数值，-90 到 90）

## 组件属性 (Props)

| 属性名     | 类型          | 默认值 | 说明              |
| ---------- | ------------- | ------ | ----------------- |
| visible    | Boolean       | false  | 控制弹窗显示/隐藏 |
| resourceId | String/Number | null   | 关联的资源 ID     |

## 组件事件 (Events)

| 事件名 | 参数     | 说明             |
| ------ | -------- | ---------------- |
| close  | -        | 弹窗关闭事件     |
| save   | siteData | 保存站点数据事件 |

## 使用示例

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="showAddSiteDialog">新增站点</el-button>

    <!-- 弹窗组件 -->
    <AddSiteDialog
      :visible="dialogVisible"
      :resourceId="currentResourceId"
      @close="handleClose"
      @save="handleSave"
    />
  </div>
</template>

<script>
import AddSiteDialog from "./AddSiteDialog.vue";

export default {
  components: {
    AddSiteDialog
  },
  data() {
    return {
      dialogVisible: false,
      currentResourceId: "resource_1"
    };
  },
  methods: {
    showAddSiteDialog() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave(siteData) {
      // 处理保存逻辑
      console.log("站点数据:", siteData);
      // 调用API保存数据
      // ...
      this.dialogVisible = false;
    }
  }
};
</script>
```

## 技术特性

- ✅ 使用 CetDialog 组件，符合项目规范
- ✅ 支持@omega-theme 主题系统
- ✅ 使用@omega-i18n 国际化
- ✅ 表单验证和错误提示
- ✅ 根据站点类型动态显示字段
- ✅ 响应式布局设计
- ✅ 数据格式验证（手机号、经纬度等）

## 数据结构

### 保存数据格式

```javascript
{
  resource_id: "resource_1",           // 关联资源ID
  site_type: 2,                       // 站点类型编码
  site_name: "储能站点A",              // 站点名称
  site_address: "广州市天河区...",      // 站点地址
  contact_person: "张三",              // 联系人
  phone_number: "13800138000",        // 联系电话
  longitude: 113.280637,              // 经度
  latitude: 23.125178,                // 纬度

  // 储能类特有字段
  rated_capacity: 1000,               // 额定容量(kWh)
  rated_power: 500,                   // 额定功率(kW)

  // 新能源类特有字段
  installed_capacity: 2000,           // 装机容量(kW)
  annual_generation: 3000000,         // 预计年发电量(kWh)

  // 其他类特有字段
  equipment_scale: "大型",             // 设备规模
  service_scope: "工业园区"            // 服务范围
}
```

## 注意事项

1. 使用前需要确保已选择资源节点
2. 表单验证会在提交时触发
3. 不同站点类型的特有字段会在类型切换时自动清空
4. 经纬度字段支持 6 位小数精度
5. 手机号码使用正则表达式验证格式

## 文件依赖

- `../../../../utils/siteTypes.js` - 站点类型常量定义
- `cet-common` - CetDialog 组件
- `@omega-theme` - 主题系统
- `@omega-i18n` - 国际化系统
