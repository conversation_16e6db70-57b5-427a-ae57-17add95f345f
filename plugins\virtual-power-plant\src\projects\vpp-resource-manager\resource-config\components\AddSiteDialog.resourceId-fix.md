# AddSiteDialog resourceId 格式修复

## 问题描述

resourceId的值是字符串格式："resource_1"，需要从中提取出数字部分传递给API。

## 修复方案

### 1. 添加提取函数

```javascript
// 从resourceId字符串中提取数字部分（格式：resource_1 -> 1）
const extractResourceId = (resourceIdStr) => {
  if (typeof resourceIdStr === 'string' && resourceIdStr.startsWith('resource_')) {
    return Number(resourceIdStr.replace('resource_', ''));
  }
  return Number(resourceIdStr);
};
```

### 2. 使用提取函数

```javascript
const saveData = {
  siteId: siteId,
  resourceId: extractResourceId(this.resourceId), // 从resource_1格式中提取数字
  vppId: Number(this.vppId),
  // ... 其他字段
};
```

## 测试用例

### 输入格式测试

| 输入值 | 预期输出 | 说明 |
|--------|----------|------|
| `"resource_1"` | `1` | 标准格式 ✅ |
| `"resource_123"` | `123` | 多位数字 ✅ |
| `"resource_0"` | `0` | 零值 ✅ |
| `"1"` | `1` | 纯数字字符串 ✅ |
| `1` | `1` | 数字类型 ✅ |
| `null` | `NaN` | 空值处理 ⚠️ |
| `undefined` | `NaN` | 未定义处理 ⚠️ |

### 函数逻辑

```javascript
function extractResourceId(resourceIdStr) {
  // 检查是否为字符串且以"resource_"开头
  if (typeof resourceIdStr === 'string' && resourceIdStr.startsWith('resource_')) {
    // 移除"resource_"前缀，转换为数字
    return Number(resourceIdStr.replace('resource_', ''));
  }
  // 其他情况直接转换为数字
  return Number(resourceIdStr);
}
```

## 实际应用示例

### 修复前 ❌
```javascript
{
  resourceId: NaN,  // Number("resource_1") = NaN
  // ... 其他字段
}
```

### 修复后 ✅
```javascript
{
  resourceId: 1,    // extractResourceId("resource_1") = 1
  // ... 其他字段
}
```

## 完整的数据转换流程

```javascript
// 输入数据
this.resourceId = "resource_1";

// 处理过程
const extractResourceId = (resourceIdStr) => {
  if (typeof resourceIdStr === 'string' && resourceIdStr.startsWith('resource_')) {
    return Number(resourceIdStr.replace('resource_', ''));  // "resource_1" -> "1" -> 1
  }
  return Number(resourceIdStr);
};

// 输出结果
const saveData = {
  resourceId: extractResourceId(this.resourceId), // 1
  // ...
};
```

## 兼容性考虑

这个函数能够处理多种输入格式：

1. **标准格式**: `"resource_123"` → `123`
2. **纯数字字符串**: `"123"` → `123`
3. **数字类型**: `123` → `123`
4. **边界情况**: 空值会返回`NaN`，需要在调用时注意

## 建议改进

如果需要更严格的错误处理，可以这样改进：

```javascript
const extractResourceId = (resourceIdStr) => {
  if (!resourceIdStr) {
    throw new Error('resourceId不能为空');
  }
  
  if (typeof resourceIdStr === 'string' && resourceIdStr.startsWith('resource_')) {
    const id = Number(resourceIdStr.replace('resource_', ''));
    if (isNaN(id)) {
      throw new Error('无效的resourceId格式');
    }
    return id;
  }
  
  const id = Number(resourceIdStr);
  if (isNaN(id)) {
    throw new Error('resourceId必须是有效的数字');
  }
  return id;
};
```

现在resourceId能够正确从"resource_1"格式中提取出数字1了！
