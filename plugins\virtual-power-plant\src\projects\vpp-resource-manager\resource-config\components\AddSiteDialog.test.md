# AddSiteDialog API 调用修复测试

## 修复内容

### 1. 添加API导入
```javascript
import { createSite } from "@/api/site-management";
```

### 2. 添加vppId props
```javascript
props: {
  // ... 其他props
  vppId: {
    type: [String, Number],
    default: null
  }
}
```

### 3. 修复保存逻辑
- 生成站点编号：`SITE + 时间戳后6位 + 随机2位数字`
- 使用驼峰命名的字段名
- 调用真正的API接口
- 正确处理响应和错误

### 4. 更新SiteManagement组件
- 传递vppId参数给AddSiteDialog
- 在保存成功后刷新站点列表

## 测试步骤

1. 打开虚拟电厂资源管理页面
2. 选择一个资源节点
3. 点击"新增站点"按钮
4. 填写站点信息
5. 点击"确定"按钮
6. 检查是否：
   - 调用了createSite API
   - 传递了正确的参数格式
   - 显示成功消息
   - 刷新了站点列表
   - 关闭了弹窗

## 预期结果

- ✅ 不再显示TODO注释
- ✅ 调用真正的API接口
- ✅ 传递正确的参数格式（驼峰命名）
- ✅ 生成唯一的站点编号
- ✅ 正确处理API响应
- ✅ 刷新站点列表数据

## API参数格式

```javascript
{
  siteId: "SITE123456789",      // 生成的站点编号
  resourceId: 123,              // 资源ID
  vppId: 456,                   // VPP ID
  siteType: 1,                  // 站点类型
  siteName: "测试站点",          // 站点名称
  siteAddress: "测试地址",       // 站点地址
  contactPerson: "张三",         // 联系人
  phoneNumber: "13800138000",   // 联系电话
  longitude: 116.404,           // 经度
  latitude: 39.915,             // 纬度
  // 根据站点类型添加的特有字段...
}
```
