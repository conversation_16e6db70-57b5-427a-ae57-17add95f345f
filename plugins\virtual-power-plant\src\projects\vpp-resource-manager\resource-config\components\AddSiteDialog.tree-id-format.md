# VPP树组件tree_id格式说明

## 数据流分析

### 1. API原始数据
```javascript
// 从API获取的原始树节点数据
{
  id: 1,                    // 数字类型的资源ID
  label: "resource",        // 节点类型
  name: "测试资源",
  parentId: 123,
  // ... 其他字段
}
```

### 2. 树组件数据转换
在 `src/api/vpp-tree-cache/index.js` 的 `transformTreeNode` 函数中：

```javascript
function transformTreeNode(apiNode) {
  const transformed = {
    tree_id: `${apiNode.label}_${apiNode.id}`, // 组合类型和ID作为tree_id
    // 例如：resource_1, user_123, site_456
    originalId: apiNode.id,                     // 保留原始数字ID
    type: apiNode.label,                        // 节点类型
    // ... 其他字段
  };
}
```

**转换结果**：
- 原始ID `1` → tree_id `"resource_1"`
- 原始ID `123` → tree_id `"user_123"`
- 原始ID `456` → tree_id `"site_456"`

### 3. 组件间传递
在 `SiteManagement.vue` 中：

```javascript
// 选中节点时
this.currentResourceId = this.selectedNode.tree_id; // "resource_1"

// 传递给AddSiteDialog
<AddSiteDialog :resourceId="currentResourceId" />
```

### 4. API调用转换
在 `AddSiteDialog.vue` 中：

```javascript
// 需要将tree_id格式转换回数字ID
const extractResourceId = (resourceIdStr) => {
  if (typeof resourceIdStr === 'string' && resourceIdStr.startsWith('resource_')) {
    return Number(resourceIdStr.replace('resource_', '')); // "resource_1" → 1
  }
  return Number(resourceIdStr);
};

// 调用API时使用数字ID
const saveData = {
  resourceId: extractResourceId(this.resourceId), // 1
  // ...
};
```

## 为什么使用这种格式？

### 1. **唯一性保证**
不同类型的节点可能有相同的ID，使用`type_id`格式确保tree_id的唯一性：
```javascript
// 可能存在相同ID的不同类型节点
resource_1  // 资源ID=1
user_1      // 用户ID=1  
site_1      // 站点ID=1
```

### 2. **类型识别**
通过tree_id可以直接识别节点类型：
```javascript
if (tree_id.startsWith('resource_')) {
  // 这是资源节点
} else if (tree_id.startsWith('site_')) {
  // 这是站点节点
}
```

### 3. **层级关系**
便于构建父子关系：
```javascript
// 父节点ID也使用相同格式
pId: apiNode.parentId ? `${getParentLabel(apiNode)}_${apiNode.parentId}` : null
```

## 数据格式对照表

| 阶段 | 字段名 | 格式 | 示例 | 说明 |
|------|--------|------|------|------|
| API原始数据 | `id` | number | `1` | 数据库中的真实ID |
| 树组件数据 | `tree_id` | string | `"resource_1"` | 组合格式，确保唯一性 |
| 树组件数据 | `originalId` | number | `1` | 保留的原始ID |
| API调用数据 | `resourceId` | number | `1` | 转换回数字格式 |

## 其他节点类型示例

```javascript
// VPP节点
{ id: 100, label: "vpp" } → tree_id: "vpp_100"

// 用户节点  
{ id: 200, label: "user" } → tree_id: "user_200"

// 资源节点
{ id: 300, label: "resource" } → tree_id: "resource_300"

// 站点节点
{ id: 400, label: "site" } → tree_id: "site_400"

// 设备节点
{ id: 500, label: "device" } → tree_id: "device_500"
```

## 最佳实践

### 1. **获取原始ID**
如果需要原始数字ID，优先使用`originalId`字段：
```javascript
const realId = selectedNode.originalId; // 推荐
// 或者从tree_id中提取
const realId = extractResourceId(selectedNode.tree_id); // 备选
```

### 2. **类型判断**
```javascript
const nodeType = selectedNode.type; // 直接使用type字段
// 或者从tree_id中判断
const nodeType = selectedNode.tree_id.split('_')[0]; // 备选
```

### 3. **API调用**
始终确保传递给API的是正确的数据类型：
```javascript
// ✅ 正确：转换为数字
resourceId: Number(extractResourceId(this.resourceId))

// ❌ 错误：直接传递字符串
resourceId: this.resourceId // "resource_1"
```

这种设计确保了树组件的正确运行，同时保持了与后端API的兼容性。
