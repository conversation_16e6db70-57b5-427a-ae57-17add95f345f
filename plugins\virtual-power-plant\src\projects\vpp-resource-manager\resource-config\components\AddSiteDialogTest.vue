<template>
  <div class="add-site-dialog-test bg-BG p-J3">
    <div class="test-header bg-BG1 rounded-lg p-J3 mb-J3">
      <h2 class="text-T1 font-medium mb-J2">{{ $T("新增站点弹窗测试") }}</h2>
      <p class="text-T2 mb-J3">
        {{ $T("测试新增站点弹窗功能，支持三种不同类型的站点表单") }}
      </p>

      <div class="test-buttons flex gap-J2">
        <el-button type="primary" @click="openDialog">
          {{ $T("打开新增站点弹窗") }}
        </el-button>
        <el-button @click="switchResourceType">
          {{ $T("切换资源类型") }}
        </el-button>
        <el-button @click="clearLog">
          {{ $T("清空日志") }}
        </el-button>
      </div>

      <div class="current-resource bg-BG2 rounded p-J2 mt-J2">
        <div class="text-T2 text-sm">
          <span class="font-medium">{{ $T("当前模拟资源") }}:</span>
          {{ mockResourceInfo.name }} ({{
            $T(getResourceTypeLabel(mockResourceInfo.type))
          }})
        </div>
      </div>
    </div>

    <div class="test-content bg-BG1 rounded-lg p-J3">
      <h3 class="text-T1 font-medium mb-J2">{{ $T("操作日志") }}</h3>
      <div
        class="log-container bg-BG2 rounded p-J2"
        style="height: 300px; overflow-y: auto"
      >
        <div
          v-for="(log, index) in logs"
          :key="index"
          class="log-item text-T2 mb-J1"
          :class="log.type"
        >
          <span class="timestamp text-T3">[{{ log.timestamp }}]</span>
          <span class="message">{{ log.message }}</span>
          <pre v-if="log.data" class="data mt-J1 text-T3">{{
            JSON.stringify(log.data, null, 2)
          }}</pre>
        </div>
      </div>
    </div>

    <!-- 新增站点弹窗 -->
    <AddSiteDialog
      :visible="dialogVisible"
      :resourceId="mockResourceInfo.id"
      :resourceInfo="mockResourceInfo"
      @close="handleDialogClose"
      @save="handleDialogSave"
    />
  </div>
</template>

<script>
import AddSiteDialog from "./AddSiteDialog.vue";

export default {
  name: "AddSiteDialogTest",
  components: {
    AddSiteDialog
  },
  data() {
    return {
      dialogVisible: false,
      mockResourceId: "resource_test_001",
      currentResourceIndex: 0,
      mockResourceTypes: [
        {
          id: "resource_generation_001",
          name: "发电资源",
          type: 1 // 发电资源
        },
        {
          id: "resource_storage_001",
          name: "储电资源",
          type: 2 // 储电资源
        },
        {
          id: "resource_consumption_001",
          name: "用电资源",
          type: 3 // 用电资源
        },
        {
          id: "resource_microgrid_001",
          name: "微电网资源",
          type: 4 // 微电网资源
        }
      ],
      logs: []
    };
  },
  computed: {
    mockResourceInfo() {
      return this.mockResourceTypes[this.currentResourceIndex];
    }
  },
  mounted() {
    this.addLog("info", "测试页面已加载");
  },
  methods: {
    // 打开弹窗
    openDialog() {
      this.addLog("info", "打开新增站点弹窗", {
        resourceId: this.mockResourceInfo.id,
        resourceInfo: this.mockResourceInfo
      });
      this.dialogVisible = true;
    },

    // 切换资源类型
    switchResourceType() {
      this.currentResourceIndex =
        (this.currentResourceIndex + 1) % this.mockResourceTypes.length;
      this.addLog(
        "info",
        `切换到资源类型: ${this.mockResourceInfo.name}`,
        this.mockResourceInfo
      );
    },

    // 弹窗关闭处理
    handleDialogClose() {
      this.addLog("info", "弹窗已关闭");
      this.dialogVisible = false;
    },

    // 弹窗保存处理
    handleDialogSave(siteData) {
      this.addLog("success", "站点数据保存成功", siteData);
      this.dialogVisible = false;

      // 模拟API调用
      setTimeout(() => {
        this.addLog("info", "模拟API调用完成");
      }, 1000);
    },

    // 添加日志
    addLog(type, message, data = null) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.push({
        type,
        timestamp,
        message,
        data
      });

      // 自动滚动到底部
      this.$nextTick(() => {
        const container = this.$el.querySelector(".log-container");
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },

    // 清空日志
    clearLog() {
      this.logs = [];
      this.addLog("info", "日志已清空");
    },

    // 获取资源类型标签
    getResourceTypeLabel(resourceType) {
      const typeMap = {
        1: "发电资源",
        2: "储电资源",
        3: "用电资源",
        4: "微电网资源"
      };
      return typeMap[resourceType] || "未知资源类型";
    }
  }
};
</script>

<style scoped>
.add-site-dialog-test {
  min-height: 100vh;
}

.log-item {
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1.4;
  padding: 4px 0;
  border-bottom: 1px solid var(--BG3);
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.info .message {
  color: var(--T1);
}

.log-item.success .message {
  color: var(--Sta1);
}

.log-item.error .message {
  color: var(--Sta3);
}

.log-item.warning .message {
  color: var(--Sta2);
}

.timestamp {
  font-weight: bold;
  margin-right: 8px;
}

.data {
  background: var(--BG3);
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: pre-wrap;
  word-break: break-all;
}

.test-buttons {
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .test-buttons {
    flex-direction: column;
    align-items: flex-start;
  }

  .test-buttons .el-button {
    width: 100%;
    margin-bottom: 8px;
  }
}
</style>
