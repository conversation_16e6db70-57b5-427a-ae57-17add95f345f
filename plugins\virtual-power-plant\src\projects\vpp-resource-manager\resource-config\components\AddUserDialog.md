# 新增代理用户弹窗组件 (AddUserDialog)

## 功能描述

这是一个用于新增代理用户的弹窗组件，基于项目的CET组件库开发，支持主题切换和国际化。

## 组件特性

- 🎨 **主题支持**: 使用 @omega-theme 主题系统，支持亮色/暗色主题切换
- 🌍 **国际化**: 使用 @omega-i18n 国际化系统，支持中英文切换
- ✅ **表单验证**: 完整的表单验证规则，包括必填项、格式验证等
- 📱 **响应式**: 支持不同屏幕尺寸的响应式布局

## 表单字段

| 字段名 | 类型 | 必填 | 验证规则 | 说明 |
|--------|------|------|----------|------|
| 用户名称 | 文本 | ✅ | 长度1-50字符，不能包含特殊字符 | 代理用户的名称 |
| 联系人 | 文本 | ✅ | 长度1-50字符 | 联系人姓名 |
| 联系电话 | 文本 | ✅ | 手机号格式验证 | 联系人手机号 |
| 区域 | 下拉选择 | ✅ | 必须选择一个区域 | 用户所属区域 |
| 备注 | 文本域 | ❌ | 最大255字符 | 可选的备注信息 |

## 使用方法

### 1. 引入组件

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="showAddUserDialog">新增代理用户</el-button>
    
    <!-- 弹窗组件 -->
    <AddUserDialog
      :visible="dialogVisible"
      :regionOptions="regionOptions"
      @close="handleClose"
      @save="handleSave"
    />
  </div>
</template>

<script>
import AddUserDialog from "./AddUserDialog.vue";

export default {
  components: {
    AddUserDialog
  },
  data() {
    return {
      dialogVisible: false,
      regionOptions: [
        { label: "广州", value: "guangzhou" },
        { label: "深圳", value: "shenzhen" },
        // ... 更多区域选项
      ]
    };
  },
  methods: {
    showAddUserDialog() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave(userData) {
      // 处理保存逻辑
      console.log("用户数据:", userData);
      // 调用API保存数据
      // ...
      this.dialogVisible = false;
    }
  }
};
</script>
```

### 2. Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 控制弹窗显示/隐藏 |
| regionOptions | Array | [] | 区域选项数组，格式：[{label, value}] |

### 3. Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| close | - | 弹窗关闭时触发 |
| save | userData | 保存用户数据时触发，参数为表单数据对象 |

### 4. 返回的用户数据格式

```javascript
{
  userName: "用户名称",
  contactPerson: "联系人",
  contactPhone: "13800138000",
  region: "guangzhou",
  remark: "备注信息"
}
```

## 测试

可以使用 `test-add-user-dialog.vue` 文件来测试弹窗功能：

```bash
# 在浏览器中访问测试页面
# 点击"打开新增代理用户弹窗"按钮测试功能
```

## 技术栈

- Vue 2.x
- Element UI
- CET Common (cet-common)
- @omega-theme (主题系统)
- @omega-i18n (国际化系统)

## 注意事项

1. 确保项目中已正确配置 CET 组件库
2. 确保国际化文件中包含所需的文本翻译
3. 表单验证规则依赖 `base/utils/common.js` 中的验证函数
4. 组件使用了项目的主题变量，确保主题系统正常工作
