<template>
  <div class="device-management">
    <el-button type="primary" @click="handleAdd">新建设备</el-button>
    <el-table :data="devices" style="width: 100%; margin-top: 16px">
      <el-table-column prop="device_name" label="设备名称" />
      <el-table-column prop="device_type" label="设备类型" />
      <el-table-column prop="device_status" label="设备状态" />
      <el-table-column prop="rated_power" label="额定功率" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small">编辑</el-button>
          <el-button type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  name: "DeviceManagement",
  data() {
    return {
      devices: [
        {
          id: 1,
          device_name: "逆变器A",
          device_type: "逆变器",
          device_status: "正常",
          rated_power: "100kW"
        },
        {
          id: 2,
          device_name: "储能柜B",
          device_type: "储能柜",
          device_status: "故障",
          rated_power: "200kW"
        }
      ]
    };
  },
  methods: {
    handleAdd() {
      // TODO: 新建设备逻辑
    }
  }
};
</script>
<style scoped>
.device-management {
  padding: 16px;
}
.el-button {
  font-size: 14px;
  font-weight: 500;
  /* 按Figma规范可进一步调整 */
}
.el-table {
  background: #fff;
  border-radius: 8px;
  /* 按Figma规范可进一步调整 */
}
</style>
