<template>
  <el-dialog
    :title="$T('编辑资源')"
    :visible.sync="dialogVisible"
    width="1000px"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :top="'5vh'"
    class="edit-resource-dialog"
    @close="onClose"
  >
    <div class="dialog-content">
      <el-form
        ref="editResourceForm"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="top"
      >
        <!-- 只读信息区域 -->
        <div class="readonly-section mb-J4">
          <div class="section-title text-H4 font-bold mb-J3 text-T1">
            {{ $T("基本信息") }}
          </div>
          <el-row :gutter="20">
            <!-- 电户号 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("电户号") }}</span>
                <div class="readonly-value text-T1">
                  {{ formData.electricityUserNumbers || "--" }}
                </div>
              </el-form-item>
            </el-col>
            <!-- 平台直控 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("平台直控") }}</span>
                <div class="readonly-value text-T1">
                  {{
                    getPlatformDirectControlLabel(
                      formData.platformDirectControl
                    )
                  }}
                </div>
              </el-form-item>
            </el-col>
            <!-- 资源类型 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("资源类型") }}</span>
                <div class="readonly-value text-T1">
                  {{ getResourceTypeLabel(formData.resourceType) }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 响应方式 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("响应方式") }}</span>
                <div class="readonly-value text-T1">
                  {{ getResponseModeLabel(formData.responseMode) }}
                </div>
              </el-form-item>
            </el-col>
            <!-- 资源状态 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("资源状态") }}</span>
                <div class="readonly-value text-T1">
                  {{ getResourceStatusLabel(formData.resourceStatus) }}
                </div>
              </el-form-item>
            </el-col>
            <!-- 区域 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("区域") }}</span>
                <div class="readonly-value text-T1">
                  {{ getRegionLabel(formData.region) }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 可编辑信息区域 -->
        <div class="editable-section">
          <div class="section-title text-H4 font-bold mb-J3 text-T1">
            {{ $T("可编辑信息") }}
          </div>
          <el-row :gutter="20">
            <!-- 资源名称 -->
            <el-col :span="8">
              <el-form-item prop="resourceName">
                <span slot="label" class="required-label">
                  {{ $T("资源名称") }}
                </span>
                <el-input
                  v-model="formData.resourceName"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 报装容量 -->
            <el-col :span="8">
              <el-form-item prop="registeredCapacity">
                <span slot="label" class="required-label">
                  {{ $T("报装容量") }}
                </span>
                <el-input
                  v-model="formData.registeredCapacity"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kVA</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最大可运行功率 -->
            <el-col :span="8">
              <el-form-item prop="maxRunningPower">
                <span slot="label" class="required-label">
                  {{ $T("最大可运行功率") }}
                </span>
                <el-input
                  v-model="formData.maxRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 最小可运行功率 -->
            <el-col :span="8">
              <el-form-item prop="minRunningPower">
                <span slot="label" class="required-label">
                  {{ $T("最小可运行功率") }}
                </span>
                <el-input
                  v-model="formData.minRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最大上调速率 -->
            <el-col :span="8">
              <el-form-item prop="maxUpRate">
                <span slot="label">{{ $T("最大上调速率") }}</span>
                <el-input
                  v-model="formData.maxUpRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW/min</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最大下调速率 -->
            <el-col :span="8">
              <el-form-item prop="maxDownRate">
                <span slot="label">{{ $T("最大下调速率") }}</span>
                <el-input
                  v-model="formData.maxDownRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW/min</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 经度 -->
            <el-col :span="6">
              <el-form-item prop="longitude">
                <span slot="label">{{ $T("经度") }}</span>
                <el-input
                  v-model="formData.longitude"
                  :placeholder="$T('请输入经度')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 纬度 -->
            <el-col :span="6">
              <el-form-item prop="latitude">
                <span slot="label">{{ $T("纬度") }}</span>
                <el-input
                  v-model="formData.latitude"
                  :placeholder="$T('请输入纬度')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 联系人 -->
            <el-col :span="6">
              <el-form-item prop="contactPerson">
                <span slot="label">{{ $T("联系人") }}</span>
                <el-input
                  v-model="formData.contactPerson"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 联系电话 -->
            <el-col :span="6">
              <el-form-item prop="contactPhone">
                <span slot="label">{{ $T("联系电话") }}</span>
                <el-input
                  v-model="formData.contactPhone"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 地址 -->
            <el-col :span="24">
              <el-form-item prop="address">
                <span slot="label">{{ $T("地址") }}</span>
                <el-input
                  v-model="formData.address"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCancel">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="onSave" :loading="saving">
        {{ $T("保存") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "EditResourceDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    resourceData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit("close");
        }
      }
    }
  },
  data() {
    return {
      saving: false,
      formData: {
        electricityUserNumbers: "",
        resourceName: "",
        registeredCapacity: "",
        platformDirectControl: "",
        resourceType: "",
        responseMode: "",
        resourceStatus: "",
        maxRunningPower: "",
        minRunningPower: "",
        maxUpRate: "",
        maxDownRate: "",
        longitude: "",
        latitude: "",
        contactPerson: "",
        contactPhone: "",
        region: "",
        address: ""
      },
      formRules: {
        resourceName: [
          {
            required: true,
            message: this.$T("资源名称不能为空"),
            trigger: "blur"
          }
        ],
        registeredCapacity: [
          {
            required: true,
            message: this.$T("报装容量不能为空"),
            trigger: "blur"
          }
        ],
        maxRunningPower: [
          {
            required: true,
            message: this.$T("最大可运行功率不能为空"),
            trigger: "blur"
          }
        ],
        minRunningPower: [
          {
            required: true,
            message: this.$T("最小可运行功率不能为空"),
            trigger: "blur"
          }
        ]
      },
      // 选项数据
      platformDirectControlOptions: [
        { label: "是", value: true },
        { label: "否", value: false }
      ],
      resourceTypeOptions: [
        { label: "发电", value: "generation" },
        { label: "储电", value: "storage" },
        { label: "用电", value: "consumption" }
      ],
      responseModeOptions: [
        { label: "自动", value: "auto" },
        { label: "手动", value: "manual" }
      ],
      resourceStatusOptions: [
        { label: "正常", value: "normal" },
        { label: "故障", value: "fault" },
        { label: "维护", value: "maintenance" }
      ],
      regionOptions: [
        { label: "广州", value: "guangzhou" },
        { label: "深圳", value: "shenzhen" },
        { label: "佛山", value: "foshan" },
        { label: "东莞", value: "dongguan" },
        { label: "中山", value: "zhongshan" },
        { label: "珠海", value: "zhuhai" }
      ]
    };
  },
  watch: {
    resourceData: {
      handler(newData) {
        if (newData && Object.keys(newData).length > 0) {
          this.loadResourceData();
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    loadResourceData() {
      // 加载资源数据进行回显，处理字段映射
      this.formData = {
        id: this.resourceData.id,
        electricityUserNumbers: this.resourceData.electric_account || "",
        resourceName: this.resourceData.resource_name || "",
        registeredCapacity: this.resourceData.rated_capacity || "",
        platformDirectControl:
          this.resourceData.platform_type === "是" ? true : false,
        resourceType: this.mapResourceType(this.resourceData.resource_type),
        responseMode: this.resourceData.response_mode || "auto",
        resourceStatus: this.mapResourceStatus(this.resourceData.status),
        maxRunningPower: this.resourceData.max_running_power || "",
        minRunningPower: this.resourceData.min_running_power || "",
        maxUpRate: this.resourceData.max_up_rate || "",
        maxDownRate: this.resourceData.max_down_rate || "",
        longitude: this.resourceData.longitude || "",
        latitude: this.resourceData.latitude || "",
        contactPerson: this.resourceData.contact_person || "",
        contactPhone: this.resourceData.contact_phone || "",
        region: this.mapRegion(this.resourceData.region),
        address: this.resourceData.address || ""
      };
    },
    mapResourceType(type) {
      const typeMap = {
        load: "consumption",
        solar: "generation",
        storage: "storage",
        fault: "generation"
      };
      return typeMap[type] || "generation";
    },
    mapResourceStatus(status) {
      const statusMap = {
        online: "normal",
        offline: "fault",
        fault: "fault",
        maintenance: "maintenance",
        storage: "normal"
      };
      return statusMap[status] || "normal";
    },
    mapRegion(region) {
      if (region === "---" || !region) {
        return "guangzhou"; // 默认广州
      }
      return region;
    },
    getPlatformDirectControlLabel(value) {
      const option = this.platformDirectControlOptions.find(
        item => item.value === value
      );
      return option ? this.$T(option.label) : "--";
    },
    getResourceTypeLabel(value) {
      const option = this.resourceTypeOptions.find(
        item => item.value === value
      );
      return option ? this.$T(option.label) : "--";
    },
    getResponseModeLabel(value) {
      const option = this.responseModeOptions.find(
        item => item.value === value
      );
      return option ? this.$T(option.label) : "--";
    },
    getResourceStatusLabel(value) {
      const option = this.resourceStatusOptions.find(
        item => item.value === value
      );
      return option ? this.$T(option.label) : "--";
    },
    getRegionLabel(value) {
      const option = this.regionOptions.find(item => item.value === value);
      return option ? this.$T(option.label) : "--";
    },
    onSave() {
      this.$refs.editResourceForm.validate(valid => {
        if (valid) {
          this.saving = true;

          // 模拟保存操作
          setTimeout(() => {
            this.saving = false;
            this.$message.success(this.$T("保存成功"));

            // 发送保存事件，只传递可编辑的字段
            this.$emit("save", {
              id: this.formData.id,
              resourceName: this.formData.resourceName,
              registeredCapacity: this.formData.registeredCapacity,
              maxRunningPower: this.formData.maxRunningPower,
              minRunningPower: this.formData.minRunningPower,
              maxUpRate: this.formData.maxUpRate,
              maxDownRate: this.formData.maxDownRate,
              longitude: this.formData.longitude,
              latitude: this.formData.latitude,
              contactPerson: this.formData.contactPerson,
              contactPhone: this.formData.contactPhone,
              address: this.formData.address
            });

            this.onClose();
          }, 1000);
        } else {
          this.$message.error(this.$T("请检查输入信息"));
        }
      });
    },
    onCancel() {
      this.onClose();
    },
    onClose() {
      this.$refs.editResourceForm?.resetFields();
      this.$emit("close");
    }
  }
};
</script>

<style scoped>
.dialog-content {
  padding: var(--J2);
  max-height: calc(90vh - 160px); /* 确保内容区域不会超出视窗 */
  overflow-y: auto;
}

.readonly-section {
  background: var(--BG2);
  border-radius: var(--Ra1);
  padding: var(--J3);
  border: 1px solid var(--B2);
}

.editable-section {
  background: var(--BG1);
  border-radius: var(--Ra1);
  padding: var(--J3);
  border: 1px solid var(--B2);
}

.section-title {
  position: relative;
  padding-left: var(--J2);
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: var(--ZS);
  border-radius: 2px;
}

.readonly-value {
  font-size: var(--Aa);
  line-height: 32px;
  color: var(--T1);
}

.required-label::before {
  content: "*";
  color: var(--ER);
  margin-right: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 弹窗样式 */
.edit-resource-dialog .el-dialog {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.edit-resource-dialog .el-dialog__body {
  padding: 0;
  flex: 1;
  overflow-y: auto;
  max-height: calc(90vh - 120px); /* 减去头部和底部的高度 */
}

.edit-resource-dialog .el-form-item__label {
  color: var(--T1);
  font-weight: 500;
}

.edit-resource-dialog .el-input__inner {
  background: var(--BG1);
  border-color: var(--B2);
  color: var(--T1);
}

.edit-resource-dialog .el-input__inner:focus {
  border-color: var(--ZS);
}

.edit-resource-dialog .el-select .el-input__inner {
  background: var(--BG1);
}

/* 响应式调整 */
@media (max-height: 800px) {
  .edit-resource-dialog .el-dialog {
    margin-top: 2vh !important;
    margin-bottom: 2vh !important;
    max-height: 96vh;
  }

  .edit-resource-dialog .el-dialog__body {
    max-height: calc(96vh - 120px);
  }

  .dialog-content {
    max-height: calc(96vh - 160px);
  }
}

@media (max-height: 600px) {
  .edit-resource-dialog .el-dialog {
    margin-top: 1vh !important;
    margin-bottom: 1vh !important;
    max-height: 98vh;
  }

  .edit-resource-dialog .el-dialog__body {
    max-height: calc(98vh - 120px);
  }

  .dialog-content {
    max-height: calc(98vh - 160px);
  }
}
</style>
