# 虚拟电厂资源管理组件说明

## 概述

本模块包含两个主要组件：

1. **ResourceManagement.vue** - 主组件，整合了左侧树形结构和右侧资源管理表格
2. **VppTree.vue** - 树形组件，基于 CetGiantTree 实现，严格遵循 Figma 设计稿的 UI 规范

## 重构说明 (2025-07-12)

### 重构目标

将原本独立的 ResourceManagement.vue 和 VppTree.vue 组件整合为一个完整的资源管理页面，实现左侧树形结构与右侧表格数据的联动，符合页面设计要求。

### 主要变更

- **布局整合**: 左侧树形 + 右侧表格的完整页面布局
- **数据联动**: 树节点选择与表格数据过滤的实时联动
- **功能增强**: 完整的搜索、筛选、分页功能
- **响应式设计**: 支持桌面端、平板端、移动端适配

## 设计规范

本组件完全按照 `plugins/virtual-power-plant/doc/tree.txt` 中的 SVG 设计稿实现，包括：

- 整体尺寸：312px × 794px
- 搜索框高度：30px，位置在顶部
- 树节点行高：30px
- 展开/收起箭头：精确匹配 SVG 中的三角形样式
- 节点图标：根据 SVG 中的 clipPath 路径定制
- 层级缩进：每级增加 28px 缩进
- 颜色规范：严格按照设计稿中的颜色值

## 功能特性

- ✅ 支持虚拟电厂层级结构展示（虚拟电厂 → 用户 → 资源 → 站点 → 设备）
- ✅ 内置搜索功能
- ✅ 自定义节点图标（虚拟电厂、用户、资源、站点、设备）
- ✅ 符合 Figma 设计稿的样式规范
- ✅ 支持主题切换（light/dark/blue）
- ✅ 响应式布局适配
- ✅ 节点点击和勾选事件

## 使用方式

### 基本用法

```vue
<template>
  <VppTree
    :tree-data="treeData"
    @node-click="handleNodeClick"
    @nodes-checked="handleNodesChecked"
  />
</template>

<script>
import VppTree from "./components/VppTree.vue";

export default {
  components: {
    VppTree
  },
  data() {
    return {
      treeData: [] // 可选，组件内部有默认数据
    };
  },
  methods: {
    handleNodeClick(node) {
      console.log("选中节点:", node);
    },
    handleNodesChecked(nodes) {
      console.log("勾选节点:", nodes);
    }
  }
};
</script>
```

### Props

| 属性名    | 类型  | 默认值 | 说明                                 |
| --------- | ----- | ------ | ------------------------------------ |
| tree-data | Array | []     | 树形数据（可选，组件内部有默认数据） |

### Events

| 事件名        | 参数  | 说明         |
| ------------- | ----- | ------------ |
| node-click    | node  | 节点点击事件 |
| nodes-checked | nodes | 节点勾选事件 |

### 数据格式

```javascript
const treeData = [
  {
    tree_id: "vpp_1",
    pId: null,
    name: "XX虚拟电厂",
    type: "vpp",
    open: true,
    isParent: true,
    iconSkin: "vpp-icon"
  },
  {
    tree_id: "user_1",
    pId: "vpp_1",
    name: "用户（xx公司）",
    type: "user",
    open: true,
    isParent: true,
    iconSkin: "user-icon"
  },
  {
    tree_id: "resource_1",
    pId: "user_1",
    name: "资源",
    type: "resource",
    open: true,
    isParent: true,
    iconSkin: "resource-icon"
  },
  {
    tree_id: "site_1",
    pId: "resource_1",
    name: "站点",
    type: "site",
    open: false,
    isParent: true,
    iconSkin: "site-icon"
  },
  {
    tree_id: "device_1",
    pId: "site_1",
    name: "设备1",
    type: "device",
    open: false,
    isParent: false,
    iconSkin: "device-icon"
  }
  // ... 更多节点
];
```

## 样式特性

### 精确匹配 Figma 设计稿

1. **容器样式**

   - 尺寸：312px × 794px
   - 背景：白色
   - 阴影：轻微阴影效果
   - 圆角：4px

2. **搜索框样式**

   - 高度：30px
   - 宽度：280px
   - 内边距：16px
   - 图标：搜索图标，颜色 #A4ADB8

3. **树节点样式**

   - 行高：30px
   - 字体：14px，颜色 #424E5F
   - 选中状态：背景 #F6F8FA，文字 #00B45E
   - 层级缩进：每级 28px

4. **展开/收起图标**

   - 尺寸：8px × 8px
   - 颜色：#A4ADB8
   - 形状：三角形箭头

5. **节点图标**

   - 尺寸：16px × 16px
   - 颜色：#C9CDD4
   - 根据 SVG clipPath 定制不同类型图标

6. **滚动指示器**
   - 位置：右侧，top: 280px
   - 尺寸：12px × 64px
   - 背景：#00B45E
   - 内部箭头：白色;

```

## 样式规范

组件严格遵循 UI 设计规范：

- **间距**：使用 J0-J5 变量（4px-32px）
- **圆角**：使用 Ra/Ra1 变量（4px/8px）
- **颜色**：使用主题变量（ZS、T1-T6、BG1-BG5 等）
- **字体**：14px 正文字体，22px 行高
- **图标**：16x16px SVG 图标

## 技术实现

- 基于 CetGiantTree 组件
- 使用 zTree 数据格式
- 支持大数据量渲染
- 内置搜索过滤功能
- CSS 变量支持主题切换

## 注意事项

1. 确保项目中已正确引入 CetGiantTree 组件
2. 树形数据必须包含 tree_id、pId、name 等必要字段
3. iconSkin 字段用于设置节点图标样式
4. 组件高度需要父容器设置固定高度
```
