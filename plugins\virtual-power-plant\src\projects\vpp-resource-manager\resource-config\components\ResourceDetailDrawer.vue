<template>
  <ElDrawer
    :title="$T('资源详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <div class="resource-detail-content h-full overflow-y-auto">
      <div class="detail-grid">
        <!-- 第一行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("电户号") }}</div>
          <div class="detail-value">
            {{ resourceDetail.electric_account || "914403007825478X0" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("资源名称") }}</div>
          <div class="detail-value">
            {{ resourceDetail.resource_name || "地铁物业管理发展有限公司" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("站点数量") }}</div>
          <div class="detail-value">
            {{ resourceDetail.site_capacity || "8" }}
          </div>
        </div>

        <!-- 第二行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("技术容量") }} (kVA)</div>
          <div class="detail-value">
            {{ resourceDetail.rated_capacity || "5000" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("平台直控") }}</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("资源类型") }}</div>
          <div class="detail-value">--</div>
        </div>

        <!-- 第三行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("响应方式") }}</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("资源状态") }}</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("最大可运行功率") }} (kW)</div>
          <div class="detail-value">--</div>
        </div>

        <!-- 第四行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("最小可运行功率") }} (kW)</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("最大上调速率") }} (kW/min)</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("最大下调速率") }} (kW/min)</div>
          <div class="detail-value">--</div>
        </div>

        <!-- 第五行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("经度") }}</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("纬度") }}</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("联系人") }}</div>
          <div class="detail-value">--</div>
        </div>

        <!-- 第六行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("联系电话") }}</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("区域") }}</div>
          <div class="detail-value">--</div>
        </div>
        <div class="detail-item"></div>

        <!-- 第七行 - 地址占整行 -->
        <div class="detail-item detail-item-full">
          <div class="detail-label">{{ $T("地址") }}</div>
          <div class="detail-value">--</div>
        </div>
      </div>
    </div>
  </ElDrawer>
</template>

<script>
export default {
  name: "ResourceDetailDrawer",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      openDrawer: false,
      resourceDetail: {}
    };
  },
  watch: {
    visibleTrigger_in() {
      this.openDrawer = true;
      this.loadResourceDetail();
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },
  methods: {
    loadResourceDetail() {
      // 加载资源详情数据
      this.resourceDetail = {
        ...this.inputData_in
      };
    },
    getResourceTypeText(type) {
      const typeMap = {
        wind: "风电",
        solar: "光伏",
        storage: "储能",
        load: "负荷"
      };
      return typeMap[type] || type;
    },
    getStatusText(status) {
      const statusMap = {
        online: "在线",
        offline: "离线",
        fault: "故障",
        maintenance: "维护",
        storage: "储能"
      };
      return statusMap[status] || status;
    }
  }
};
</script>

<style scoped>
.resource-detail-content {
  padding: var(--J3);
  background: var(--BG1);
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--J3) var(--J2);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--J1);
}

.detail-item-full {
  grid-column: 1 / -1;
}

.detail-label {
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
}

.detail-value {
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  word-break: break-all;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: var(--J2);
  }

  .detail-item-full {
    grid-column: 1;
  }
}
</style>
