<template>
  <div class="user-management">
    <!-- 摘要卡片区 -->
    <section class="summary-cards">
      <div class="summary-card">
        <div class="summary-content">
          <span class="summary-title">资源（个）</span>
          <span class="summary-value resource-value">
            {{ userStats.resource }}
          </span>
        </div>
      </div>
      <div class="summary-card">
        <div class="summary-content">
          <span class="summary-title">站点（个）</span>
          <span class="summary-value site-value">{{ userStats.site }}</span>
        </div>
      </div>
      <div class="summary-card">
        <div class="summary-content">
          <span class="summary-title">设备（个）</span>
          <span class="summary-value device-value">{{ userStats.device }}</span>
        </div>
      </div>
    </section>
    <!-- 筛选栏 -->
    <section class="filter-bar vpp-filter-bar">
      <el-input
        class="vpp-search-input"
        placeholder="请输入关键字"
        v-model="searchKeyword"
        clearable
        prefix-icon="el-icon-search"
        size="small"
      />
      <div class="vpp-select-group">
        <div class="vpp-select-item">
          <span class="vpp-select-label">资源类型</span>
          <el-select
            v-model="filters.userType"
            placeholder="全部"
            size="small"
            class="w-25"
            clearable
          >
            <el-option
              v-for="item in userTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <div class="vpp-select-item">
          <span class="vpp-select-label">区域</span>
          <el-select
            v-model="filters.region"
            placeholder="省/市/区"
            size="small"
            class="w-30"
            clearable
          >
            <el-option
              v-for="item in regionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="vpp-action-buttons">
        <el-button
          type="danger"
          class="vpp-btn-danger"
          size="small"
          :disabled="selectedResources.length === 0"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
        <el-button
          class="vpp-btn-primary"
          type="primary"
          size="small"
          @click="handleAdd"
        >
          新增资源
        </el-button>
      </div>
    </section>
    <!-- 表格区域 -->
    <section class="table-section">
      <el-table
        :data="tableData"
        border
        highlight-current-row
        ref="userTable"
        style="width: 100%"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="60" align="center">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="electric_account"
          label="电户号"
          min-width="140"
        />
        <el-table-column
          prop="resource_name"
          label="资源名称"
          min-width="120"
        />
        <el-table-column prop="resource_type" label="资源类型" width="100">
          <template slot-scope="scope">
            <span
              :class="['tag', getResourceTypeClass(scope.row.resource_type)]"
            >
              {{ getResourceTypeText(scope.row.resource_type) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="region" label="区域" width="80" />
        <el-table-column
          prop="rated_capacity"
          label="报装容量（kVA）"
          width="120"
          align="center"
        />
        <el-table-column
          prop="platform_type"
          label="平台直控"
          width="80"
          align="center"
        />
        <el-table-column
          prop="site_capacity"
          label="站点数量（个）"
          width="100"
          align="center"
        />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <span
              class="action-link detail-link"
              @click="handleDetail(scope.row)"
            >
              详情
            </span>
            <span class="action-link edit-link" @click="handleEdit(scope.row)">
              编辑
            </span>
            <span
              class="action-link delete-link"
              @click="handleDelete(scope.row)"
            >
              删除
            </span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区域 -->
      <div class="table-footer">
        <span>
          共
          <span class="total-highlight">{{ total }}</span>
          个
        </span>
        <el-pagination
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
        />
      </div>
    </section>

    <!-- 新增资源弹窗 -->
    <AddResourceDialog
      :visible="addResourceDialog.visible"
      @close="onAddResourceDialogClose"
      @save="onAddResourceDialogSave"
    />

    <!-- 编辑资源弹窗 -->
    <EditResourceDialog
      :visible="editResourceDialog.visible"
      :resourceData="editResourceDialog.resourceData"
      @close="onEditResourceDialogClose"
      @save="onEditResourceDialogSave"
    />

    <!-- 资源详情抽屉 -->
    <ResourceDetailDrawer
      :visibleTrigger_in="resourceDetailDialog.visibleTrigger"
      :closeTrigger_in="resourceDetailDialog.closeTrigger"
      :inputData_in="resourceDetailDialog.resourceData"
    />
  </div>
</template>

<script>
import AddResourceDialog from "./AddResourceDialog.vue";
import EditResourceDialog from "./EditResourceDialog.vue";
import ResourceDetailDrawer from "./ResourceDetailDrawer.vue";
import { createResource, getResourcePage } from "@/api/resource-management";
import {
  RESOURCE_TYPE_OPTIONS,
  RESPONSE_MODE_MAP,
  convertApiResourceType,
  convertToApiResourceType,
  getResourceTypeText,
  getResourceTypeClass
} from "@/utils/enums";

export default {
  name: "ResourceManagement",
  components: {
    AddResourceDialog,
    EditResourceDialog,
    ResourceDetailDrawer
  },
  props: {
    vppId: {
      type: Number,
      required: true
    },
    userId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      selectedResources: [], // 选中的资源列表
      userStats: {
        resource: 0,
        site: 0,
        device: 0
      },
      filters: {
        userType: "",
        region: ""
      },
      searchKeyword: "",
      regionOptions: [
        { label: "全部", value: "" },
        { label: "广州", value: 1 },
        { label: "深圳", value: 2 },
        { label: "佛山", value: 3 },
        { label: "东莞", value: 4 },
        { label: "中山", value: 5 },
        { label: "珠海", value: 6 }
      ],
      userTypeOptions: RESOURCE_TYPE_OPTIONS,

      tableData: [],

      currentPage: 1,
      pageSize: 10,
      total: 0,
      jumpPage: 1,
      loading: false,
      tableLoading: false,
      searchTimer: null,
      // 新增资源弹窗
      addResourceDialog: {
        visible: false
      },
      // 编辑资源弹窗
      editResourceDialog: {
        visible: false,
        resourceData: {}
      },
      // 资源详情抽屉
      resourceDetailDialog: {
        visibleTrigger: 0,
        closeTrigger: 0,
        resourceData: {}
      }
    };
  },
  mounted() {
    this.loadResources();
    this.loadStatistics();
  },
  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    "filters.userType": {
      handler() {
        this.loadResources();
      }
    },

    "filters.region": {
      handler() {
        this.loadResources();
      }
    }
  },
  methods: {
    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadResources();
      }, 500);
    },

    // 数据转换：将API返回的资源数据转换为表格显示格式
    transformResourceData(apiResource) {
      const regionOption = this.regionOptions.find(
        option => option.value === apiResource.district
      );

      return {
        id: apiResource.id,
        electric_account: apiResource.electricity_user_numbers || "---",
        resource_name: apiResource.resource_name,
        resource_type: this.getResourceTypeFromApi(apiResource.resource_type),
        region: regionOption ? regionOption.label : "---",
        rated_capacity: apiResource.registered_capacity || 0,
        platform_type: apiResource.platform_direct_control ? "是" : "否",
        site_capacity: apiResource.site_count || 0,
        // 保留原始API数据用于编辑
        originalData: apiResource
      };
    },

    // 将API资源类型转换为组件使用的类型
    getResourceTypeFromApi(apiType) {
      return convertApiResourceType(apiType);
    },

    // 将表单数据转换为API需要的格式
    transformFormDataToApi(formData) {
      // 区域映射（district字段）
      const districtMap = {
        guangzhou: 1,
        shenzhen: 2,
        foshan: 3,
        dongguan: 4,
        zhongshan: 5,
        zhuhai: 6
      };

      return {
        // 必填字段
        electricityUserNumbers: formData.electricityUserNumbers, // 电户号
        type: convertToApiResourceType(formData.resourceType), // 资源类型（1-发电设备，2-储能设备，3-负荷设备，4-微电网资源）
        userId: this.userId, // 所属用户ID（必填）
        vppId: this.vppId, // 所属电厂ID（必填）
        resourceName: formData.resourceName, // 资源名称

        // 可选字段
        resourceType: convertToApiResourceType(formData.resourceType), // 资源类型(发电/储电/用电/微电网)
        registeredCapacity: formData.registeredCapacity
          ? parseFloat(formData.registeredCapacity)
          : null, // 报装容量
        responseMode: RESPONSE_MODE_MAP[formData.responseMode] || null, // 响应方式(自动/手动)
        platformDirectControl: formData.platformDirectControl || null, // 平台直控
        maximumOperablePower: formData.maxRunningPower
          ? parseFloat(formData.maxRunningPower)
          : null, // 最大可运行功率
        minmumOperablePower: formData.minRunningPower
          ? parseFloat(formData.minRunningPower)
          : null, // 最小可运行功率
        maximumUpscalingRate: formData.maxUpRate
          ? parseFloat(formData.maxUpRate)
          : null, // 最大上调速率
        maximumDownwardRate: formData.maxDownRate
          ? parseFloat(formData.maxDownRate)
          : null, // 最大下调速率
        longitude: formData.longitude ? parseFloat(formData.longitude) : null, // 经度
        latitude: formData.latitude ? parseFloat(formData.latitude) : null, // 纬度
        contactPerson: formData.contactPerson || null, // 联系人
        phoneNumber: formData.contactPhone || null, // 联系电话
        district: districtMap[formData.region] || null, // 区域
        address: formData.address || null // 地址
      };
    },

    // 加载资源列表
    async loadResources() {
      this.tableLoading = true;
      try {
        const queryData = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          vppId: this.vppId
        };

        // 添加搜索条件
        if (this.searchKeyword) {
          queryData.name = this.searchKeyword;
        }
        if (this.filters.userType) {
          queryData.type = this.filters.userType;
        }
        if (this.filters.region) {
          queryData.location = this.filters.region;
        }

        const response = await getResourcePage(queryData);

        if (response.code === 0) {
          this.tableData = response.data.records.map(resource =>
            this.transformResourceData(resource)
          );
          this.total = response.data.total;
          this.currentPage = response.data.pageNum;
          this.pageSize = response.data.pageSize;
        } else {
          this.$message.error(response.msg || "加载资源列表失败");
        }
      } catch (error) {
        console.error("加载资源列表失败:", error);
        this.$message.error("加载资源列表失败");
      } finally {
        this.tableLoading = false;
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        // 使用getResourcePage获取当前VPP的所有资源来计算统计
        const response = await getResourcePage({
          pageNum: 1,
          pageSize: 1000, // 使用较大的页面大小来获取所有资源
          vppId: this.vppId
        });

        if (response.code === 0) {
          const resources = response.data.records;
          this.userStats.resource = response.data.total;

          // 计算站点总数
          this.userStats.site = resources.reduce((total, resource) => {
            return total + (resource.site_count || 0);
          }, 0);

          // TODO: 设备数量需要从其他API获取
          // 这里暂时保持为0，后续可以添加相应的API调用
        }
      } catch (error) {
        console.error("加载统计数据失败:", error);
      }
    },
    getResourceTypeClass(type) {
      return getResourceTypeClass(type);
    },
    getResourceTypeText(type) {
      return getResourceTypeText(type);
    },

    handleSelectionChange(selection) {
      this.selectedResources = selection;
    },
    handleBatchDelete() {
      if (this.selectedResources.length === 0) {
        this.$message.warning("请选择要删除的资源");
        return;
      }

      this.$confirm(
        `确定要删除选中的 ${this.selectedResources.length} 个资源吗？`,
        "批量删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          // TODO: 调用后端批量删除接口
          const resourceIds = this.selectedResources.map(
            resource => resource.id
          );
          console.log("批量删除资源IDs:", resourceIds);

          // 模拟删除成功
          this.$message.success(
            `成功删除 ${this.selectedResources.length} 个资源`
          );

          // 清空选中状态
          this.selectedResources = [];

          // TODO: 重新加载数据
          // this.loadResources();
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    handleDetail(row) {
      console.log("查看详情:", row);
      // 设置详情数据并触发抽屉显示
      this.resourceDetailDialog.resourceData = row;
      this.resourceDetailDialog.visibleTrigger = Date.now();
    },
    handleEdit(row) {
      console.log("编辑:", row);
      // 设置资源数据并显示编辑弹窗
      this.editResourceDialog.resourceData = row;
      this.editResourceDialog.visible = true;
    },
    handleDelete(row) {
      console.log("删除:", row);

      // 弹出删除确认对话框
      this.$confirm(
        this.$T(
          "确定要删除资源「{0}」吗？删除后将无法恢复。",
          row.resource_name
        ),
        this.$T("删除确认"),
        {
          confirmButtonText: this.$T("确定"),
          cancelButtonText: this.$T("取消"),
          type: "warning",
          closeOnClickModal: false,
          showClose: false,
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = this.$T("删除中...");

              try {
                // TODO: 调用后端接口删除资源
                // const response = await customApi.deleteResource(row.id);
                // if (response.code !== 0) {
                //   return;
                // }

                // 模拟删除操作
                await new Promise(resolve => setTimeout(resolve, 1500));
                console.log("执行删除操作:", row);

                this.$message.success(this.$T("删除成功"));

                // 刷新表格数据
                // this.loadResources();
              } catch (error) {
                console.error("删除失败:", error);
                this.$message.error(this.$T("删除失败"));
              } finally {
                instance.confirmButtonLoading = false;
              }
            }
            done();
          }
        }
      ).catch(() => {
        this.$message.info(this.$T("已取消删除"));
      });
    },
    handlePageChange(page) {
      this.currentPage = page;
    },
    handlePageSizeChange(size) {
      this.pageSize = size;
    },
    // 新增资源
    handleAdd() {
      this.addResourceDialog.visible = true;
    },
    // 新增资源弹窗关闭
    onAddResourceDialogClose() {
      this.addResourceDialog.visible = false;
    },
    // 新增资源弹窗保存
    async onAddResourceDialogSave(resourceData) {
      try {
        // 验证必要的参数
        if (!this.vppId) {
          this.$message.error(this.$T("缺少VPP ID，无法创建资源"));
          return;
        }
        if (!this.userId) {
          this.$message.error(this.$T("请先选择用户节点，然后再创建资源"));
          return;
        }

        // 转换表单数据为API需要的格式
        const apiData = this.transformFormDataToApi(resourceData);

        // 调用后端接口保存资源数据
        const response = await createResource(apiData);

        if (response.code === 0) {
          // 1. 关闭弹窗
          this.addResourceDialog.visible = false;

          // 2. 刷新表格数据和统计信息
          await this.loadResources();
          await this.loadStatistics();

          // 3. 显示成功消息
          this.$message.success(this.$T("资源添加成功"));
        } else {
          this.$message.error(response.msg || this.$T("资源添加失败"));
        }
      } catch (error) {
        console.error("保存资源数据失败:", error);
        this.$message.error(this.$T("资源添加失败，请稍后重试"));
      }
    },
    // 编辑资源弹窗关闭
    onEditResourceDialogClose() {
      this.editResourceDialog.visible = false;
    },
    // 编辑资源弹窗保存
    onEditResourceDialogSave(resourceData) {
      // TODO: 调用后端接口保存编辑的资源数据
      console.log("保存编辑的资源数据:", resourceData);

      // 模拟保存成功后的操作
      // 1. 关闭弹窗
      this.editResourceDialog.visible = false;

      // 2. 刷新表格数据
      // this.loadResources();

      // 3. 显示成功消息
      this.$message.success(this.$T("资源编辑成功"));
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>

<style scoped>
.user-management {
  background: var(--BG1);
  border-radius: var(--Ra);
  padding: var(--J2) var(--J3) var(--J3) var(--J3);
}

.summary-cards {
  display: flex;
  gap: var(--J1);
  margin-bottom: var(--J2);
}

.summary-card {
  flex: 1;
  background: var(--BG1);
  border-radius: var(--Ra1);
  padding: var(--J1) var(--J2);
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: var(--J0);
}

.summary-title {
  font-size: var(--Ab);
  color: var(--T3);
  font-weight: 400;
  line-height: var(--J2);
}

.summary-value {
  font-size: var(--Aa);
  font-weight: 500;
  line-height: var(--J2);
}

.resource-value {
  color: var(--Sta2);
}

.site-value {
  color: var(--F2);
}

.device-value {
  color: var(--Sta1);
}
.filter-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--J2);
  margin-bottom: var(--J2);
}

.vpp-search-input {
  width: 240px;
  height: 32px;
}

.vpp-search-input .el-input__inner {
  height: 32px;
  line-height: 32px;
  padding: 1px 1px 1px var(--J1);
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  font-size: var(--Aa);
  color: var(--T1);
  box-shadow: none;
}

.vpp-search-input .el-input__inner::placeholder {
  color: var(--T4);
  font-size: var(--Aa);
}

.vpp-search-input .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-search-input .el-input__prefix {
  left: var(--J1);
}

.vpp-search-input .el-input__prefix .el-input__icon {
  color: var(--T4);
  font-size: var(--J2);
  line-height: 32px;
}
.vpp-select-group {
  display: flex;
  gap: var(--J2);
  flex: 1;
}

.vpp-select-item {
  display: flex;
  align-items: center;
  gap: var(--J1);
  min-width: 120px;
}

.vpp-select-label {
  color: var(--ZS);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  white-space: nowrap;
}

.vpp-select-item .el-select .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  height: 32px;
  line-height: 32px;
}

.vpp-select-item .el-select .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-select-item .el-select .el-input__suffix {
  right: 0;
}

.vpp-select-item .el-select .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}
.vpp-action-buttons {
  display: flex;
  align-items: center;
  gap: var(--J2);
}

.vpp-btn-outline {
  height: 32px;
  padding: 0 var(--J2);
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}

.vpp-btn-primary {
  height: 32px;
  padding: 0 var(--J2);
  border: none;
  border-radius: var(--Ra);
  background: var(--ZS);
  color: var(--T5);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}
.table-section {
  background: var(--BG);
  border-radius: var(--Ra);
  /* padding: var(--J3); */
}

.table-section .el-table {
  background: transparent;
  border: none;
}

.table-section .el-table th {
  background: var(--BG);
  border-bottom: 1px solid var(--B2);
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  padding: var(--J1) var(--J3);
}

.table-section .el-table td {
  border-bottom: 1px solid var(--B2);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding: var(--J1) var(--J3);
}
.tag {
  display: inline-block;
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 400;
  text-align: center;
}

/* 资源类型标签 */
.tag-generation {
  background: var(--BG4);
  color: var(--F1);
  border: 1px solid var(--F1);
}

.tag-storage {
  background: var(--BG4);
  color: var(--Sta1);
  border: 1px solid var(--Sta1);
}

.tag-load {
  background: var(--BG4);
  color: var(--F2);
  border: 1px solid var(--F2);
}

.tag-microgrid {
  background: var(--BG4);
  color: var(--Sta2);
  border: 1px solid var(--Sta2);
}

.tag-default {
  background: var(--BG);
  color: var(--T3);
  border: 1px solid var(--B1);
}
.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--J2);
  margin-top: var(--J2);
  font-size: var(--Aa);
  color: var(--T1);
}

.total-highlight {
  color: var(--ZS);
  font-weight: 500;
}

.table-footer .el-pagination {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.table-footer .el-pagination .el-pager li {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  margin: 0;
}

.table-footer .el-pagination .el-pager li.active {
  background: var(--BG2);
  color: var(--T1);
  border-color: var(--B2);
}

.table-footer .el-pagination .btn-prev,
.table-footer .el-pagination .btn-next {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
}

.table-footer .el-pagination .el-pagination__jump {
  margin-left: var(--J2);
  color: var(--T1);
  font-size: var(--Aa);
}

.table-footer .el-pagination .el-pagination__jump .el-input__inner {
  width: 48px;
  height: 32px;
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  text-align: center;
}

/* 批量删除按钮样式 */
.vpp-btn-danger {
  background: var(--Sta3);
  border-color: var(--Sta3);
  color: var(--T5);
}

.vpp-btn-danger:hover {
  background: var(--Sta3);
  border-color: var(--Sta3);
  opacity: 0.8;
}

.vpp-btn-danger:disabled {
  background: var(--B2);
  border-color: var(--B2);
  color: var(--T3);
  cursor: not-allowed;
}

.vpp-btn-danger:disabled:hover {
  background: var(--B2);
  border-color: var(--B2);
  opacity: 1;
}

/* 操作链接样式 */
.action-link {
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}
@media (max-width: 900px) {
  .filter-bar {
    flex-direction: column;
    gap: var(--J1);
    align-items: stretch;
  }

  .vpp-search-input {
    width: 100%;
  }

  .vpp-select-group {
    flex-direction: column;
    gap: var(--J1);
  }

  .vpp-select-item {
    min-width: auto;
    width: 100%;
  }

  .vpp-action-buttons {
    gap: var(--J1);
    flex-wrap: wrap;
  }

  .summary-cards {
    flex-direction: column;
    gap: var(--J1);
  }

  .table-footer {
    flex-direction: column;
    gap: var(--J1);
    align-items: center;
  }
}
</style>
