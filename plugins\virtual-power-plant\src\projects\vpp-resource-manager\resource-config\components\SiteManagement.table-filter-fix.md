# SiteManagement 表格过滤问题修复

## 问题分析

从调试日志发现的问题：
```
📊 处理后的站点数据: [{…}, __ob__: Observer] // 有数据
📊 currentPageData计算结果: []                // 但计算结果为空
```

## 根本原因

### 1. **API查询参数格式错误**
```javascript
// 问题：传递tree_id格式给API
queryData.resourceId = "resource_1"; // ❌ 字符串格式

// 修复：提取数字ID
queryData.resourceId = 1; // ✅ 数字格式
```

### 2. **过滤逻辑ID格式不匹配**
```javascript
// 问题：tree_id与数字ID比较
item.resourceId === "resource_1" // ❌ 数字 vs 字符串

// 修复：统一为数字格式比较
item.resourceId === 1 // ✅ 数字 vs 数字
```

## 修复内容

### 1. **API查询参数修复**
```javascript
// 修复前
if (this.resourceId) {
  queryData.resourceId = this.resourceId; // "resource_1"
}

// 修复后
if (this.resourceId) {
  const extractedResourceId = typeof this.resourceId === 'string' && 
    this.resourceId.startsWith('resource_') 
    ? Number(this.resourceId.replace('resource_', ''))
    : Number(this.resourceId);
  queryData.resourceId = extractedResourceId; // 1
}
```

### 2. **过滤逻辑修复**
```javascript
// 修复前
case "resource":
  filtered = filtered.filter(item => item.resourceId === nodeId); // "resource_1"

// 修复后
case "resource":
  const resourceId = originalId || 
    (typeof nodeId === 'string' && nodeId.startsWith('resource_') ? 
      Number(nodeId.replace('resource_', '')) : nodeId);
  filtered = filtered.filter(item => item.resourceId === resourceId); // 1
```

### 3. **添加详细调试日志**
```javascript
console.log("🔍 API查询参数resourceId:", extractedResourceId, "原始值:", this.resourceId);
console.log("🔍 过滤站点数据:", { nodeType, nodeId, originalId, allSitesCount });
console.log("🔍 按资源ID过滤:", resourceId, "站点数据resourceId:", this.allSites.map(s => s.resourceId));
console.log("🔍 过滤后的站点数据:", filtered.length, "条");
```

## 数据流修复

### 修复前 ❌
```
选中资源节点 → tree_id: "resource_1" → API查询: resourceId="resource_1" → 
API返回: resourceId=null → 过滤: null === "resource_1" → 结果: []
```

### 修复后 ✅
```
选中资源节点 → tree_id: "resource_1" → 提取ID: 1 → API查询: resourceId=1 → 
API返回: resourceId=1 → 过滤: 1 === 1 → 结果: [站点数据]
```

## 测试验证

### 第1步：检查API查询参数
控制台应该显示：
```
🔍 API查询参数resourceId: 1 原始值: resource_1
```

### 第2步：检查API返回数据
原始站点数据中应该有正确的resourceId：
```
📋 第一条原始站点数据: {id: 1, resourceId: 1, siteName: '1234', ...}
```

### 第3步：检查过滤逻辑
控制台应该显示：
```
🔍 过滤站点数据: {nodeType: "resource", nodeId: "resource_1", originalId: 1, allSitesCount: 1}
🔍 按资源ID过滤: 1 站点数据resourceId: [1]
🔍 过滤后的站点数据: 1 条
```

### 第4步：检查最终结果
```
📊 currentPageData计算结果: [{siteName: "1234", siteType: 1, ...}]
```

## 兼容性处理

修复后的代码能够处理多种ID格式：

### tree_id格式
```javascript
"resource_1" → 1
"user_123" → 123
"site_456" → 456
```

### 原始ID格式
```javascript
1 → 1
"123" → 123
```

### originalId优先
```javascript
// 如果节点有originalId，优先使用
const resourceId = originalId || extractFromTreeId(nodeId);
```

## 其他节点类型

修复也适用于其他节点类型：

```javascript
case "user":
  const userId = originalId || extractUserId(nodeId);
  filtered = filtered.filter(item => item.userId === userId);

case "site":
  const siteId = originalId || extractSiteId(nodeId);
  filtered = filtered.filter(item => item.id === siteId);
```

## 预期结果

修复后，当选中资源节点时：
1. ✅ API查询使用正确的数字resourceId
2. ✅ API返回该资源下的站点数据
3. ✅ 过滤逻辑正确匹配数据
4. ✅ 表格显示站点列表
5. ✅ 分页功能正常工作

现在表格应该能正确显示选中资源下的站点数据了！
