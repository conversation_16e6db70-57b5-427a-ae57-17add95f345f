<template>
  <div class="site-management-test bg-BG">
    <div class="test-header bg-BG1 rounded-lg p-J3 mb-J3">
      <h2 class="text-T1 font-medium mb-J2">{{ $T("站点管理交互测试") }}</h2>
      <p class="text-T2 mb-J3">
        {{ $T("测试左侧树选择资源节点，右侧显示站点管理，点击新增按钮能识别资源类型") }}
      </p>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content bg-BG1 rounded-lg p-J3">
      <div class="content-layout">
        <!-- 左侧树形区域 -->
        <div class="tree-section">
          <h3 class="text-T1 font-medium mb-J2">{{ $T("虚拟电厂树") }}</h3>
          <VppTree @node-click="handleNodeClick" />
        </div>
        
        <!-- 右侧站点管理区域 -->
        <div class="management-section">
          <div v-if="!selectedNode" class="empty-state text-center p-J4">
            <div class="text-T3 text-lg mb-J2">{{ $T("请选择左侧树节点") }}</div>
            <div class="text-T2">{{ $T("选择资源节点后可以管理该资源下的站点") }}</div>
          </div>
          
          <div v-else-if="selectedNode.type !== 'resource'" class="empty-state text-center p-J4">
            <div class="text-T3 text-lg mb-J2">{{ $T("请选择资源节点") }}</div>
            <div class="text-T2">{{ $T("当前选中的是") }}: {{ selectedNode.name }} ({{ $T(getNodeTypeLabel(selectedNode.type)) }})</div>
            <div class="text-T2">{{ $T("只有选择资源节点才能管理站点") }}</div>
          </div>
          
          <div v-else class="resource-management">
            <div class="resource-info bg-BG2 rounded p-J2 mb-J3">
              <h4 class="text-T1 font-medium mb-J1">{{ $T("当前资源信息") }}</h4>
              <div class="text-T2">
                <span class="font-medium">{{ $T("资源名称") }}:</span> {{ selectedNode.name }}
              </div>
              <div class="text-T2">
                <span class="font-medium">{{ $T("资源类型") }}:</span> {{ $T(getResourceTypeLabel(selectedNode.resourceType)) }}
              </div>
              <div class="text-T2">
                <span class="font-medium">{{ $T("资源类别") }}:</span> {{ $T(getResourceCategoryLabel(selectedNode.resourceCategory)) }}
              </div>
            </div>
            
            <!-- 站点管理操作区 -->
            <div class="management-actions mb-J3">
              <el-button type="primary" @click="handleAddSite">
                {{ $T("新增站点") }}
              </el-button>
              <el-button @click="clearLog">
                {{ $T("清空日志") }}
              </el-button>
            </div>
            
            <!-- 模拟站点列表 -->
            <div class="site-list">
              <h4 class="text-T1 font-medium mb-J2">{{ $T("站点列表") }}</h4>
              <div class="text-T3">{{ $T("这里会显示该资源下的站点列表...") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作日志 -->
    <div class="log-section bg-BG1 rounded-lg p-J3 mt-J3">
      <h3 class="text-T1 font-medium mb-J2">{{ $T("操作日志") }}</h3>
      <div class="log-container bg-BG2 rounded p-J2" style="height: 200px; overflow-y: auto;">
        <div
          v-for="(log, index) in logs"
          :key="index"
          class="log-item text-T2 mb-J1"
          :class="log.type"
        >
          <span class="timestamp text-T3">[{{ log.timestamp }}]</span>
          <span class="message">{{ log.message }}</span>
          <pre v-if="log.data" class="data mt-J1 text-T3">{{ JSON.stringify(log.data, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 新增站点弹窗 -->
    <AddSiteDialog
      :visible="addSiteDialogVisible"
      :resourceId="currentResourceId"
      :resourceInfo="currentResourceInfo"
      @close="handleAddSiteDialogClose"
      @save="handleAddSiteDialogSave"
    />
  </div>
</template>

<script>
import VppTree from "./VppTree.vue";
import AddSiteDialog from "./AddSiteDialog.vue";

export default {
  name: "SiteManagementTest",
  components: {
    VppTree,
    AddSiteDialog
  },
  data() {
    return {
      selectedNode: null,
      addSiteDialogVisible: false,
      currentResourceId: null,
      currentResourceInfo: null,
      logs: []
    };
  },
  mounted() {
    this.addLog("info", "站点管理测试页面已加载");
  },
  methods: {
    // 处理树节点点击
    handleNodeClick(node) {
      this.selectedNode = node;
      this.addLog("info", `选中节点: ${node.name} (${node.type})`, node);
    },

    // 新增站点
    handleAddSite() {
      if (this.selectedNode && this.selectedNode.type === "resource") {
        this.currentResourceId = this.selectedNode.tree_id;
        this.currentResourceInfo = {
          id: this.selectedNode.tree_id,
          name: this.selectedNode.name,
          type: this.selectedNode.resourceType,
          category: this.selectedNode.resourceCategory
        };
        
        this.addLog("success", "打开新增站点弹窗", this.currentResourceInfo);
        this.addSiteDialogVisible = true;
      } else {
        this.addLog("warning", "请先选择一个资源节点");
      }
    },

    // 弹窗关闭处理
    handleAddSiteDialogClose() {
      this.addLog("info", "新增站点弹窗已关闭");
      this.addSiteDialogVisible = false;
      this.currentResourceId = null;
      this.currentResourceInfo = null;
    },

    // 弹窗保存处理
    handleAddSiteDialogSave(siteData) {
      this.addLog("success", "站点数据保存成功", siteData);
      this.addSiteDialogVisible = false;
      this.currentResourceId = null;
      this.currentResourceInfo = null;
    },

    // 获取节点类型标签
    getNodeTypeLabel(nodeType) {
      const typeMap = {
        vpp: "虚拟电厂",
        user: "用户",
        resource: "资源",
        site: "站点",
        device: "设备"
      };
      return typeMap[nodeType] || nodeType;
    },

    // 获取资源类型标签
    getResourceTypeLabel(resourceType) {
      const typeMap = {
        generation: "发电",
        storage: "储电",
        consumption: "用电"
      };
      return typeMap[resourceType] || resourceType;
    },

    // 获取资源类别标签
    getResourceCategoryLabel(resourceCategory) {
      const categoryMap = {
        energy_storage: "储能",
        solar_power: "光伏",
        wind_power: "风电"
      };
      return categoryMap[resourceCategory] || resourceCategory;
    },

    // 添加日志
    addLog(type, message, data = null) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.push({
        type,
        timestamp,
        message,
        data
      });
      
      // 自动滚动到底部
      this.$nextTick(() => {
        const container = this.$el.querySelector('.log-container');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },

    // 清空日志
    clearLog() {
      this.logs = [];
      this.addLog("info", "日志已清空");
    }
  }
};
</script>

<style scoped>
.site-management-test {
  min-height: 100vh;
  padding: 16px;
}

.content-layout {
  display: flex;
  gap: 24px;
  min-height: 600px;
}

.tree-section {
  flex-shrink: 0;
  width: 312px;
}

.management-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.resource-management {
  flex: 1;
}

.log-item {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  padding: 4px 0;
  border-bottom: 1px solid var(--BG3);
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.info .message {
  color: var(--T1);
}

.log-item.success .message {
  color: var(--Sta1);
}

.log-item.error .message {
  color: var(--Sta3);
}

.log-item.warning .message {
  color: var(--Sta2);
}

.timestamp {
  font-weight: bold;
  margin-right: 8px;
}

.data {
  background: var(--BG3);
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: pre-wrap;
  word-break: break-all;
}

@media (max-width: 1200px) {
  .content-layout {
    flex-direction: column;
    gap: 16px;
  }
  
  .tree-section {
    width: 100%;
  }
}
</style>
