<template>
  <ElDrawer
    :title="$T('用户详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="640px"
  >
    <div class="user-detail-content h-full overflow-y-auto">
      <!-- 用户详情 -->
      <div class="detail-section">
        <div class="section-title text-H3 font-bold mb-J3 text-T1">
          {{ $T("用户详情") }}
        </div>
        <el-row :gutter="16">
          <el-col :span="12" class="mb-J3">
            <div class="detail-item">
              <div class="detail-label text-T3 mb-J1">{{ $T("用户名称") }}</div>
              <div class="detail-value text-T1">
                {{ userDetail.name || "--" }}
              </div>
            </div>
          </el-col>
          <el-col :span="12" class="mb-J3">
            <div class="detail-item">
              <div class="detail-label text-T3 mb-J1">{{ $T("联系人") }}</div>
              <div class="detail-value text-T1">
                {{ userDetail.contact || "--" }}
              </div>
            </div>
          </el-col>
          <el-col :span="12" class="mb-J3">
            <div class="detail-item">
              <div class="detail-label text-T3 mb-J1">{{ $T("联系电话") }}</div>
              <div class="detail-value text-T1">
                {{ userDetail.phone || "--" }}
              </div>
            </div>
          </el-col>
          <el-col :span="12" class="mb-J3">
            <div class="detail-item">
              <div class="detail-label text-T3 mb-J1">{{ $T("资源数量") }}</div>
              <div class="detail-value text-T1">
                {{ userDetail.count || 0 }}
              </div>
            </div>
          </el-col>
          <el-col :span="12" class="mb-J3">
            <div class="detail-item">
              <div class="detail-label text-T3 mb-J1">{{ $T("区域") }}</div>
              <div class="detail-value text-T1">
                {{ userDetail.area || "--" }}
              </div>
            </div>
          </el-col>
          <el-col :span="12" class="mb-J3">
            <div class="detail-item">
              <div class="detail-label text-T3 mb-J1">{{ $T("地址") }}</div>
              <div class="detail-value text-T1">
                {{ userDetail.address || "--" }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </ElDrawer>
</template>

<script>
export default {
  name: "UserDetailDrawer",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      openDrawer: false,
      userDetail: {}
    };
  },
  watch: {
    visibleTrigger_in() {
      this.openDrawer = true;
      this.loadUserDetail();
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },
  methods: {
    loadUserDetail() {
      // 直接使用传入的用户详情数据
      this.userDetail = {
        ...this.inputData_in
      };
    }
  }
};
</script>

<style scoped>
.user-detail-content {
  padding: var(--J3);
}

.detail-section {
  border-bottom: 1px solid var(--B2);
  padding-bottom: var(--J3);
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  position: relative;
  padding-left: var(--J2);
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: var(--ZS);
  border-radius: 2px;
}

.detail-item {
  min-height: 48px;
}

.detail-label {
  font-size: var(--Ab);
  line-height: 1.4;
}

.detail-value {
  font-size: var(--Aa);
  line-height: 1.4;
  word-break: break-all;
}
</style>
