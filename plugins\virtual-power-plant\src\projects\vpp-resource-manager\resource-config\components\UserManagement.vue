<template>
  <div class="vpp-management bg-BG1 rounded-Ra p-J3">
    <!-- 摘要卡片区 -->
    <section class="summary-cards flex gap-J2 mb-J3">
      <div class="summary-card flex-1 rounded-Ra1 p-J2">
        <div class="summary-content flex flex-col gap-J0">
          <span class="summary-title text-Ab text-T3 font-normal leading-4">
            {{ $T("用户（个）") }}
          </span>
          <span
            class="summary-value user-value text-Aa font-medium leading-4 text-ZS"
          >
            {{ statCards.user }}
          </span>
        </div>
      </div>
      <div class="summary-card flex-1 rounded-Ra1 p-J2">
        <div class="summary-content flex flex-col gap-J0">
          <span class="summary-title text-Ab text-T3 font-normal leading-4">
            {{ $T("资源（个）") }}
          </span>
          <span
            class="summary-value resource-value text-Aa font-medium leading-4 text-Sta2"
          >
            {{ statCards.resource }}
          </span>
        </div>
      </div>
      <div class="summary-card flex-1 rounded-Ra1 p-J2">
        <div class="summary-content flex flex-col gap-J0">
          <span class="summary-title text-Ab text-T3 font-normal leading-4">
            {{ $T("站点（个）") }}
          </span>
          <span
            class="summary-value site-value text-Aa font-medium leading-4 text-F2"
          >
            {{ statCards.site }}
          </span>
        </div>
      </div>
      <div class="summary-card flex-1 rounded-Ra1 p-J2">
        <div class="summary-content flex flex-col gap-J0">
          <span class="summary-title text-Ab text-T3 font-normal leading-4">
            {{ $T("设备（个）") }}
          </span>
          <span
            class="summary-value device-value text-Aa font-medium leading-4 text-Sta1"
          >
            {{ statCards.device }}
          </span>
        </div>
      </div>
    </section>

    <!-- 筛选栏 -->
    <section
      class="filter-bar vpp-filter-bar flex items-center justify-between mb-J3 py-J2"
    >
      <el-input
        class="vpp-search-input w-48"
        :placeholder="$T('请输入关键字')"
        v-model="search.keyword"
        size="small"
        clearable
        prefix-icon="el-icon-search"
      />
      <div class="vpp-filter-group flex items-center gap-J3 flex-1 ml-J3">
        <div class="vpp-select-group flex items-center gap-J2">
          <span class="vpp-select-label text-Aa text-T2 whitespace-nowrap">
            {{ $T("区域") }}
          </span>
          <el-select
            v-model="search.region"
            :placeholder="$T('请选择区域')"
            size="small"
            style="width: 120px"
            clearable
          >
            <el-option
              v-for="item in regionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="vpp-action-buttons flex gap-J2">
        <el-button
          type="danger"
          class="vpp-btn-danger"
          size="small"
          :disabled="selectedUsers.length === 0"
          @click="onBatchDelete"
        >
          {{ $T("批量删除") }}
        </el-button>
        <el-button
          class="vpp-btn-primary"
          type="primary"
          size="small"
          @click="onAddUser"
        >
          {{ $T("新增代理用户") }}
        </el-button>
      </div>
    </section>

    <!-- 表格区域 -->
    <section class="table-section bg-BG1 rounded-Ra">
      <el-table
        :data="tableData"
        border
        highlight-current-row
        ref="vppTable"
        style="width: 100%"
        v-loading="tableLoading"
        @selection-change="onSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column :label="$T('序号')" width="60" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$T('用户名称')" />
        <el-table-column prop="area" :label="$T('区域')" />
        <el-table-column prop="count" :label="$T('资源数量（个）')" />
        <el-table-column :label="$T('操作')" width="180" align="center">
          <template slot-scope="scope">
            <span class="action-link detail-link" @click="onDetail(scope.row)">
              {{ $T("详情") }}
            </span>
            <span class="action-link edit-link" @click="onEdit(scope.row)">
              {{ $T("编辑") }}
            </span>
            <span class="action-link delete-link" @click="onDelete(scope.row)">
              {{ $T("删除") }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="pagination-wrapper flex justify-end mt-J3 py-J3">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          :page-size="pagination.size"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 30, 50]"
          @size-change="onPageSizeChange"
          @current-change="onPageChange"
        />
      </div>
    </section>

    <!-- 新增代理用户弹窗 -->
    <AddUserDialog
      :visible="addUserDialog.visible"
      :regionOptions="regionOptions"
      :cityOptions="cityOptions"
      :districtOptions="districtOptions"
      @close="onAddUserDialogClose"
      @save="onAddUserDialogSave"
    />

    <!-- 用户详情弹窗 -->
    <UserDetailDrawer
      :visibleTrigger_in="userDetailDialog.visibleTrigger"
      :closeTrigger_in="userDetailDialog.closeTrigger"
      :inputData_in="userDetailDialog.userData"
    />

    <!-- 用户编辑弹窗 -->
    <UserEditDialog
      :visible="userEditDialog.visible"
      :userData="userEditDialog.userData"
      @close="onUserEditDialogClose"
      @save="onUserEditDialogSave"
    />
  </div>
</template>

<script>
import AddUserDialog from "./AddUserDialog.vue";
import UserDetailDrawer from "./UserDetailDrawer.vue";
import UserEditDialog from "./UserEditDialog.vue";
import {
  createUser,
  getUserPage,
  updateUser,
  deleteUser
} from "@/api/user-management";

export default {
  name: "VppManagement",
  components: {
    AddUserDialog,
    UserDetailDrawer,
    UserEditDialog
  },
  props: {
    node: {
      type: Object,
      default: () => null
    },
    vppId: {
      type: Number,
      default: null
    },
    userId: {
      type: Number,
      default: null
    },
    resourceId: {
      type: Number,
      default: null
    },
    siteId: {
      type: Number,
      default: null
    },
    roomId: {
      type: Number,
      default: null
    },
    siteType: {
      type: Number,
      default: null
    },
    resourceType: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      selectedUsers: [], // 选中的用户列表
      statCards: {
        user: 0,
        resource: 0,
        site: 0,
        device: 0
      },
      search: { keyword: "", region: "" },
      regionOptions: [
        { label: this.$T("全部"), value: "" },
        { label: this.$T("华南地区"), value: 1 },
        { label: this.$T("华东地区"), value: 2 },
        { label: this.$T("华北地区"), value: 3 },
        { label: this.$T("华中地区"), value: 4 },
        { label: this.$T("西南地区"), value: 5 },
        { label: this.$T("西北地区"), value: 6 },
        { label: this.$T("东北地区"), value: 7 }
      ],
      cityOptions: [
        { label: this.$T("广州市"), value: 1 },
        { label: this.$T("深圳市"), value: 2 },
        { label: this.$T("佛山市"), value: 3 },
        { label: this.$T("东莞市"), value: 4 },
        { label: this.$T("中山市"), value: 5 },
        { label: this.$T("珠海市"), value: 6 },
        { label: this.$T("惠州市"), value: 7 },
        { label: this.$T("江门市"), value: 8 },
        { label: this.$T("肇庆市"), value: 9 }
      ],
      districtOptions: [
        { label: this.$T("天河区"), value: 1 },
        { label: this.$T("越秀区"), value: 2 },
        { label: this.$T("海珠区"), value: 3 },
        { label: this.$T("荔湾区"), value: 4 },
        { label: this.$T("白云区"), value: 5 },
        { label: this.$T("黄埔区"), value: 6 },
        { label: this.$T("番禺区"), value: 7 },
        { label: this.$T("花都区"), value: 8 },
        { label: this.$T("南沙区"), value: 9 },
        { label: this.$T("从化区"), value: 10 },
        { label: this.$T("增城区"), value: 11 }
      ],
      tableData: [],
      pagination: { total: 0, page: 1, size: 10 },
      loading: false,
      tableLoading: false,
      addUserDialog: {
        visible: false
      },
      userDetailDialog: {
        visibleTrigger: 0,
        closeTrigger: 0,
        userData: {}
      },
      userEditDialog: {
        visible: false,
        userData: {}
      },
      searchTimer: null // 搜索防抖定时器
    };
  },
  computed: {
    // 获取当前VPP ID
    currentVppId() {
      return this.vppId;
    }
  },

  watch: {
    vppId: {
      handler(newVppId, oldVppId) {
        console.log("🔔 UserManagement - VPP ID watch triggered:", {
          from: oldVppId,
          to: newVppId,
          node: this.node?.type,
          nodeId: this.node?.originalId,
          immediate: oldVppId === undefined
        });
        this.loadData();
      },
      immediate: true
    }
  },
  mounted() {
    console.log("UserManagement mounted with props:", {
      vppId: this.vppId,
      node: this.node,
      nodeType: this.node?.type,
      nodeId: this.node?.originalId,
      roomId: this.roomId,
      siteType: this.siteType,
      resourceType: this.resourceType
    });

    // 手动触发一次数据加载，以防watch没有正确触发
    console.log("🚀 Manually triggering loadData from mounted");
    this.loadData();
  },
  watch: {
    "search.keyword": {
      handler() {
        this.debounceSearch();
      }
    },
    "search.region": {
      handler() {
        this.loadUsers();
      }
    }
  },
  methods: {
    // 统一的数据加载方法
    loadData() {
      console.log("🔄 UserManagement.loadData() called with props:", {
        vppId: this.vppId,
        currentVppId: this.currentVppId,
        node: this.node?.type,
        nodeId: this.node?.originalId
      });

      if (this.currentVppId) {
        console.log("✅ Loading data for VPP ID:", this.currentVppId);
        this.loadUsers();
        this.loadStatistics();
      } else {
        console.log("❌ No VPP ID, clearing data");
        this.tableData = [];
        this.statCards = { user: 0, resource: 0, site: 0, device: 0 };
      }
    },

    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.pagination.page = 1;
        this.loadUsers();
      }, 500);
    },

    // 数据转换：将API返回的用户数据转换为表格显示格式
    transformUserData(apiUser) {
      const regionOption = this.regionOptions.find(
        option => option.value === apiUser.region
      );
      return {
        id: apiUser.id,
        name: apiUser.user_name,
        area: regionOption ? regionOption.label : this.$T("未知区域"),
        count: apiUser.resource_count || 0,
        // 为了兼容 UserDetailDrawer 组件，同时提供两种字段名
        contact: apiUser.contact_person,
        contactPerson: apiUser.contact_person,
        phone: apiUser.phone_number,
        phoneNumber: apiUser.phone_number,
        address: apiUser.address,
        vppId: apiUser.vpp_id,
        region: apiUser.region,
        city: apiUser.city,
        district: apiUser.district,
        createTime: apiUser.create_time,
        updateTime: apiUser.update_time
      };
    },

    // 加载用户列表
    async loadUsers() {
      console.log("📋 UserManagement.loadUsers() called");

      // 检查是否有vppId
      if (!this.currentVppId) {
        console.warn("❌ 没有VPP ID，无法加载用户列表");
        return;
      }

      console.log("🔍 Starting to load users for VPP ID:", this.currentVppId);
      this.tableLoading = true;
      try {
        const queryData = {
          index: this.pagination.page - 1, // API使用0基索引
          limit: this.pagination.size,
          vppId: this.currentVppId
        };

        // 添加搜索条件
        if (this.search.keyword) {
          queryData.userName = this.search.keyword;
        }
        if (this.search.region) {
          queryData.region = this.search.region;
        }

        const response = await getUserPage(queryData);

        if (response.code === 0) {
          this.tableData = response.data.records.map(user =>
            this.transformUserData(user)
          );
          this.pagination.total = response.data.total;
          this.pagination.page = response.data.pageNum;
          this.pagination.size = response.data.pageSize;
        } else {
          this.$message.error(response.msg || this.$T("加载用户列表失败"));
        }
      } catch (error) {
        console.error("加载用户列表失败:", error);
        this.$message.error(this.$T("加载用户列表失败"));
      } finally {
        this.tableLoading = false;
      }
    },

    // 加载统计数据
    async loadStatistics() {
      // 检查是否有vppId
      if (!this.currentVppId) {
        console.warn("没有VPP ID，无法加载统计数据");
        return;
      }

      try {
        // 使用getUserPage获取当前VPP的所有用户来计算统计
        // 设置一个较大的limit来获取所有用户，或者可以分批获取
        const response = await getUserPage({
          index: 0,
          limit: 1000, // 假设单个VPP不会超过1000个用户
          vppId: this.currentVppId
        });

        if (response.code === 0) {
          const users = response.data.records;
          this.statCards.user = response.data.total; // 使用API返回的total

          // 计算资源总数 - 注意使用API返回的原始字段名
          this.statCards.resource = users.reduce((total, user) => {
            return total + (user.resource_count || 0);
          }, 0);

          // TODO: 站点和设备数量需要从其他API获取
          // 这里暂时保持为0，后续可以添加相应的API调用
        }
      } catch (error) {
        console.error("加载统计数据失败:", error);
      }
    },
    onSelectionChange(selection) {
      this.selectedUsers = selection;
    },
    async onBatchDelete() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning(this.$T("请选择要删除的用户"));
        return;
      }

      this.$confirm(
        this.$T(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？`),
        this.$T("批量删除确认"),
        {
          confirmButtonText: this.$T("确定"),
          cancelButtonText: this.$T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          try {
            const userIds = this.selectedUsers.map(user => user.id);

            // 批量删除用户
            const deletePromises = userIds.map(id => deleteUser(id));
            const results = await Promise.allSettled(deletePromises);

            // 检查删除结果
            const successCount = results.filter(
              result => result.status === "fulfilled" && result.value.code === 0
            ).length;

            if (successCount === userIds.length) {
              this.$message.success(this.$T(`成功删除 ${successCount} 个用户`));
            } else {
              this.$message.warning(
                this.$T(
                  `删除了 ${successCount}/${userIds.length} 个用户，部分删除失败`
                )
              );
            }

            // 清空选中状态
            this.selectedUsers = [];
            this.$refs.vppTable.clearSelection();

            // 重新加载数据
            this.loadUsers();
            this.loadStatistics();
          } catch (error) {
            console.error("批量删除失败:", error);
            this.$message.error(this.$T("批量删除失败"));
          }
        })
        .catch(() => {
          this.$message.info(this.$T("已取消删除"));
        });
    },
    onAddUser() {
      this.addUserDialog.visible = true;
    },
    onAddUserDialogClose() {
      this.addUserDialog.visible = false;
    },
    async onAddUserDialogSave(userData) {
      // 检查是否有vppId
      if (!this.currentVppId) {
        this.$message.error(this.$T("请先选择虚拟电厂"));
        return;
      }

      try {
        // 转换数据格式并添加VPP ID到用户数据
        const userDataWithVpp = {
          userName: userData.userName,
          vppId: this.currentVppId,
          phoneNumber: userData.phoneNumber,
          contactPerson: userData.contactPerson,
          address: userData.address,
          region: userData.region,
          resourceCount: userData.resourceCount || 0,
          city: userData.city,
          district: userData.district
        };

        const response = await createUser(userDataWithVpp);

        if (response.code === 0) {
          this.$message.success(this.$T("用户创建成功"));

          // 1. 关闭弹窗
          this.addUserDialog.visible = false;

          // 2. 刷新表格数据
          this.loadUsers();

          // 3. 更新统计卡片
          this.loadStatistics();
        } else {
          this.$message.error(response.msg || this.$T("用户创建失败"));
        }
      } catch (error) {
        console.error("创建用户失败:", error);
        this.$message.error(this.$T("用户创建失败"));
      }
    },
    async onDetail(row) {
      try {
        // 使用getUserPage通过用户ID获取用户详细信息
        const response = await getUserPage({
          index: 0,
          limit: 1,
          userId: row.id // 假设getUserPage支持通过userId查询
        });

        if (response.code === 0 && response.data.records.length > 0) {
          // 转换数据格式并显示详情弹窗
          this.userDetailDialog.userData = this.transformUserData(
            response.data.records[0]
          );
          this.userDetailDialog.visibleTrigger = Date.now();
        } else {
          this.$message.error(this.$T("获取用户详情失败"));
        }
      } catch (error) {
        console.error("获取用户详情失败:", error);
        this.$message.error(this.$T("获取用户详情失败"));
      }
    },
    async onEdit(row) {
      try {
        // 使用getUserPage通过用户ID获取用户信息用于编辑
        const response = await getUserPage({
          index: 0,
          limit: 1,
          userId: row.id // 假设getUserPage支持通过userId查询
        });

        if (response.code === 0 && response.data.records.length > 0) {
          this.userEditDialog.userData = this.transformUserData(
            response.data.records[0]
          );
          this.userEditDialog.visible = true;
        } else {
          this.$message.error(this.$T("获取用户信息失败"));
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
        this.$message.error(this.$T("获取用户信息失败"));
      }
    },
    onUserEditDialogClose() {
      this.userEditDialog.visible = false;
    },
    async onUserEditDialogSave(editedData) {
      try {
        console.log("UserManagement - Saving edited user data:", editedData);

        // 获取原始用户数据，保留不可编辑的字段
        const originalData = this.userEditDialog.userData;

        // 转换数据格式为API需要的格式
        const updateData = {
          userName: originalData.name, // 用户名不可编辑，使用原始数据
          contactPerson: editedData.contact || editedData.contactPerson,
          phoneNumber: editedData.phone || editedData.phoneNumber,
          address: editedData.address,
          vppId: originalData.vppId || this.currentVppId, // 使用原始数据或当前VPP ID
          region: originalData.region,
          city: originalData.city,
          district: originalData.district
        };

        console.log("UserManagement - Update data to API:", updateData);
        const response = await updateUser(editedData.id, updateData);

        if (response.code === 0) {
          this.$message.success(this.$T("用户信息更新成功"));

          // 关闭弹窗
          this.userEditDialog.visible = false;

          // 刷新表格数据
          this.loadUsers();
          this.loadStatistics();
        } else {
          this.$message.error(response.msg || this.$T("用户信息更新失败"));
        }
      } catch (error) {
        console.error("更新用户信息失败:", error);
        this.$message.error(this.$T("用户信息更新失败"));
      }
    },
    async onDelete(row) {
      this.$confirm(this.$T("确认删除该用户？"), this.$T("提示"), {
        confirmButtonText: this.$T("确定"),
        cancelButtonText: this.$T("取消"),
        type: "warning"
      })
        .then(async () => {
          try {
            const response = await deleteUser(row.id);

            if (response.code === 0) {
              this.$message.success(this.$T("删除成功"));

              // 重新加载数据
              this.loadUsers();
              this.loadStatistics();
            } else {
              this.$message.error(response.msg || this.$T("删除失败"));
            }
          } catch (error) {
            console.error("删除用户失败:", error);
            this.$message.error(this.$T("删除失败"));
          }
        })
        .catch(() => {
          this.$message.info(this.$T("已取消删除"));
        });
    },
    onPageSizeChange(size) {
      this.pagination.size = size;
      this.pagination.page = 1; // 重置到第一页
      this.loadUsers();
    },
    onPageChange(page) {
      this.pagination.page = page;
      this.loadUsers();
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>

<style scoped>
/* 摘要卡片样式 */
.summary-card {
  background: var(--BG1);
}

/* 按钮样式优化 */
.vpp-btn-outline {
  border: 1px solid var(--B1);
  color: var(--T2);
  background: var(--BG1);
}

.vpp-btn-outline:hover {
  color: var(--ZS);
  border-color: var(--ZS);
  background-color: var(--BG2);
}

.vpp-btn-primary {
  background: var(--ZS);
  border-color: var(--ZS);
  color: var(--T5);
}

.vpp-btn-primary:hover {
  background: var(--ZS);
  border-color: var(--ZS);
  opacity: 0.8;
}

/* 批量删除按钮样式 */
.vpp-btn-danger {
  background: var(--Sta3);
  border-color: var(--Sta3);
  color: var(--T5);
}

.vpp-btn-danger:hover {
  background: var(--Sta3);
  border-color: var(--Sta3);
  opacity: 0.8;
}

.vpp-btn-danger:disabled {
  background: var(--B2);
  border-color: var(--B2);
  color: var(--T3);
  cursor: not-allowed;
}

.vpp-btn-danger:disabled:hover {
  background: var(--B2);
  border-color: var(--B2);
  opacity: 1;
}

/* 操作链接样式 */
.action-link {
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}
</style>
