<template>
  <div class="device-management-container">
    <div class="device-management">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <div class="search-left">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入设备名称"
            class="search-input"
            clearable
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <el-select
            v-model="selectedCategory"
            placeholder="全部"
            class="category-select"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="储能设备" value="储能设备"></el-option>
            <el-option label="光伏设备" value="光伏设备"></el-option>
            <el-option label="风电设备" value="风电设备"></el-option>
          </el-select>
        </div>
        <div class="search-right">
          <el-button
            type="danger"
            class="batch-delete-btn"
            :disabled="selectedDevices.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
          <el-button type="primary" class="add-device-btn" @click="handleAdd">
            新建
          </el-button>
        </div>
      </div>

      <!-- 设备表格 -->
      <el-table
        :data="filteredDevices"
        border
        class="device-table"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="serialNumber" label="序号" width="80" />
        <el-table-column prop="deviceName" label="设备名称" />
        <el-table-column prop="deviceType" label="设备类型" />
        <el-table-column prop="ratedPower" label="额定功率（kW）" />
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <span
              class="action-link detail-link"
              @click="handleDetail(scope.row)"
            >
              详情
            </span>
            <span class="action-link edit-link" @click="handleEdit(scope.row)">
              编辑
            </span>
            <span
              class="action-link delete-link"
              @click="handleDelete(scope.row)"
            >
              删除
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <span class="total-info">共{{ totalDevices }}条</span>
        <el-pagination
          background
          layout="sizes, prev, pager, next, jumper"
          :total="totalDevices"
          :page-size="pageSize"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          class="custom-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增设备弹窗 -->
    <el-dialog
      :title="$T('新增设备')"
      :visible.sync="addDeviceDialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="add-device-dialog"
      @close="handleDialogClose"
    >
      <div class="dialog-content">
        <el-form
          ref="addDeviceForm"
          :model="addDeviceFormData"
          :rules="addDeviceFormRules"
          label-position="top"
          class="device-form"
        >
          <el-row :gutter="30">
            <el-col :span="12">
              <!-- 设备名称 -->
              <el-form-item :label="$T('设备名称')" prop="deviceName" required>
                <el-input
                  v-model="addDeviceFormData.deviceName"
                  :placeholder="$T('请输入内容')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 设备类型 -->
              <el-form-item :label="$T('设备类型')" prop="deviceType" required>
                <el-select
                  v-model="addDeviceFormData.deviceType"
                  :placeholder="$T('请选择')"
                  style="width: 100%"
                  @change="handleDeviceTypeChange"
                >
                  <el-option
                    v-for="item in deviceTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <!-- 管网设备 -->
              <el-form-item
                :label="$T('管网设备')"
                prop="networkDevice"
                required
              >
                <div class="network-device-input">
                  <el-input
                    v-model="addDeviceFormData.networkDevice"
                    :placeholder="$T('请选择')"
                    readonly
                  />
                  <i
                    class="el-icon-edit network-device-icon"
                    @click="selectNetworkDevice"
                  ></i>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button plain @click="handleCancel">{{ $T("取消") }}</el-button>
        <el-button type="success" @click="handleConfirm">
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 管网设备选择弹窗 -->
    <el-dialog
      :title="$T('选择管网设备')"
      :visible.sync="networkDeviceDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      class="network-device-dialog"
    >
      <div class="network-device-content">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="networkDeviceSearchKeyword"
            :placeholder="$T('请输入设备名称')"
            class="search-input"
            clearable
            @input="handleNetworkDeviceSearch"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>

        <!-- 管网设备列表 -->
        <el-table
          :data="networkDevices"
          :loading="networkDeviceLoading"
          style="width: 100%; margin-top: 16px"
          height="400"
          @selection-change="handleNetworkDeviceSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="deviceName" :label="$T('设备名称')" />
          <el-table-column prop="deviceType" :label="$T('设备类型')" />
          <el-table-column prop="roomId" :label="$T('房间ID')" />
          <el-table-column prop="roomType" :label="$T('房间类型')" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleNetworkDeviceSizeChange"
            @current-change="handleNetworkDeviceCurrentChange"
            :current-page="networkDeviceCurrentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="networkDevicePageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="networkDeviceTotal"
          />
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="networkDeviceDialogVisible = false">
          {{ $T("取消") }}
        </el-button>
        <el-button
          type="primary"
          @click="confirmNetworkDeviceSelection"
          :disabled="selectedNetworkDevices.length === 0"
        >
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 设备详情抽屉 -->
    <el-drawer
      :title="$T('详情')"
      :visible.sync="deviceDetailDrawerVisible"
      direction="rtl"
      size="60%"
      class="device-detail-drawer"
      @close="handleDetailDrawerClose"
    >
      <div class="detail-content" v-if="currentDeviceDetail">
        <!-- 设备基本信息 -->
        <div class="basic-info-section">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">{{ $T("设备名称") }}</div>
              <div class="info-value">
                {{ currentDeviceDetail.deviceName || "--" }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ $T("设备类型") }}</div>
              <div class="info-value">
                {{ currentDeviceDetail.deviceType || "--" }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ $T("额定功率") }} (kW)</div>
              <div class="info-value">
                {{ currentDeviceDetail.ratedPower || "--" }}
              </div>
            </div>
          </div>
        </div>

        <!-- 管网设备 -->
        <div class="network-device-section">
          <div class="info-label">{{ $T("管网设备") }}</div>
          <div class="info-value">
            {{ currentDeviceDetail.networkDevice || "--" }}
          </div>
        </div>

        <!-- 管网设备列表 -->
        <div class="device-list-section">
          <div class="list-title">{{ $T("管网设备列表") }}</div>
          <div class="device-list-table">
            <div class="table-header">
              <div class="header-cell serial-cell">{{ $T("序号") }}</div>
              <div class="header-cell name-cell">{{ $T("管网设备名称") }}</div>
            </div>
            <div
              class="table-row"
              v-for="(device, index) in networkDeviceList"
              :key="index"
            >
              <div class="table-cell serial-cell">{{ device.id }}</div>
              <div class="table-cell name-cell">{{ device.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
// 导入API和组件
import {
  getDevicePage,
  createDevice,
  getDeviceById,
  updateDevice,
  deleteDevice,
  batchDeleteDevices,
  checkDeviceIdExists
} from "@/api/device-management";
import {
  getMonitorDevicesByRoom,
  getDeviceMonitorRelations
} from "@/api/base-config";
import { getSiteById } from "@/api/site-management";

export default {
  name: "VppDeviceManagement",
  props: {
    siteId: {
      type: Number,
      default: null
    },
    vppId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      searchKeyword: "",
      selectedCategory: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectedDevices: [], // 选中的设备列表
      devices: [], // 设备列表数据
      loading: false,
      tableLoading: false,
      searchTimer: null,

      // 新增设备弹窗相关
      addDeviceDialogVisible: false, // 弹窗显示状态

      // 设备详情抽屉相关
      deviceDetailDrawerVisible: false, // 抽屉显示状态
      currentDeviceDetail: null, // 当前查看的设备详情
      networkDeviceList: [
        { id: "01", name: "--" },
        { id: "02", name: "--" },
        { id: "03", name: "--" },
        { id: "04", name: "--" },
        { id: "05", name: "--" },
        { id: "06", name: "--" },
        { id: "07", name: "--" },
        { id: "08", name: "--" },
        { id: "09", name: "--" },
        { id: "10", name: "--" }
      ],

      // 管网设备选择相关
      networkDeviceDialogVisible: false,
      networkDevices: [],
      networkDeviceLoading: false,
      networkDeviceSearchKeyword: "",
      networkDeviceCurrentPage: 1,
      networkDevicePageSize: 10,
      networkDeviceTotal: 0,
      selectedNetworkDevices: [],
      networkDeviceSearchTimer: null, // 管网设备搜索防抖定时器
      siteInfo: null, // 站点信息，包含房间信息
      deviceMonitorRelations: [], // 电厂设备与管网设备关联关系
      allowedNetworkDeviceTypes: [], // 当前设备类型允许关联的管网设备类型

      // 设备类型选项
      deviceTypeOptions: [
        { value: "储能设备", label: "储能设备", id: 1 },
        { value: "光伏设备", label: "光伏设备", id: 2 },
        { value: "风电设备", label: "风电设备", id: 3 },
        { value: "风机泵类", label: "风机泵类", id: 4 },
        { value: "风冷式空调", label: "风冷式空调", id: 5 },
        { value: "充电设备", label: "充电设备", id: 6 }
      ],

      // 新增设备表单数据
      addDeviceFormData: {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "", // 管网设备ID
        parentDevice: "",
        ratedPower: ""
      },

      // 新增设备表单验证规则
      addDeviceFormRules: {
        deviceName: [
          {
            required: true,
            message: "请输入设备名称",
            trigger: "blur"
          },
          {
            max: 50,
            message: "设备名称不能超过50个字符",
            trigger: "blur"
          }
        ],
        deviceType: [
          {
            required: true,
            message: "请选择设备类型",
            trigger: "change"
          }
        ],
        networkDevice: [
          {
            required: true,
            message: "请选择管网设备",
            trigger: "change"
          }
        ],
        ratedPower: [
          {
            type: "number",
            message: "额定功率必须为数字",
            trigger: "blur"
          }
        ]
      }
    };
  },
  mounted() {
    this.loadDevices();
  },
  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    selectedCategory: {
      handler() {
        this.currentPage = 1;
        this.loadDevices();
      }
    },
    siteId: {
      handler() {
        if (this.siteId) {
          this.loadDevices();
        }
      },
      immediate: true
    }
  },
  computed: {
    filteredDevices() {
      // 如果是按站点加载，使用客户端过滤
      if (this.siteId) {
        let filtered = this.devices;

        // 按设备名称搜索
        if (this.searchKeyword) {
          filtered = filtered.filter(device =>
            device.deviceName
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
          );
        }

        // 按设备类型筛选
        if (this.selectedCategory) {
          filtered = filtered.filter(
            device => device.deviceType === this.selectedCategory
          );
        }

        // 分页
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return filtered.slice(start, end);
      } else {
        // 服务端分页，直接返回设备列表
        return this.devices;
      }
    },
    totalDevices() {
      return this.siteId ? this.devices.length : this.total;
    }
  },
  methods: {
    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadDevices();
      }, 500);
    },

    // 数据转换：将API返回的设备数据转换为组件显示格式
    transformDeviceData(apiDevice) {
      return {
        id: apiDevice.id,
        serialNumber: apiDevice.id, // 使用ID作为序号
        deviceName: apiDevice.deviceName,
        deviceType: apiDevice.deviceType,
        deviceSubtype: apiDevice.deviceSubType,
        deviceStatus: apiDevice.deviceStatus,
        ratedPower: apiDevice.ratedPower,
        deviceId: apiDevice.deviceId,
        siteId: apiDevice.siteId,
        manufacturer: apiDevice.manufacturer,
        model: apiDevice.model,
        maxWorkingPower: apiDevice.maxWorkingPower,
        minWorkingPower: apiDevice.minWorkingPower,
        position: apiDevice.position,
        installationDate: apiDevice.installationDate,
        operationDate: apiDevice.operationDate,
        createTime: apiDevice.createTime,
        updateTime: apiDevice.updateTime,
        // 保留原始API数据用于编辑
        originalData: apiDevice
      };
    },

    // 加载设备列表
    async loadDevices() {
      this.tableLoading = true;
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 添加搜索条件
        if (this.searchKeyword) {
          params.deviceName = this.searchKeyword;
        }
        if (this.selectedCategory) {
          params.deviceType = this.selectedCategory;
        }
        if (this.siteId) {
          params.siteId = this.siteId;
        }

        const response = await getDevicePage(params);

        if (response.code === 0) {
          this.devices = response.data.records.map(device =>
            this.transformDeviceData(device)
          );
          this.total = response.data.total;
          this.currentPage = response.data.pageNum;
          this.pageSize = response.data.pageSize;
        } else {
          this.$message.error(response.msg || "加载设备列表失败");
        }
      } catch (error) {
        console.error("加载设备列表失败:", error);
        this.$message.error("加载设备列表失败");
      } finally {
        this.tableLoading = false;
      }
    },

    // 打开新增设备弹窗
    handleAdd() {
      // 重置表单
      this.resetAddDeviceForm();
      // 打开弹窗
      this.addDeviceDialogVisible = true;
    },

    // 弹窗关闭处理
    handleDialogClose() {
      console.log("新增设备弹窗已关闭");
      // 重置表单
      this.resetAddDeviceForm();
    },

    // 取消按钮处理
    handleCancel() {
      this.addDeviceDialogVisible = false;
    },

    // 确定按钮处理
    handleConfirm() {
      // 表单验证
      this.$refs.addDeviceForm.validate(valid => {
        if (valid) {
          // 构造提交数据
          const deviceData = {
            deviceName: this.addDeviceFormData.deviceName.trim(),
            deviceType: this.addDeviceFormData.deviceType,
            parentDevice: this.addDeviceFormData.parentDevice,
            ratedPower: this.addDeviceFormData.ratedPower || 0
          };

          // 调用API保存设备数据
          this.saveDevice(deviceData);
        } else {
          this.$message.error("请检查输入信息");
        }
      });
    },

    // 保存设备数据
    async saveDevice(deviceData) {
      try {
        console.log("保存设备数据:", deviceData);

        // 调用实际API
        const res = await createDevice(deviceData);

        if (res.code === 0) {
          this.$message.success("新增设备成功");
          this.addDeviceDialogVisible = false;
          this.loadDevices(); // 重新加载设备列表
        } else {
          this.$message.error(res.msg || "新增设备失败");
        }
      } catch (error) {
        console.error("保存设备失败:", error);
        this.$message.error("新增设备失败");
      }
    },

    // 选择管网设备
    async selectNetworkDevice() {
      try {
        // 检查是否已选择设备类型
        if (!this.addDeviceFormData.deviceType) {
          this.$message.warning(this.$T("请先选择设备类型"));
          return;
        }

        // 检查是否有允许关联的管网设备类型
        if (!this.allowedNetworkDeviceTypes.length) {
          this.$message.warning(this.$T("当前设备类型无可关联的管网设备类型"));
          return;
        }

        // 首先获取站点信息
        if (!this.siteInfo && this.siteId) {
          await this.loadSiteInfo();
        }

        if (!this.siteInfo || !this.siteInfo.roomId) {
          this.$message.warning(
            this.$T("当前站点未关联房间，无法查询管网设备")
          );
          return;
        }

        // 打开管网设备选择弹窗
        this.networkDeviceDialogVisible = true;
        // 加载管网设备列表
        await this.loadNetworkDevices();
      } catch (error) {
        console.error("打开管网设备选择弹窗失败:", error);
        this.$message.error(this.$T("打开管网设备选择弹窗失败"));
      }
    },

    // 重置新增设备表单
    resetAddDeviceForm() {
      this.addDeviceFormData = {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "",
        parentDevice: "",
        ratedPower: ""
      };
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.addDeviceForm) {
          this.$refs.addDeviceForm.clearValidate();
        }
      });
    },

    // 查看设备详情
    handleDetail(row) {
      console.log("查看设备详情:", row);
      // 设置当前设备详情，确保包含networkDevice字段
      this.currentDeviceDetail = {
        ...row,
        networkDevice: row.networkDevice || "--"
      };
      this.deviceDetailDrawerVisible = true;
    },

    // 详情抽屉关闭处理
    handleDetailDrawerClose() {
      this.currentDeviceDetail = null;
    },

    handleEdit(row) {
      console.log("编辑设备:", row);
      // TODO: 编辑设备逻辑
    },

    handleDelete(row) {
      console.log("删除设备:", row);
      // TODO: 删除设备逻辑
    },

    handleSelectionChange(selection) {
      this.selectedDevices = selection;
    },
    handleBatchDelete() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning("请选择要删除的设备");
        return;
      }

      this.$confirm(
        `确定要删除选中的 ${this.selectedDevices.length} 个设备吗？`,
        "批量删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          // TODO: 调用后端批量删除接口
          const deviceIds = this.selectedDevices.map(device => device.id);
          console.log("批量删除设备IDs:", deviceIds);

          // 模拟删除成功
          this.$message.success(
            `成功删除 ${this.selectedDevices.length} 个设备`
          );

          // 清空选中状态
          this.selectedDevices = [];

          // TODO: 重新加载数据
          // this.loadDevices();
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1;
      this.loadDevices();
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.loadDevices();
    },
    getStatusClass(status) {
      switch (status) {
        case "正常运行":
          return "status-normal";
        case "故障":
          return "status-error";
        case "维护中":
          return "status-maintenance";
        default:
          return "";
      }
    },

    // 加载站点信息
    async loadSiteInfo() {
      try {
        if (!this.siteId) {
          console.warn("站点ID为空，无法加载站点信息");
          return;
        }

        const res = await getSiteById(this.siteId);
        if (res.code === 0) {
          this.siteInfo = res.data;
          console.log("站点信息加载成功:", this.siteInfo);
        } else {
          console.error("加载站点信息失败:", res.msg);
          this.$message.error(res.msg || "加载站点信息失败");
        }
      } catch (error) {
        console.error("加载站点信息失败:", error);
        this.$message.error("加载站点信息失败");
      }
    },

    // 加载管网设备列表
    async loadNetworkDevices() {
      try {
        this.networkDeviceLoading = true;

        if (!this.siteInfo || !this.siteInfo.roomId) {
          this.networkDevices = [];
          this.networkDeviceTotal = 0;
          return;
        }

        const queryData = {
          roomId: this.siteInfo.roomId,
          roomType: this.siteInfo.roomType || "",
          deviceTypes: this.allowedNetworkDeviceTypes, // 根据设备类型过滤管网设备类型
          page: this.networkDeviceCurrentPage,
          size: this.networkDevicePageSize,
          keyword: this.networkDeviceSearchKeyword
        };

        const res = await getMonitorDevicesByRoom(queryData);
        if (res.code === 0) {
          this.networkDevices = res.data || [];
          this.networkDeviceTotal = res.total || 0;
          console.log("管网设备加载成功:", this.networkDevices);
        } else {
          console.error("加载管网设备失败:", res.msg);
          this.$message.error(res.msg || "加载管网设备失败");
          this.networkDevices = [];
          this.networkDeviceTotal = 0;
        }
      } catch (error) {
        console.error("加载管网设备失败:", error);
        this.$message.error("加载管网设备失败");
        this.networkDevices = [];
        this.networkDeviceTotal = 0;
      } finally {
        this.networkDeviceLoading = false;
      }
    },

    // 管网设备搜索
    handleNetworkDeviceSearch() {
      // 防抖处理
      clearTimeout(this.networkDeviceSearchTimer);
      this.networkDeviceSearchTimer = setTimeout(() => {
        this.networkDeviceCurrentPage = 1;
        this.loadNetworkDevices();
      }, 500);
    },

    // 管网设备分页大小改变
    handleNetworkDeviceSizeChange(size) {
      this.networkDevicePageSize = size;
      this.networkDeviceCurrentPage = 1;
      this.loadNetworkDevices();
    },

    // 管网设备当前页改变
    handleNetworkDeviceCurrentChange(page) {
      this.networkDeviceCurrentPage = page;
      this.loadNetworkDevices();
    },

    // 管网设备选择改变
    handleNetworkDeviceSelectionChange(selection) {
      this.selectedNetworkDevices = selection;
    },

    // 确认管网设备选择
    confirmNetworkDeviceSelection() {
      if (this.selectedNetworkDevices.length === 0) {
        this.$message.warning(this.$T("请选择管网设备"));
        return;
      }

      // 目前只支持单选，取第一个选中的设备
      const selectedDevice = this.selectedNetworkDevices[0];
      this.addDeviceFormData.networkDevice = selectedDevice.deviceName;
      this.addDeviceFormData.networkDeviceId = selectedDevice.id;

      // 关闭弹窗
      this.networkDeviceDialogVisible = false;
      this.$message.success(this.$T("管网设备选择成功"));
    },

    // 加载电厂设备与管网设备关联关系
    async loadDeviceMonitorRelations() {
      try {
        const res = await getDeviceMonitorRelations();
        if (res.code === 0) {
          this.deviceMonitorRelations = res.data || [];
          console.log(
            "电厂设备与管网设备关联关系加载成功:",
            this.deviceMonitorRelations
          );
        } else {
          console.error("加载电厂设备与管网设备关联关系失败:", res.msg);
        }
      } catch (error) {
        console.error("加载电厂设备与管网设备关联关系失败:", error);
      }
    },

    // 设备类型改变处理
    handleDeviceTypeChange(deviceType) {
      console.log("设备类型改变:", deviceType);

      // 清空之前选择的管网设备
      this.addDeviceFormData.networkDevice = "";
      this.addDeviceFormData.networkDeviceId = "";

      // 根据设备类型获取允许关联的管网设备类型
      this.updateAllowedNetworkDeviceTypes(deviceType);
    },

    // 更新允许关联的管网设备类型
    updateAllowedNetworkDeviceTypes(deviceType) {
      this.allowedNetworkDeviceTypes = [];

      if (!deviceType || !this.deviceMonitorRelations.length) {
        return;
      }

      // 查找当前电厂设备类型对应的管网设备类型
      // 根据API文档，需要根据devicetype字段匹配，然后收集monitorlabel字段
      const monitorLabels = [];
      this.deviceMonitorRelations.forEach(relation => {
        // 这里需要根据实际的设备类型映射关系来匹配
        // 假设deviceType是设备类型名称，需要转换为对应的ID或者直接匹配
        if (this.isDeviceTypeMatch(relation.devicetype, deviceType)) {
          monitorLabels.push(relation.monitorlabel);
        }
      });

      this.allowedNetworkDeviceTypes = [...new Set(monitorLabels)]; // 去重
      console.log("允许关联的管网设备类型:", this.allowedNetworkDeviceTypes);
    },

    // 判断设备类型是否匹配
    isDeviceTypeMatch(deviceTypeId, deviceTypeName) {
      // 根据设备类型名称找到对应的ID
      const deviceTypeOption = this.deviceTypeOptions.find(
        option => option.value === deviceTypeName
      );

      if (!deviceTypeOption) {
        return false;
      }

      // 比较ID是否匹配
      return deviceTypeOption.id === deviceTypeId;
    }
  },
  created() {
    this.loadDevices();
    this.loadDeviceMonitorRelations();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    if (this.networkDeviceSearchTimer) {
      clearTimeout(this.networkDeviceSearchTimer);
    }
  }
};
</script>
<style scoped>
.device-management-container {
  width: 100%;
  height: 100%;
}

.device-management {
  padding: 24px 24px 0 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  width: 300px;
}

.category-select {
  width: 150px;
}

.add-device-btn {
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  font-weight: 400;
  border-radius: 3px;
  letter-spacing: 1px;
  padding: 5px 16px;
  font-size: 14px;
  background: #00b45e;
  border: none;
}

.add-device-btn:hover,
.add-device-btn:focus {
  background: #00c870;
}

.batch-delete-btn {
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  font-weight: 400;
  border-radius: 3px;
  letter-spacing: 1px;
  padding: 5px 16px;
  font-size: 14px;
  background: #f53f3f;
  border: none;
  margin-right: 16px;
}

.batch-delete-btn:hover,
.batch-delete-btn:focus {
  background: #e53e3e;
}

.batch-delete-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.batch-delete-btn:disabled:hover {
  background: #c0c4cc;
}

/* 设备表格样式 */
.device-table {
  width: 100%;
  font-size: 14px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 24px;
}

.device-table >>> .el-table__header th {
  background: #fafcff;
  color: #424e5f;
  font-weight: 600;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  border-bottom: 1px solid #dcdfe6;
  height: 48px;
}

.device-table >>> .el-table__row {
  transition: background 0.2s;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  color: #424e5f;
  font-size: 14px;
  height: 48px;
}

.device-table >>> .el-table__row:hover {
  background: #f0f2f5;
}

/* 设备状态样式 */
.status-normal {
  color: #00b45e;
  background: #e6f7ea;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-error {
  color: #f53f3f;
  background: #fff1f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-maintenance {
  color: #ff7d00;
  background: #fff7e6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}
/* 操作链接样式 */
.action-link {
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: #409eff;
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: #00b45e;
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: #f53f3f;
}

.delete-link:hover {
  opacity: 0.8;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px 0 0 0;
}

.total-info {
  font-size: 14px;
  color: #424e5f;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}

.custom-pagination >>> .el-pager li {
  border-radius: 3px;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  min-width: 32px;
  height: 32px;
  line-height: 32px;
  margin: 0 4px;
}

.custom-pagination >>> .el-pager li.active {
  background: #00b45e;
  color: #fff;
  border: none;
}

.custom-pagination >>> .el-pager li:not(.active) {
  background: #f0f2f5;
  color: #424e5f;
  border: none;
}

.custom-pagination >>> .el-pagination__jump {
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}

.custom-pagination >>> .el-pagination__sizes {
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}

.custom-pagination >>> .btn-prev,
.custom-pagination >>> .btn-next {
  border-radius: 3px;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  min-width: 32px;
  height: 32px;
  line-height: 32px;
  background: #f0f2f5;
  color: #424e5f;
  border: none;
}

.custom-pagination >>> .btn-prev:hover,
.custom-pagination >>> .btn-next:hover {
  background: #00b45e;
  color: #fff;
}

/* 新增设备弹窗样式 */
.add-device-dialog >>> .el-dialog {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.add-device-dialog >>> .el-dialog__header {
  background: #fff;
  padding: 20px 24px 16px 24px;
  position: relative;
}

.add-device-dialog >>> .el-dialog__title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}

.add-device-dialog >>> .el-dialog__headerbtn {
  position: absolute;
  top: 20px;
  right: 20px;
}

.add-device-dialog >>> .el-dialog__headerbtn .el-dialog__close {
  font-size: 20px;
  color: #909399;
}

.add-device-dialog >>> .el-dialog__body {
  background: #fff;
  padding: 10px 24px 20px 24px;
}

.add-device-dialog >>> .el-dialog__footer {
  background: #fff;
  padding: 16px 24px 20px 24px;
  text-align: right;
}

.dialog-content {
  background: #fff;
}

.device-form >>> .el-form-item__label {
  color: #333;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  line-height: 22px;
  padding-bottom: 8px;
}

.device-form >>> .el-form-item__label::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.device-form >>> .el-input__inner {
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #333;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  border-radius: 4px;
  height: 40px;
  line-height: 40px;
}

.device-form >>> .el-input__inner:focus {
  border-color: #409eff;
}

.device-form >>> .el-input__inner::placeholder {
  color: #c0c4cc;
}

.device-form >>> .el-select .el-input__inner {
  cursor: pointer;
}

.network-device-input {
  position: relative;
}

.network-device-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #909399;
  cursor: pointer;
  z-index: 10;
}

/* 管网设备选择弹窗样式 */
.network-device-dialog .el-dialog__body {
  padding: 20px;
}

.network-device-content {
  width: 100%;
}

.network-device-content .search-bar {
  margin-bottom: 16px;
}

.network-device-content .search-input {
  width: 300px;
}

.network-device-content .pagination-container {
  margin-top: 16px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  font-weight: 400;
  border-radius: 4px;
  padding: 9px 20px;
  font-size: 14px;
  height: 40px;
  line-height: 20px;
}

.dialog-footer .el-button--default {
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.dialog-footer .el-button--default:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.dialog-footer .el-button--success {
  background: #00b45e;
  border: 1px solid #00b45e;
  color: #fff;
}

.dialog-footer .el-button--success:hover {
  background: #00c870;
  border-color: #00c870;
}

/* 设备详情抽屉样式 */
.device-detail-drawer >>> .el-drawer {
  background: #fff;
}

.device-detail-drawer >>> .el-drawer__header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 20px 24px;
  margin-bottom: 0;
}

.device-detail-drawer >>> .el-drawer__title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}

.device-detail-drawer >>> .el-drawer__close-btn {
  color: #909399;
  font-size: 20px;
}

.device-detail-drawer >>> .el-drawer__body {
  background: #fff;
  padding: 0;
}

.detail-content {
  padding: 24px;
  background: #fff;
}

/* 设备基本信息样式 */
.basic-info-section {
  margin-bottom: 32px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  color: #666;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  margin-bottom: 8px;
  line-height: 20px;
}

.info-value {
  color: #333;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  line-height: 20px;
  min-height: 20px;
}

/* 管网设备样式 */
.network-device-section {
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
}

.network-device-section .info-label {
  margin-bottom: 8px;
}

.network-device-section .info-value {
  color: #333;
}

/* 管网设备列表样式 */
.device-list-section {
  margin-bottom: 24px;
}

.list-title {
  color: #333;
  font-size: 16px;
  font-weight: 500;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  margin-bottom: 16px;
  line-height: 24px;
}

.device-list-table {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e6e6e6;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
  background: #fff;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px 16px;
  color: #333;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  line-height: 20px;
}

.header-cell {
  padding: 12px 16px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  background: #f8f9fa;
  line-height: 20px;
}

.serial-cell {
  flex: 0 0 120px;
  border-right: 1px solid #e6e6e6;
}

.name-cell {
  flex: 1;
}
</style>
