<template>
  <cet-aside class="resource-config-page">
    <template #aside>
      <!-- 左侧树形组件 -->
      <VppTree
        :tree-data="treeData"
        @node-click="handleNodeClick"
        @nodes-checked="handleNodesChecked"
        @tree-data-updated="handleTreeDataUpdated"
      />
    </template>
    <template #container>
      <!-- 右侧主要内容区域 -->
      <div>
        <component
          :is="currentComponent"
          :key="componentKey"
          :node="selectedNode"
          :vppId="currentVppId"
          :userId="currentUserId"
          :resourceId="currentResourceId"
          :siteId="currentSiteId"
          :roomId="currentRoomId"
          :siteType="currentSiteType"
          :resourceType="currentResourceType"
          :province="currentProvince"
        />
      </div>
    </template>
  </cet-aside>
</template>

<script>
import VppTree from "./components/VppTree.vue";
import UserManagement from "./components/UserManagement.vue";
import ResourceManagement from "./components/ResourceManagement.vue";
import SiteManagement from "./components/SiteManagement.vue";
import DeviceManagement from "./components/DeviceManagement.vue";
import VppDeviceManagement from "./components/VppDeviceManagement.vue";

export default {
  name: "ResourceConfig",
  components: {
    VppTree,
    UserManagement,
    ResourceManagement,
    SiteManagement,
    DeviceManagement,
    VppDeviceManagement
  },
  data() {
    return {
      treeData: [],
      selectedNode: null,
      componentKey: Date.now() // 用于强制组件重新创建
    };
  },
  computed: {
    currentComponent() {
      let component;
      if (!this.selectedNode) {
        component = "UserManagement";
      } else {
        switch (this.selectedNode.type) {
          case "vpp":
            component = "UserManagement";
            break;
          case "user":
            component = "ResourceManagement";
            break;
          case "resource":
            component = "SiteManagement";
            break;
          case "site":
            component = "VppDeviceManagement";
            break;
          case "device":
            component = "DeviceManagement";
            break;
          default:
            component = "UserManagement";
        }
      }

      return component;
    },

    // 当前VPP ID
    currentVppId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是VPP节点
      if (this.selectedNode.type === "vpp") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找VPP节点
      return this.findParentNodeId("vpp");
    },

    // 当前用户ID
    currentUserId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是用户节点
      if (this.selectedNode.type === "user") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找用户节点
      return this.findParentNodeId("user");
    },

    // 当前资源ID
    currentResourceId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是资源节点
      if (this.selectedNode.type === "resource") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找资源节点
      return this.findParentNodeId("resource");
    },

    // 当前站点ID
    currentSiteId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是站点节点
      if (this.selectedNode.type === "site") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找站点节点
      return this.findParentNodeId("site");
    },

    // 当前房间ID
    currentRoomId() {
      if (!this.selectedNode) return null;
      return this.selectedNode.roomId || null;
    },

    // 当前站点类型
    currentSiteType() {
      if (!this.selectedNode) return null;
      return this.selectedNode.siteType || null;
    },

    // 当前资源类型
    currentResourceType() {
      if (!this.selectedNode) return null;
      return this.selectedNode.resourceType || null;
    },

    // 当前电厂所属省份
    currentProvince() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是VPP节点，直接返回province
      if (this.selectedNode.type === "vpp") {
        return this.selectedNode.province || null;
      }

      // 从树形结构中向上查找VPP节点的province
      const vppNode = this.findParentNode("vpp");
      return vppNode ? vppNode.province : null;
    }
  },
  methods: {
    /**
     * 处理树节点点击事件
     */
    handleNodeClick(node) {
      console.log("Node clicked:", node?.type, "ID:", node?.originalId);
      console.log("Will switch to component:", this.getComponentForNode(node));

      // 更新选中节点和组件key来强制重新创建组件
      this.selectedNode = node;
      this.componentKey = Date.now();

      console.log("Component will be recreated with key:", this.componentKey);
    },

    /**
     * 获取节点对应的组件名称（用于调试）
     */
    getComponentForNode(node) {
      if (!node) return "UserManagement";
      switch (node.type) {
        case "vpp":
          return "UserManagement";
        case "user":
          return "ResourceManagement";
        case "resource":
          return "SiteManagement";
        case "site":
          return "VppDeviceManagement";
        case "device":
          return "DeviceManagement";
        default:
          return "UserManagement";
      }
    },

    /**
     * 处理树节点勾选事件
     */
    handleNodesChecked(nodes) {
      console.log("Checked nodes:", nodes);
    },

    /**
     * 处理树形数据更新事件
     */
    handleTreeDataUpdated(treeData) {
      this.treeData = treeData;
      console.log("Tree data updated:", treeData);
    },

    /**
     * 查找指定类型的父节点ID
     * @param {string} targetType - 目标节点类型 (vpp/user/resource/site)
     * @returns {number|null} 父节点的originalId
     */
    findParentNodeId(targetType) {
      const parentNode = this.findParentNode(targetType);
      return parentNode ? parentNode.originalId : null;
    },

    /**
     * 查找指定类型的父节点对象
     * @param {string} targetType - 目标节点类型 (vpp/user/resource/site)
     * @returns {Object|null} 父节点对象
     */
    findParentNode(targetType) {
      if (!this.selectedNode || !this.treeData) return null;

      // 层级关系：vpp -> user -> resource -> site -> device
      const hierarchy = ["vpp", "user", "resource", "site", "device"];
      const currentIndex = hierarchy.indexOf(this.selectedNode.type);
      const targetIndex = hierarchy.indexOf(targetType);

      // 如果目标类型不在当前节点的上级，返回null
      if (targetIndex >= currentIndex) return null;

      // 从当前节点开始向上查找
      let currentNodeId = this.selectedNode.tree_id;
      let currentNode = this.findNodeById(currentNodeId);

      while (currentNode && currentNode.type !== targetType) {
        if (!currentNode.pId) break;
        currentNode = this.findNodeById(currentNode.pId);
      }

      return currentNode && currentNode.type === targetType
        ? currentNode
        : null;
    },

    /**
     * 根据tree_id查找节点
     * @param {string} treeId - 树节点ID
     * @returns {Object|null} 找到的节点
     */
    findNodeById(treeId) {
      if (!this.treeData || !Array.isArray(this.treeData)) return null;

      for (const node of this.treeData) {
        if (node.tree_id === treeId) {
          return node;
        }
      }
      return null;
    }
  },
  mounted() {
    // 树形数据现在通过API自动加载，无需手动设置
  }
};
</script>

<style lang="scss" scoped>
.resource-config-page {
  height: 100%;
}

// cet-aside组件会自动处理布局，这里只需要设置基本样式
.resource-config-page :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
</style>
