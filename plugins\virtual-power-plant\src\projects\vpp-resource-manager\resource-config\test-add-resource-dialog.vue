<template>
  <div class="test-page">
    <h1>{{ $T("新增资源弹窗测试") }}</h1>
    <el-button type="primary" @click="showDialog">
      {{ $T("打开新增资源弹窗") }}
    </el-button>

    <AddResourceDialog
      :visible="dialogVisible"
      @close="handleClose"
      @save="handleSave"
    />
  </div>
</template>

<script>
import AddResourceDialog from "./components/AddResourceDialog.vue";

export default {
  name: "TestAddResourceDialog",
  components: {
    AddResourceDialog
  },
  data() {
    return {
      dialogVisible: false
    };
  },
  methods: {
    showDialog() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave(resourceData) {
      console.log("保存资源数据:", resourceData);
      this.$message.success(this.$T("资源数据保存成功"));
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
}
</style>
