<template>
  <div class="test-page">
    <h1>{{ $T("新增代理用户弹窗测试") }}</h1>
    <el-button type="primary" @click="showDialog">
      {{ $T("打开新增代理用户弹窗") }}
    </el-button>
    
    <AddUserDialog
      :visible="dialogVisible"
      :regionOptions="regionOptions"
      @close="handleClose"
      @save="handleSave"
    />
  </div>
</template>

<script>
import AddUserDialog from "./components/AddUserDialog.vue";

export default {
  name: "TestAddUserDialog",
  components: {
    AddUserDialog
  },
  data() {
    return {
      dialogVisible: false,
      regionOptions: [
        { label: this.$T("广州"), value: "guangzhou" },
        { label: this.$T("深圳"), value: "shenzhen" },
        { label: this.$T("佛山"), value: "foshan" },
        { label: this.$T("东莞"), value: "dongguan" },
        { label: this.$T("中山"), value: "zhongshan" },
        { label: this.$T("珠海"), value: "zhuhai" }
      ]
    };
  },
  methods: {
    showDialog() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSave(userData) {
      console.log("保存用户数据:", userData);
      this.$message.success(this.$T("用户数据保存成功"));
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
}
</style>
