<template>
  <div class="vpp-info-page">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">{{ $T("虚拟电厂厂站信息") }}</h2>
      <el-button type="primary" class="config-btn" @click="handleConfigVpp">
        {{ $T("配置虚拟电厂") }}
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 虚拟电厂基本信息卡片 -->
      <div class="vpp-info-section">
        <!-- 左侧：图片回显 -->
        <div class="vpp-image-container">
          <img
            :src="vppInfo.image || defaultImage"
            alt="虚拟电厂"
            class="vpp-image"
          />
        </div>

        <!-- 右侧：基本信息 -->
        <div class="vpp-basic-info">
          <!-- 第一行 -->
          <div class="info-row">
            <div class="info-item">
              <span class="info-text">{{ $T("虚拟电厂名称：--") }}</span>
            </div>
            <div class="info-item">
              <span class="info-text">{{ $T("虚拟电厂所属省份：--") }}</span>
            </div>
            <div class="info-item">
              <span class="info-text">{{ $T("虚拟电厂站类型：--") }}</span>
            </div>
          </div>
          <!-- 第二行 -->
          <div class="info-row">
            <div class="info-item">
              <span class="info-text">{{ $T("虚拟电厂成立时间：--") }}</span>
            </div>
            <div class="info-item">
              <span class="info-text">{{ $T("运营商编号：--") }}</span>
            </div>
            <div class="info-item opacity-0">
              <span class="info-text">{{ $T("虚拟电厂成立时间：--") }}</span>
            </div>
          </div>
          <!-- 第三行 -->
          <div class="info-row">
            <div class="info-item full-width">
              <span class="info-text">
                {{ $T("虚拟电厂厂站类型说明：--") }}
              </span>
            </div>
          </div>

          <!-- 技术参数卡片区域 -->
          <div class="tech-params-container">
            <div class="tech-params-grid" :class="getTechParamsGridClass()">
              <!-- 需求响应卡片 -->
              <div class="param-card">
                <h4 class="card-title">{{ $T("需求响应") }}</h4>
                <div class="card-content">
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格上限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.demandResponse.priceUpper || "--" }}
                    </span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格下限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.demandResponse.priceLower || "--" }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 调峰卡片 -->
              <div class="param-card">
                <h4 class="card-title">{{ $T("调峰") }}</h4>
                <div class="card-content">
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格上限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.peakRegulation.priceUpper || "--" }}
                    </span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格下限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.peakRegulation.priceLower || "--" }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 调频卡片 -->
              <div class="param-card">
                <h4 class="card-title">{{ $T("调频") }}</h4>
                <div class="card-content">
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格上限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.frequencyRegulation.priceUpper || "--" }}
                    </span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格下限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.frequencyRegulation.priceLower || "--" }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 调节卡片 - 仅当类型包含调节型时显示 -->
              <div v-if="isRegulationType" class="param-card">
                <h4 class="card-title">{{ $T("调节") }}</h4>
                <div class="card-content">
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格上限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.regulation.priceUpper || "--" }}
                    </span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格下限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.regulation.priceLower || "--" }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 能量卡片 - 仅当类型包含能量型时显示 -->
              <div v-if="isEnergyType" class="param-card">
                <h4 class="card-title">{{ $T("能量") }}</h4>
                <div class="card-content">
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格上限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.energy.priceUpper || "--" }}
                    </span>
                  </div>
                  <div class="param-item">
                    <span class="param-label">
                      {{ $T("申报价格下限（元/MWh）") }}
                    </span>
                    <span class="param-value">
                      {{ techParams.energy.priceLower || "--" }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 虚拟电厂资源统计 -->
      <div class="stats-section">
        <div class="stats-header">
          <h3 class="stats-title">{{ $T("虚拟电厂资源统计") }}</h3>
        </div>
        <div class="stats-cards">
          <div class="stats-card">
            <div class="stats-icon">
              <img src="" alt="用户图标" class="icon-image" />
            </div>
            <div class="stats-info">
              <div class="stats-label">{{ $T("接入企业/用户数量（家）") }}</div>
              <div class="stats-value">
                {{ capacityStats.userCount || "--" }}
              </div>
            </div>
          </div>

          <div class="stats-card">
            <div class="stats-icon">
              <img src="" alt="资源图标" class="icon-image" />
            </div>
            <div class="stats-info">
              <div class="stats-label">{{ $T("聚合资源数量（个）") }}</div>
              <div class="stats-value">
                {{ capacityStats.resourceCount || "--" }}
              </div>
            </div>
          </div>

          <div class="stats-card">
            <div class="stats-icon">
              <img src="" alt="容量图标" class="icon-image" />
            </div>
            <div class="stats-info">
              <div class="stats-label">{{ $T("可调节容量（MW）") }}</div>
              <div class="stats-value">
                {{ capacityStats.totalCapacity || "--" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "VirtualPowerPlantInfo",
  data() {
    return {
      // 默认电厂图片 - 使用占位符
      defaultImage:
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik04MCA2MEg0MFY5MEg4MFY2MFoiIGZpbGw9IiNEREREREQiLz4KPHA+dGggZD0iTTE2MCA2MEgxMjBWOTBIMTYwVjYwWiIgZmlsbD0iI0RERERERCIvPgo8cGF0aCBkPSJNMTAwIDMwSDEwMFY2MEgxMDBWMzBaIiBzdHJva2U9IiNEREREREQiIHN0cm9rZS13aWR0aD0iMiIvPgo8dGV4dCB4PSIxMDAiIHk9IjEyMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OTk5OSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIj7ooZXmi6XnlLXljoI8L3RleHQ+Cjwvc3ZnPgo=",
      // 虚拟电厂基本信息
      vppInfo: {
        name: "",
        province: "",
        stationType: "",
        establishTime: "",
        operatorCode: "",
        stationTypeDesc: "",
        image: "",
        // 虚拟电厂类型数组，支持多选
        // 1=发电类, 2=负荷类, 3=调节型, 4=能量型
        vppTypes: []
      },
      // 技术参数
      techParams: {
        demandResponse: {
          priceUpper: "",
          priceLower: ""
        },
        peakRegulation: {
          priceUpper: "",
          priceLower: ""
        },
        frequencyRegulation: {
          priceUpper: "",
          priceLower: ""
        },
        regulation: {
          priceUpper: "",
          priceLower: ""
        },
        energy: {
          priceUpper: "",
          priceLower: ""
        }
      },
      // 容量统计数据
      capacityStats: {
        userCount: "",
        resourceCount: "",
        totalCapacity: ""
      }
    };
  },
  computed: {
    // 是否包含调节型
    isRegulationType() {
      return this.vppInfo.vppTypes.includes(3);
    },
    // 是否包含能量型
    isEnergyType() {
      return this.vppInfo.vppTypes.includes(4);
    },
    // 计算显示的卡片总数
    totalCardCount() {
      let count = 3; // 基础三个卡片：需求响应、调峰、调频
      if (this.isRegulationType) count++;
      if (this.isEnergyType) count++;
      return count;
    }
  },
  mounted() {
    this.loadVppData();
  },
  methods: {
    // 获取技术参数网格样式类
    getTechParamsGridClass() {
      const count = this.totalCardCount;
      switch (count) {
        case 3:
          return "grid-3-cards";
        case 4:
          return "grid-4-cards";
        case 5:
          return "grid-5-cards";
        default:
          return "grid-3-cards";
      }
    },

    // 加载虚拟电厂数据
    async loadVppData() {
      try {
        // TODO: 调用API获取数据
        // const response = await this.$http.get('/api/vpp/info');
        // if (response.code === 0) {
        //   this.vppInfo = response.data.vppInfo;
        //   this.techParams = response.data.techParams;
        // }

        // 暂时显示空状态，等待API实现
        this.vppInfo = null;
        this.techParams = null;

        this.capacityStats = {
          userCount: "25",
          resourceCount: "156",
          totalCapacity: "1250"
        };
      } catch (error) {
        console.error("加载虚拟电厂数据失败:", error);
        this.$message.error("加载数据失败");
      }
    },

    // 配置虚拟电厂
    handleConfigVpp() {
      this.$message.info("配置虚拟电厂功能");
      // TODO: 打开配置虚拟电厂弹窗或跳转到配置页面
    },

    // 测试方法：切换虚拟电厂类型以演示动态布局
    toggleVppType(type) {
      const index = this.vppInfo.vppTypes.indexOf(type);
      if (index > -1) {
        this.vppInfo.vppTypes.splice(index, 1);
      } else {
        this.vppInfo.vppTypes.push(type);
      }
      console.log("当前虚拟电厂类型:", this.vppInfo.vppTypes);
      console.log("卡片总数:", this.totalCardCount);
      console.log("网格样式类:", this.getTechParamsGridClass());
    }
  }
};
</script>

<style scoped>
.vpp-info-page {
  padding: 24px;
  background: #ffffff;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-family: "PingFang SC", sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  color: #13171f;
  margin: 0;
}

.config-btn {
  background: #00b45e;
  border-color: #00b45e;
  color: white;
  padding: 5px 16px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 1.57;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 虚拟电厂基本信息 */
.vpp-info-section {
  display: flex;
  gap: 16px;
}

.vpp-image-container {
  width: 199px;
  height: 258px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  background: #d9d9d9;
}

.vpp-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 基本信息模块 */
.vpp-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-row {
  display: flex;
  gap: 16px;
}

.info-item {
  flex: 1;
  background: #f6f8fa;
  border-radius: 4px;
  padding: 10px;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.info-item.full-width {
  flex: none;
  width: 100%;
}

.info-item.opacity-0 {
  opacity: 0;
}

.info-text {
  font-family: "PingFang SC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.57;
  color: #424e5f;
}

/* 技术参数容器 */
.tech-params-container {
  width: 100%;
}

.tech-params-grid {
  display: grid;
  gap: 16px;
}

/* 技术参数卡片 */
.param-card {
  width: 443px;
  height: 121px;
  background: #f6f8fa;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.card-title {
  font-family: "PingFang SC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #424e5f;
  margin: 0 0 12px 0;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  flex: 1;
}

.param-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-label {
  font-family: "PingFang SC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #798492;
}

.param-value {
  font-family: "PingFang SC", sans-serif;
  font-size: 36px;
  font-weight: 500;
  line-height: 46px;
  color: #13171f;
  font-stretch: 200.5%;
}

/* 动态网格布局 */
/* 3个卡片：一行3个，但由于卡片宽度固定，使用auto-fit */
.grid-3-cards {
  grid-template-columns: repeat(auto-fit, 443px);
  justify-content: flex-start;
}

/* 4个卡片：两行，每行2个 */
.grid-4-cards {
  grid-template-columns: repeat(2, 443px);
  justify-content: flex-start;
}

/* 5个卡片：第一行3个，第二行2个 */
.grid-5-cards {
  grid-template-columns: repeat(3, 443px);
  justify-content: flex-start;
}

/* 资源统计区域 */
.stats-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stats-header {
  display: flex;
  align-items: center;
}

.stats-title {
  font-family: "PingFang SC", sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  color: #13171f;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 16px;
}

.stats-card {
  flex: 1;
  background: #f6f8fa;
  border-radius: 4px;
  padding: 16px;
  height: 88px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 56px;
  height: 56px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #d9d9d9;
  border-radius: 4px;
}

.icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stats-label {
  font-family: "PingFang SC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
  color: #798492;
  margin-bottom: 4px;
}

.stats-value {
  font-family: "PingFang SC", sans-serif;
  font-size: 28px;
  font-weight: 500;
  line-height: 1.29;
  color: #13171f;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .grid-3-cards,
  .grid-4-cards,
  .grid-5-cards {
    grid-template-columns: repeat(2, 443px);
    justify-content: flex-start;
  }
}

@media (max-width: 950px) {
  .param-card {
    width: 100%;
    max-width: 443px;
  }

  .grid-3-cards,
  .grid-4-cards,
  .grid-5-cards {
    grid-template-columns: 1fr;
    justify-content: stretch;
  }
}

@media (max-width: 768px) {
  .vpp-info-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .vpp-info-section {
    flex-direction: column;
    gap: 16px;
  }

  .vpp-image-container {
    width: 100%;
    height: 200px;
  }

  .info-row {
    flex-direction: column;
    gap: 12px;
  }

  .stats-cards {
    flex-direction: column;
    gap: 12px;
  }

  .param-card {
    width: 100%;
    height: auto;
    min-height: 121px;
  }

  .card-content {
    flex-direction: column;
    gap: 16px;
  }

  .param-item {
    gap: 6px;
  }

  .param-value {
    font-size: 28px;
    line-height: 36px;
    font-stretch: 200.5%;
  }
}
</style>
