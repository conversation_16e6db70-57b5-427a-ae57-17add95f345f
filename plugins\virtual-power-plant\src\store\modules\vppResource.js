import { getUserPage } from "@/api/user-management";
import { getResourcePage } from "@/api/resource-management";
import { getSitePage } from "@/api/site-management";
import { getDevicePage } from "@/api/device-management";

const state = {
  vpps: [],
  users: [],
  resources: [],
  sites: [],
  devices: [],
  loading: false,
  listUser: [] // 新增，防止未定义报错
};

const mutations = {
  SET_VPPS(state, vpps) {
    state.vpps = vpps;
  },
  SET_USERS(state, users) {
    state.users = users;
  },
  SET_RESOURCES(state, resources) {
    state.resources = resources;
  },
  SET_SITES(state, sites) {
    state.sites = sites;
  },
  SET_DEVICES(state, devices) {
    state.devices = devices;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  }
};

const actions = {
  async fetchVpps({ commit }, params) {
    commit("SET_LOADING", true);
    // VPP相关API需要单独实现，这里暂时返回空数组
    commit("SET_VPPS", []);
    commit("SET_LOADING", false);
  },
  async fetchUsers({ commit }, params) {
    commit("SET_LOADING", true);
    try {
      const res = await getUserPage(params);
      commit("SET_USERS", res.data.records || []);
    } catch (error) {
      console.error("获取用户列表失败:", error);
      commit("SET_USERS", []);
    }
    commit("SET_LOADING", false);
  },
  async fetchResources({ commit }, params) {
    commit("SET_LOADING", true);
    try {
      const res = await getResourcePage(params);
      commit("SET_RESOURCES", res.data.records || []);
    } catch (error) {
      console.error("获取资源列表失败:", error);
      commit("SET_RESOURCES", []);
    }
    commit("SET_LOADING", false);
  },
  async fetchSites({ commit }, params) {
    commit("SET_LOADING", true);
    try {
      const res = await getSitePage(params);
      commit("SET_SITES", res.data.records || []);
    } catch (error) {
      console.error("获取站点列表失败:", error);
      commit("SET_SITES", []);
    }
    commit("SET_LOADING", false);
  },
  async fetchDevices({ commit }, params) {
    commit("SET_LOADING", true);
    try {
      const res = await getDevicePage(params);
      commit("SET_DEVICES", res.data.records || []);
    } catch (error) {
      console.error("获取设备列表失败:", error);
      commit("SET_DEVICES", []);
    }
    commit("SET_LOADING", false);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
