/**
 * 虚拟电厂相关枚举定义
 */

/**
 * 资源类型枚举
 * @readonly
 * @enum {number}
 */
export const RESOURCE_TYPE = {
  /** 发电设备 */
  GENERATION: 1,
  /** 储能设备 */
  STORAGE: 2,
  /** 负荷设备 */
  LOAD: 3,
  /** 微电网资源 */
  MICROGRID_RESOURCE: 4
};

/**
 * 响应方式枚举
 * @readonly
 * @enum {number}
 */
export const RESPONSE_MODE = {
  /** 自动 */
  AUTO: 1,
  /** 手动 */
  MANUAL: 2
};

/**
 * 资源类型映射 - 用于API数据转换
 */
export const RESOURCE_TYPE_MAP = {
  // 数字枚举到内部标识的映射
  [RESOURCE_TYPE.GENERATION]: "generation",
  [RESOURCE_TYPE.STORAGE]: "storage", 
  [RESOURCE_TYPE.LOAD]: "load",
  [RESOURCE_TYPE.MICROGRID_RESOURCE]: "microgrid",
  
  // 反向映射：内部标识到数字枚举
  generation: RESOURCE_TYPE.GENERATION,
  storage: RESOURCE_TYPE.STORAGE,
  load: RESOURCE_TYPE.LOAD,
  microgrid: RESOURCE_TYPE.MICROGRID_RESOURCE
};

/**
 * 响应方式映射 - 用于API数据转换
 */
export const RESPONSE_MODE_MAP = {
  // 数字枚举到内部标识的映射
  [RESPONSE_MODE.AUTO]: "auto",
  [RESPONSE_MODE.MANUAL]: "manual",
  
  // 反向映射：内部标识到数字枚举
  auto: RESPONSE_MODE.AUTO,
  manual: RESPONSE_MODE.MANUAL
};

/**
 * 资源类型显示文本
 */
export const RESOURCE_TYPE_TEXT = {
  [RESOURCE_TYPE.GENERATION]: "发电设备",
  [RESOURCE_TYPE.STORAGE]: "储能设备",
  [RESOURCE_TYPE.LOAD]: "负荷设备", 
  [RESOURCE_TYPE.MICROGRID_RESOURCE]: "微电网资源",
  
  // 内部标识到显示文本的映射
  generation: "发电设备",
  storage: "储能设备",
  load: "负荷设备",
  microgrid: "微电网资源"
};

/**
 * 响应方式显示文本
 */
export const RESPONSE_MODE_TEXT = {
  [RESPONSE_MODE.AUTO]: "自动",
  [RESPONSE_MODE.MANUAL]: "手动",
  
  // 内部标识到显示文本的映射
  auto: "自动",
  manual: "手动"
};

/**
 * 资源类型CSS类名映射
 */
export const RESOURCE_TYPE_CSS_CLASS = {
  [RESOURCE_TYPE.GENERATION]: "tag-generation",
  [RESOURCE_TYPE.STORAGE]: "tag-storage",
  [RESOURCE_TYPE.LOAD]: "tag-load",
  [RESOURCE_TYPE.MICROGRID_RESOURCE]: "tag-microgrid",
  
  // 内部标识到CSS类名的映射
  generation: "tag-generation",
  storage: "tag-storage", 
  load: "tag-load",
  microgrid: "tag-microgrid"
};

/**
 * 资源类型选项 - 用于下拉框
 */
export const RESOURCE_TYPE_OPTIONS = [
  { label: "全部", value: "" },
  { label: RESOURCE_TYPE_TEXT[RESOURCE_TYPE.GENERATION], value: RESOURCE_TYPE.GENERATION },
  { label: RESOURCE_TYPE_TEXT[RESOURCE_TYPE.STORAGE], value: RESOURCE_TYPE.STORAGE },
  { label: RESOURCE_TYPE_TEXT[RESOURCE_TYPE.LOAD], value: RESOURCE_TYPE.LOAD },
  { label: RESOURCE_TYPE_TEXT[RESOURCE_TYPE.MICROGRID_RESOURCE], value: RESOURCE_TYPE.MICROGRID_RESOURCE }
];

/**
 * 响应方式选项 - 用于下拉框
 */
export const RESPONSE_MODE_OPTIONS = [
  { label: RESPONSE_MODE_TEXT[RESPONSE_MODE.AUTO], value: RESPONSE_MODE.AUTO },
  { label: RESPONSE_MODE_TEXT[RESPONSE_MODE.MANUAL], value: RESPONSE_MODE.MANUAL }
];

/**
 * 工具函数：根据资源类型枚举值获取显示文本
 * @param {number} type - 资源类型枚举值
 * @returns {string} 显示文本
 */
export function getResourceTypeText(type) {
  return RESOURCE_TYPE_TEXT[type] || RESOURCE_TYPE_TEXT[RESOURCE_TYPE_MAP[type]] || type;
}

/**
 * 工具函数：根据资源类型枚举值获取CSS类名
 * @param {number} type - 资源类型枚举值
 * @returns {string} CSS类名
 */
export function getResourceTypeClass(type) {
  return RESOURCE_TYPE_CSS_CLASS[type] || RESOURCE_TYPE_CSS_CLASS[RESOURCE_TYPE_MAP[type]] || "tag-default";
}

/**
 * 工具函数：根据响应方式枚举值获取显示文本
 * @param {number} mode - 响应方式枚举值
 * @returns {string} 显示文本
 */
export function getResponseModeText(mode) {
  return RESPONSE_MODE_TEXT[mode] || RESPONSE_MODE_TEXT[RESPONSE_MODE_MAP[mode]] || mode;
}

/**
 * 工具函数：将API资源类型转换为内部标识
 * @param {number} apiType - API返回的资源类型
 * @returns {string} 内部标识
 */
export function convertApiResourceType(apiType) {
  return RESOURCE_TYPE_MAP[apiType] || "load";
}

/**
 * 工具函数：将内部资源类型标识转换为API枚举值
 * @param {string} internalType - 内部资源类型标识
 * @returns {number} API枚举值
 */
export function convertToApiResourceType(internalType) {
  return RESOURCE_TYPE_MAP[internalType] || RESOURCE_TYPE.LOAD;
}

/**
 * 工具函数：将API响应方式转换为内部标识
 * @param {number} apiMode - API返回的响应方式
 * @returns {string} 内部标识
 */
export function convertApiResponseMode(apiMode) {
  return RESPONSE_MODE_MAP[apiMode] || "auto";
}

/**
 * 工具函数：将内部响应方式标识转换为API枚举值
 * @param {string} internalMode - 内部响应方式标识
 * @returns {number} API枚举值
 */
export function convertToApiResponseMode(internalMode) {
  return RESPONSE_MODE_MAP[internalMode] || RESPONSE_MODE.AUTO;
}
