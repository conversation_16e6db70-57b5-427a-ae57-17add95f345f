/**
 * 站点类型常量定义
 * 虚拟电厂资源管理系统
 */

// 站点类型编码（与后端枚举保持一致）
export const SITE_TYPE_CODES = {
  SELF_POWER: 1, // 自备电源
  USER_STORAGE: 2, // 用户侧储能
  ELECTRIC_VEHICLE: 3, // 电动汽车
  CHARGING_STATION: 4, // 充电站
  BATTERY_SWAP_STATION: 5, // 换电站
  BUILDING_AIR_CONDITIONING: 6, // 楼宇空调
  COMMERCIAL_LOAD: 7, // 工商业可调节负荷
  DISTRIBUTED_PV: 8, // 分布式光伏
  DISTRIBUTED_WIND: 9, // 分散式风电
  DISTRIBUTED_STORAGE: 10, // 分布式独立储能
  NON_ADJUSTABLE_LOAD: 11, // 非可调节负荷
  OTHER: 99 // 其他
};

// 站点类型选项列表
export const SITE_TYPE_OPTIONS = [
  { value: SITE_TYPE_CODES.SELF_POWER, label: "自备电源" },
  { value: SITE_TYPE_CODES.USER_STORAGE, label: "用户侧储能" },
  { value: SITE_TYPE_CODES.ELECTRIC_VEHICLE, label: "电动汽车" },
  { value: SITE_TYPE_CODES.CHARGING_STATION, label: "充电站" },
  { value: SITE_TYPE_CODES.BATTERY_SWAP_STATION, label: "换电站" },
  { value: SITE_TYPE_CODES.BUILDING_AIR_CONDITIONING, label: "楼宇空调" },
  { value: SITE_TYPE_CODES.COMMERCIAL_LOAD, label: "工商业可调节负荷" },
  { value: SITE_TYPE_CODES.DISTRIBUTED_PV, label: "分布式光伏" },
  { value: SITE_TYPE_CODES.DISTRIBUTED_WIND, label: "分散式风电" },
  { value: SITE_TYPE_CODES.DISTRIBUTED_STORAGE, label: "分布式独立储能" },
  { value: SITE_TYPE_CODES.NON_ADJUSTABLE_LOAD, label: "非可调节负荷" },
  { value: SITE_TYPE_CODES.OTHER, label: "其他" }
];

// 资源类型编码
export const RESOURCE_TYPE_CODES = {
  GENERATION: 1, // 发电资源
  STORAGE: 2, // 储电资源
  CONSUMPTION: 3, // 用电资源
  MICROGRID: 4 // 微电网资源
};

// 资源-站点类型关联关系
export const RESOURCE_SITE_RELATION = {
  [RESOURCE_TYPE_CODES.GENERATION]: [
    SITE_TYPE_CODES.SELF_POWER, // 自备电源
    SITE_TYPE_CODES.DISTRIBUTED_PV, // 分布式光伏
    SITE_TYPE_CODES.DISTRIBUTED_WIND // 分散式风电
  ],
  [RESOURCE_TYPE_CODES.STORAGE]: [
    SITE_TYPE_CODES.USER_STORAGE, // 用户侧储能
    SITE_TYPE_CODES.DISTRIBUTED_STORAGE // 分布式独立储能
  ],
  [RESOURCE_TYPE_CODES.CONSUMPTION]: [
    SITE_TYPE_CODES.ELECTRIC_VEHICLE, // 电动汽车
    SITE_TYPE_CODES.CHARGING_STATION, // 充电站
    SITE_TYPE_CODES.BATTERY_SWAP_STATION, // 换电站
    SITE_TYPE_CODES.BUILDING_AIR_CONDITIONING, // 楼宇空调
    SITE_TYPE_CODES.COMMERCIAL_LOAD, // 工商业可调节负荷
    SITE_TYPE_CODES.NON_ADJUSTABLE_LOAD, // 非可调节负荷
    SITE_TYPE_CODES.OTHER // 其他
  ],
  [RESOURCE_TYPE_CODES.MICROGRID]: [
    SITE_TYPE_CODES.SELF_POWER,
    SITE_TYPE_CODES.USER_STORAGE,
    SITE_TYPE_CODES.ELECTRIC_VEHICLE,
    SITE_TYPE_CODES.CHARGING_STATION,
    SITE_TYPE_CODES.BATTERY_SWAP_STATION,
    SITE_TYPE_CODES.BUILDING_AIR_CONDITIONING,
    SITE_TYPE_CODES.COMMERCIAL_LOAD,
    SITE_TYPE_CODES.DISTRIBUTED_PV,
    SITE_TYPE_CODES.DISTRIBUTED_WIND,
    SITE_TYPE_CODES.DISTRIBUTED_STORAGE,
    SITE_TYPE_CODES.NON_ADJUSTABLE_LOAD,
    SITE_TYPE_CODES.OTHER
  ]
};

// 站点类型分组（保持向后兼容）
export const SITE_TYPE_GROUPS = {
  // 储能类：用户侧储能、分布式独立储能
  STORAGE: [SITE_TYPE_CODES.USER_STORAGE, SITE_TYPE_CODES.DISTRIBUTED_STORAGE],

  // 新能源类：分布式光伏、分散式风电
  RENEWABLE: [SITE_TYPE_CODES.DISTRIBUTED_PV, SITE_TYPE_CODES.DISTRIBUTED_WIND],

  // 其他类：剩余所有类型
  OTHER: [
    SITE_TYPE_CODES.SELF_POWER,
    SITE_TYPE_CODES.ELECTRIC_VEHICLE,
    SITE_TYPE_CODES.CHARGING_STATION,
    SITE_TYPE_CODES.BATTERY_SWAP_STATION,
    SITE_TYPE_CODES.BUILDING_AIR_CONDITIONING,
    SITE_TYPE_CODES.COMMERCIAL_LOAD,
    SITE_TYPE_CODES.NON_ADJUSTABLE_LOAD,
    SITE_TYPE_CODES.OTHER
  ]
};

/**
 * 根据站点类型获取对应的分组
 * @param {number} siteType 站点类型编码
 * @returns {string} 分组名称：'STORAGE' | 'RENEWABLE' | 'OTHER'
 */
export function getSiteTypeGroup(siteType) {
  if (SITE_TYPE_GROUPS.STORAGE.includes(siteType)) {
    return "STORAGE";
  }
  if (SITE_TYPE_GROUPS.RENEWABLE.includes(siteType)) {
    return "RENEWABLE";
  }
  return "OTHER";
}

/**
 * 根据站点类型编码获取站点类型名称
 * @param {number} siteType 站点类型编码
 * @returns {string} 站点类型名称
 */
export function getSiteTypeName(siteType) {
  const option = SITE_TYPE_OPTIONS.find(item => item.value === siteType);
  return option ? option.label : "未知类型";
}

/**
 * 根据资源类型获取允许的站点类型列表
 * @param {number} resourceType 资源类型编码 (1-发电, 2-储电, 3-用电, 4-微电网)
 * @returns {Array} 允许的站点类型编码数组
 */
export function getAllowedSiteTypes(resourceType) {
  return RESOURCE_SITE_RELATION[resourceType] || [];
}

/**
 * 根据资源类型获取推荐的站点类型选项
 * @param {number} resourceType 资源类型编码
 * @returns {Array} 站点类型选项数组
 */
export function getRecommendedSiteTypeOptions(resourceType) {
  const allowedTypes = getAllowedSiteTypes(resourceType);
  return SITE_TYPE_OPTIONS.filter(option =>
    allowedTypes.includes(option.value)
  );
}

/**
 * 检查资源类型是否支持指定的站点类型
 * @param {number} resourceType 资源类型编码
 * @param {number} siteType 站点类型编码
 * @returns {boolean} 是否支持
 */
export function isResourceSiteTypeCompatible(resourceType, siteType) {
  const allowedTypes = getAllowedSiteTypes(resourceType);
  return allowedTypes.includes(siteType);
}

/**
 * 获取资源类型名称
 * @param {number} resourceType 资源类型编码
 * @returns {string} 资源类型名称
 */
export function getResourceTypeName(resourceType) {
  const typeMap = {
    [RESOURCE_TYPE_CODES.GENERATION]: "发电资源",
    [RESOURCE_TYPE_CODES.STORAGE]: "储电资源",
    [RESOURCE_TYPE_CODES.CONSUMPTION]: "用电资源",
    [RESOURCE_TYPE_CODES.MICROGRID]: "微电网资源"
  };
  return typeMap[resourceType] || "未知资源类型";
}
