# 站点类型更新说明

## 更新内容

根据后端枚举定义，更新了站点类型常量名称以保持一致性。

### 更新的常量名称

| 原常量名 | 新常量名 | 编码 | 中文名称 |
|----------|----------|------|----------|
| `BATTERY_SWAP` | `BATTERY_SWAP_STATION` | 5 | 换电站 |
| `BUILDING_AC` | `BUILDING_AIR_CONDITIONING` | 6 | 楼宇空调 |

### 完整的站点类型枚举

```javascript
export const SITE_TYPE_CODES = {
  SELF_POWER: 1,                    // 自备电源
  USER_STORAGE: 2,                  // 用户侧储能
  ELECTRIC_VEHICLE: 3,              // 电动汽车
  CHARGING_STATION: 4,              // 充电站
  BATTERY_SWAP_STATION: 5,          // 换电站
  BUILDING_AIR_CONDITIONING: 6,     // 楼宇空调
  COMMERCIAL_LOAD: 7,               // 工商业可调节负荷
  DISTRIBUTED_PV: 8,                // 分布式光伏
  DISTRIBUTED_WIND: 9,              // 分散式风电
  DISTRIBUTED_STORAGE: 10,          // 分布式独立储能
  NON_ADJUSTABLE_LOAD: 11,          // 非可调节负荷
  OTHER: 99                         // 其他
};
```

## 与后端枚举对应关系

### 后端枚举
```java
public enum SiteTypeEnum {
    SELF_POWER(1, "自备电源"),
    USER_STORAGE(2, "用户侧储能"),
    ELECTRIC_VEHICLE(3, "电动汽车"),
    CHARGING_STATION(4, "充电站"),
    BATTERY_SWAP_STATION(5, "换电站"),
    BUILDING_AIR_CONDITIONING(6, "楼宇空调"),
    COMMERCIAL_LOAD(7, "工商业可调节负荷"),
    DISTRIBUTED_PV(8, "分布式光伏"),
    DISTRIBUTED_WIND(9, "分散式风电"),
    DISTRIBUTED_STORAGE(10, "分布式独立储能"),
    NON_ADJUSTABLE_LOAD(11, "非可调节负荷"),
    OTHER(99, "其他");
}
```

### 前端常量
```javascript
export const SITE_TYPE_CODES = {
  SELF_POWER: 1,                    // ✅ 对应 SELF_POWER
  USER_STORAGE: 2,                  // ✅ 对应 USER_STORAGE
  ELECTRIC_VEHICLE: 3,              // ✅ 对应 ELECTRIC_VEHICLE
  CHARGING_STATION: 4,              // ✅ 对应 CHARGING_STATION
  BATTERY_SWAP_STATION: 5,          // ✅ 对应 BATTERY_SWAP_STATION
  BUILDING_AIR_CONDITIONING: 6,     // ✅ 对应 BUILDING_AIR_CONDITIONING
  COMMERCIAL_LOAD: 7,               // ✅ 对应 COMMERCIAL_LOAD
  DISTRIBUTED_PV: 8,                // ✅ 对应 DISTRIBUTED_PV
  DISTRIBUTED_WIND: 9,              // ✅ 对应 DISTRIBUTED_WIND
  DISTRIBUTED_STORAGE: 10,          // ✅ 对应 DISTRIBUTED_STORAGE
  NON_ADJUSTABLE_LOAD: 11,          // ✅ 对应 NON_ADJUSTABLE_LOAD
  OTHER: 99                         // ✅ 对应 OTHER
};
```

## 资源-站点类型关联关系

### 发电资源 (GENERATION = 1)
- 自备电源 (1)
- 分布式光伏 (8)
- 分散式风电 (9)

### 储电资源 (STORAGE = 2)
- 用户侧储能 (2)
- 分布式独立储能 (10)

### 用电资源 (CONSUMPTION = 3)
- 电动汽车 (3)
- 充电站 (4)
- 换电站 (5)
- 楼宇空调 (6)
- 工商业可调节负荷 (7)
- 非可调节负荷 (11)
- 其他 (99)

### 微电网资源 (MICROGRID = 4)
- 支持所有站点类型 (1-11, 99)

## 站点类型分组

### 储能类 (STORAGE)
- 用户侧储能 (2)
- 分布式独立储能 (10)

### 新能源类 (RENEWABLE)
- 分布式光伏 (8)
- 分散式风电 (9)

### 其他类 (OTHER)
- 自备电源 (1)
- 电动汽车 (3)
- 充电站 (4)
- 换电站 (5)
- 楼宇空调 (6)
- 工商业可调节负荷 (7)
- 非可调节负荷 (11)
- 其他 (99)

## 影响的文件

### 已更新的文件
- `src/utils/siteTypes.js` - 站点类型常量定义

### 可能需要检查的文件
- `src/projects/vpp-resource-manager/resource-config/components/AddSiteDialog.vue`
- `src/projects/vpp-resource-manager/resource-config/components/SiteManagement.vue`
- 其他使用站点类型常量的组件

## 向后兼容性

所有更新都保持了向后兼容性：
- 编码值保持不变
- 中文名称保持不变
- 只更新了常量名称以与后端保持一致
- 所有导出的函数和接口保持不变

## 使用示例

```javascript
import { SITE_TYPE_CODES, getSiteTypeName, getSiteTypeGroup } from '@/utils/siteTypes';

// 使用更新后的常量
const batterySwapStation = SITE_TYPE_CODES.BATTERY_SWAP_STATION; // 5
const buildingAC = SITE_TYPE_CODES.BUILDING_AIR_CONDITIONING;    // 6

// 获取站点类型名称
console.log(getSiteTypeName(5)); // "换电站"
console.log(getSiteTypeName(6)); // "楼宇空调"

// 获取站点类型分组
console.log(getSiteTypeGroup(2));  // "STORAGE"
console.log(getSiteTypeGroup(8));  // "RENEWABLE"
console.log(getSiteTypeGroup(5));  // "OTHER"
```

现在前端的站点类型定义与后端枚举完全一致！
