/**
 * 站点类型关联关系测试脚本
 * 用于验证资源-站点类型关联关系是否正确
 */

import {
  RESOURCE_TYPE_CODES,
  SITE_TYPE_CODES,
  RESOURCE_SITE_RELATION,
  getAllowedSiteTypes,
  getRecommendedSiteTypeOptions,
  isResourceSiteTypeCompatible,
  getResourceTypeName,
  getSiteTypeName
} from './siteTypes.js';

// 测试数据
const testCases = [
  {
    resourceType: RESOURCE_TYPE_CODES.GENERATION,
    expectedSiteTypes: [
      SITE_TYPE_CODES.SELF_POWER,        // 1: 自备电源
      SITE_TYPE_CODES.DISTRIBUTED_PV,    // 8: 分布式光伏
      SITE_TYPE_CODES.DISTRIBUTED_WIND   // 9: 分散式风电
    ]
  },
  {
    resourceType: RESOURCE_TYPE_CODES.STORAGE,
    expectedSiteTypes: [
      SITE_TYPE_CODES.USER_STORAGE,      // 2: 用户侧储能
      SITE_TYPE_CODES.DISTRIBUTED_STORAGE // 10: 分布式独立储能
    ]
  },
  {
    resourceType: RESOURCE_TYPE_CODES.CONSUMPTION,
    expectedSiteTypes: [
      SITE_TYPE_CODES.ELECTRIC_VEHICLE,   // 3: 电动汽车
      SITE_TYPE_CODES.CHARGING_STATION,   // 4: 充电站
      SITE_TYPE_CODES.BATTERY_SWAP,       // 5: 换电站
      SITE_TYPE_CODES.BUILDING_AC,        // 6: 楼宇空调
      SITE_TYPE_CODES.COMMERCIAL_LOAD,    // 7: 工商业可调节负荷
      SITE_TYPE_CODES.NON_ADJUSTABLE_LOAD, // 11: 非可调节负荷
      SITE_TYPE_CODES.OTHER               // 99: 其他
    ]
  },
  {
    resourceType: RESOURCE_TYPE_CODES.MICROGRID,
    expectedSiteTypes: [
      SITE_TYPE_CODES.SELF_POWER,         // 1: 自备电源
      SITE_TYPE_CODES.USER_STORAGE,       // 2: 用户侧储能
      SITE_TYPE_CODES.ELECTRIC_VEHICLE,   // 3: 电动汽车
      SITE_TYPE_CODES.CHARGING_STATION,   // 4: 充电站
      SITE_TYPE_CODES.BATTERY_SWAP,       // 5: 换电站
      SITE_TYPE_CODES.BUILDING_AC,        // 6: 楼宇空调
      SITE_TYPE_CODES.COMMERCIAL_LOAD,    // 7: 工商业可调节负荷
      SITE_TYPE_CODES.DISTRIBUTED_PV,     // 8: 分布式光伏
      SITE_TYPE_CODES.DISTRIBUTED_WIND,   // 9: 分散式风电
      SITE_TYPE_CODES.DISTRIBUTED_STORAGE, // 10: 分布式独立储能
      SITE_TYPE_CODES.NON_ADJUSTABLE_LOAD, // 11: 非可调节负荷
      SITE_TYPE_CODES.OTHER               // 99: 其他
    ]
  }
];

/**
 * 运行测试
 */
export function runSiteTypeTests() {
  console.log('🧪 开始测试资源-站点类型关联关系...\n');

  let allTestsPassed = true;

  testCases.forEach((testCase, index) => {
    const { resourceType, expectedSiteTypes } = testCase;
    const resourceTypeName = getResourceTypeName(resourceType);
    
    console.log(`📋 测试 ${index + 1}: ${resourceTypeName} (类型编码: ${resourceType})`);
    
    // 测试 getAllowedSiteTypes
    const allowedTypes = getAllowedSiteTypes(resourceType);
    const isAllowedTypesCorrect = JSON.stringify(allowedTypes.sort()) === JSON.stringify(expectedSiteTypes.sort());
    
    if (isAllowedTypesCorrect) {
      console.log('  ✅ getAllowedSiteTypes 测试通过');
    } else {
      console.log('  ❌ getAllowedSiteTypes 测试失败');
      console.log('    期望:', expectedSiteTypes.sort());
      console.log('    实际:', allowedTypes.sort());
      allTestsPassed = false;
    }
    
    // 测试 getRecommendedSiteTypeOptions
    const recommendedOptions = getRecommendedSiteTypeOptions(resourceType);
    const recommendedTypeIds = recommendedOptions.map(option => option.value).sort();
    const isRecommendedCorrect = JSON.stringify(recommendedTypeIds) === JSON.stringify(expectedSiteTypes.sort());
    
    if (isRecommendedCorrect) {
      console.log('  ✅ getRecommendedSiteTypeOptions 测试通过');
    } else {
      console.log('  ❌ getRecommendedSiteTypeOptions 测试失败');
      console.log('    期望:', expectedSiteTypes.sort());
      console.log('    实际:', recommendedTypeIds);
      allTestsPassed = false;
    }
    
    // 测试 isResourceSiteTypeCompatible
    let compatibilityTestPassed = true;
    
    // 测试应该兼容的站点类型
    expectedSiteTypes.forEach(siteType => {
      if (!isResourceSiteTypeCompatible(resourceType, siteType)) {
        console.log(`  ❌ 兼容性测试失败: ${resourceTypeName} 应该支持 ${getSiteTypeName(siteType)}`);
        compatibilityTestPassed = false;
        allTestsPassed = false;
      }
    });
    
    // 测试不应该兼容的站点类型（随机选择一些不在列表中的）
    const allSiteTypes = Object.values(SITE_TYPE_CODES);
    const incompatibleTypes = allSiteTypes.filter(siteType => !expectedSiteTypes.includes(siteType));
    
    incompatibleTypes.slice(0, 3).forEach(siteType => {
      if (isResourceSiteTypeCompatible(resourceType, siteType)) {
        console.log(`  ❌ 兼容性测试失败: ${resourceTypeName} 不应该支持 ${getSiteTypeName(siteType)}`);
        compatibilityTestPassed = false;
        allTestsPassed = false;
      }
    });
    
    if (compatibilityTestPassed) {
      console.log('  ✅ isResourceSiteTypeCompatible 测试通过');
    }
    
    // 显示支持的站点类型
    console.log('  📝 支持的站点类型:');
    expectedSiteTypes.forEach(siteType => {
      console.log(`    - ${getSiteTypeName(siteType)} (${siteType})`);
    });
    
    console.log('');
  });

  if (allTestsPassed) {
    console.log('🎉 所有测试通过！资源-站点类型关联关系配置正确。');
  } else {
    console.log('❌ 部分测试失败，请检查配置。');
  }
  
  return allTestsPassed;
}

/**
 * 显示完整的关联关系表
 */
export function showResourceSiteRelationTable() {
  console.log('\n📊 完整的资源-站点类型关联关系表:');
  console.log('┌─────────────────┬─────────────────┬──────────────────────────┐');
  console.log('│ 资源类型编码    │ 站点类型编码    │ 站点类型名称             │');
  console.log('├─────────────────┼─────────────────┼──────────────────────────┤');
  
  Object.entries(RESOURCE_SITE_RELATION).forEach(([resourceType, siteTypes]) => {
    const resourceTypeName = getResourceTypeName(parseInt(resourceType));
    
    siteTypes.forEach((siteType, index) => {
      const siteTypeName = getSiteTypeName(siteType);
      const resourceTypeDisplay = index === 0 ? `${resourceType} (${resourceTypeName})` : '';
      
      console.log(`│ ${resourceTypeDisplay.padEnd(15)} │ ${siteType.toString().padEnd(15)} │ ${siteTypeName.padEnd(24)} │`);
    });
    
    if (siteTypes.length > 0) {
      console.log('├─────────────────┼─────────────────┼──────────────────────────┤');
    }
  });
  
  console.log('└─────────────────┴─────────────────┴──────────────────────────┘');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  runSiteTypeTests();
  showResourceSiteRelationTable();
}
