<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>虚拟电厂Figma布局测试</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont,
          "Segoe UI", sans-serif;
        background: #ffffff;
        color: #13171f;
      }

      .vpp-info-page {
        padding: 24px;
        min-height: 100vh;
      }

      /* 页面头部 */
      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        flex: 1;
      }

      .page-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.5;
        color: #13171f;
        margin: 0;
      }

      .header-controls {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .search-box,
      .filter-select {
        width: 240px;
        height: 32px;
        border: 1px solid #c9cdd4;
        border-radius: 4px;
        padding: 0 8px;
        font-size: 14px;
      }

      .config-btn {
        background: #00b45e;
        border: none;
        color: white;
        padding: 5px 16px;
        border-radius: 3px;
        font-size: 14px;
        cursor: pointer;
      }

      /* 主要内容区域 */
      .main-content {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }

      /* 虚拟电厂基本信息 */
      .vpp-info-section {
        display: flex;
        gap: 16px;
      }

      .vpp-image-container {
        width: 199px;
        height: 258px;
        flex-shrink: 0;
        border-radius: 8px;
        overflow: hidden;
        background: #d9d9d9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
      }

      .vpp-basic-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 24px;
      }

      .info-row {
        display: flex;
        gap: 16px;
      }

      .info-item {
        flex: 1;
        background: #f6f8fa;
        border-radius: 4px;
        padding: 10px;
        min-height: 44px;
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #424e5f;
      }

      .info-item.full-width {
        flex: none;
        width: 100%;
      }

      .info-item.opacity-0 {
        opacity: 0;
      }

      /* 技术参数网格 */
      .tech-params-grid {
        display: grid;
        gap: 16px;
        grid-template-columns: repeat(3, 1fr);
      }

      .param-card {
        background: #f6f8fa;
        border-radius: 4px;
        padding: 16px;
        height: 120px;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .card-title {
        font-size: 14px;
        font-weight: 400;
        color: #424e5f;
        margin: 0;
      }

      .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .param-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .param-label {
        font-size: 14px;
        color: #798492;
      }

      .param-value {
        font-size: 36px;
        font-weight: 500;
        color: #13171f;
      }

      /* 资源统计 */
      .stats-section {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .stats-title {
        font-size: 16px;
        font-weight: 600;
        color: #13171f;
        margin: 0;
      }

      .stats-cards {
        display: flex;
        gap: 16px;
      }

      .stats-card {
        flex: 1;
        background: #f6f8fa;
        border-radius: 4px;
        padding: 16px;
        height: 88px;
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .stats-icon {
        width: 56px;
        height: 56px;
        background: #d9d9d9;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 12px;
      }

      .stats-info {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .stats-label {
        font-size: 14px;
        color: #798492;
        margin-bottom: 4px;
      }

      .stats-value {
        font-size: 28px;
        font-weight: 500;
        color: #13171f;
      }

      /* 响应式 */
      @media (max-width: 768px) {
        .vpp-info-page {
          padding: 16px;
        }
        .page-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 16px;
        }
        .header-left {
          width: 100%;
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }
        .header-controls {
          flex-direction: column;
          width: 100%;
          gap: 12px;
        }
        .search-box,
        .filter-select {
          width: 100%;
        }
        .vpp-info-section {
          flex-direction: column;
        }
        .vpp-image-container {
          width: 100%;
          height: 200px;
        }
        .info-row {
          flex-direction: column;
          gap: 12px;
        }
        .tech-params-grid {
          grid-template-columns: 1fr;
        }
        .stats-cards {
          flex-direction: column;
        }
      }
    </style>
  </head>
  <body>
    <div class="vpp-info-page">
      <!-- 页面标题和操作按钮 -->
      <div class="page-header">
        <div class="header-left">
          <h2 class="page-title">虚拟电厂厂站信息</h2>
          <div class="header-controls">
            <input type="text" class="search-box" placeholder="请输入关键字" />
            <select class="filter-select">
              <option>机组类型</option>
              <option>全部</option>
              <option>发电类</option>
              <option>负荷类</option>
              <option>调节型</option>
              <option>能量型</option>
            </select>
          </div>
        </div>
        <button class="config-btn">配置虚拟电厂</button>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 虚拟电厂基本信息卡片 -->
        <div class="vpp-info-section">
          <!-- 左侧：图片回显 -->
          <div class="vpp-image-container">虚拟电厂图片</div>

          <!-- 右侧：基本信息 -->
          <div class="vpp-basic-info">
            <!-- 第一行 -->
            <div class="info-row">
              <div class="info-item">虚拟电厂名称：--</div>
              <div class="info-item">虚拟电厂所属省份：--</div>
              <div class="info-item">虚拟电厂站类型：--</div>
            </div>
            <!-- 第二行 -->
            <div class="info-row">
              <div class="info-item">虚拟电厂成立时间：--</div>
              <div class="info-item">运营商编号：--</div>
              <div class="info-item opacity-0">虚拟电厂成立时间：--</div>
            </div>
            <!-- 第三行 -->
            <div class="info-row">
              <div class="info-item full-width">虚拟电厂厂站类型说明：--</div>
            </div>

            <!-- 技术参数卡片区域 -->
            <div class="tech-params-container">
              <div class="tech-params-grid">
                <!-- 需求响应卡片 -->
                <div class="param-card">
                  <h4 class="card-title">需求响应</h4>
                  <div class="card-content">
                    <div class="param-row">
                      <div class="param-item">
                        <span class="param-label">申报价格上限（元/MWh）</span>
                        <span class="param-value">--</span>
                      </div>
                    </div>
                    <div class="param-row">
                      <div class="param-item">
                        <span class="param-label">申报价格下限（元/MWh）</span>
                        <span class="param-value">--</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 调峰卡片 -->
                <div class="param-card">
                  <h4 class="card-title">调峰</h4>
                  <div class="card-content">
                    <div class="param-row">
                      <div class="param-item">
                        <span class="param-label">申报价格上限（元/MWh）</span>
                        <span class="param-value">--</span>
                      </div>
                    </div>
                    <div class="param-row">
                      <div class="param-item">
                        <span class="param-label">申报价格下限（元/MWh）</span>
                        <span class="param-value">--</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 调频卡片 -->
                <div class="param-card">
                  <h4 class="card-title">调频</h4>
                  <div class="card-content">
                    <div class="param-row">
                      <div class="param-item">
                        <span class="param-label">申报价格上限（元/MWh）</span>
                        <span class="param-value">--</span>
                      </div>
                    </div>
                    <div class="param-row">
                      <div class="param-item">
                        <span class="param-label">申报价格下限（元/MWh）</span>
                        <span class="param-value">--</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 虚拟电厂资源统计 -->
        <div class="stats-section">
          <div class="stats-header">
            <h3 class="stats-title">虚拟电厂资源统计</h3>
          </div>
          <div class="stats-cards">
            <div class="stats-card">
              <div class="stats-icon">图标</div>
              <div class="stats-info">
                <div class="stats-label">接入企业/用户数量（家）</div>
                <div class="stats-value">--</div>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon">图标</div>
              <div class="stats-info">
                <div class="stats-label">聚合资源数量（个）</div>
                <div class="stats-value">--</div>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon">图标</div>
              <div class="stats-info">
                <div class="stats-label">可调节容量（MW）</div>
                <div class="stats-value">--</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
