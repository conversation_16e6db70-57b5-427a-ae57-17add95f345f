<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SVG卡片布局测试</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont,
          "Segoe UI", sans-serif;
        background: #ffffff;
        color: #13171f;
        padding: 24px;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
      }

      h1 {
        margin-bottom: 24px;
        font-size: 24px;
        color: #13171f;
      }

      .cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, 443px);
        gap: 16px;
        justify-content: flex-start;
        margin-bottom: 32px;
      }

      .param-card {
        width: 443px;
        height: 121px;
        background: #f6f8fa;
        border-radius: 4px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        position: relative;
      }

      .card-title {
        font-family: "PingFang SC", sans-serif;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: #424e5f;
        margin: 0 0 12px 0;
      }

      .card-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 24px;
        flex: 1;
      }

      .param-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .param-label {
        font-family: "PingFang SC", sans-serif;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #798492;
      }

      .param-value {
        font-family: "PingFang SC", sans-serif;
        font-size: 36px;
        font-weight: 500;
        line-height: 46px;
        color: #13171f;
        font-stretch: 200.5%;
      }

      .layout-demo {
        margin-bottom: 32px;
      }

      .layout-title {
        font-size: 18px;
        margin-bottom: 16px;
        color: #13171f;
      }

      /* 响应式设计 */
      @media (max-width: 1400px) {
        .cards-grid {
          grid-template-columns: repeat(2, 443px);
        }
      }

      @media (max-width: 950px) {
        .param-card {
          width: 100%;
          max-width: 443px;
        }

        .cards-grid {
          grid-template-columns: 1fr;
          justify-content: stretch;
        }
      }

      @media (max-width: 768px) {
        body {
          padding: 16px;
        }

        .param-card {
          width: 100%;
          height: auto;
          min-height: 121px;
        }

        .card-content {
          flex-direction: column;
          gap: 16px;
        }

        .param-value {
          font-size: 28px;
          line-height: 36px;
          font-stretch: 200.5%;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>SVG卡片布局测试</h1>

      <div class="layout-demo">
        <h2 class="layout-title">3个卡片布局</h2>
        <div class="cards-grid">
          <div class="param-card">
            <h4 class="card-title">需求响应</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">--</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">--</span>
              </div>
            </div>
          </div>

          <div class="param-card">
            <h4 class="card-title">调峰</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">--</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">--</span>
              </div>
            </div>
          </div>

          <div class="param-card">
            <h4 class="card-title">调频</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">--</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">--</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="layout-demo">
        <h2 class="layout-title">5个卡片布局（包含调节和能量）</h2>
        <div class="cards-grid">
          <div class="param-card">
            <h4 class="card-title">需求响应</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">500</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">100</span>
              </div>
            </div>
          </div>

          <div class="param-card">
            <h4 class="card-title">调峰</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">800</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">200</span>
              </div>
            </div>
          </div>

          <div class="param-card">
            <h4 class="card-title">调频</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">1200</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">300</span>
              </div>
            </div>
          </div>

          <div class="param-card">
            <h4 class="card-title">调节</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">1000</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">250</span>
              </div>
            </div>
          </div>

          <div class="param-card">
            <h4 class="card-title">能量</h4>
            <div class="card-content">
              <div class="param-item">
                <span class="param-label">申报价格上限（元/MWh）</span>
                <span class="param-value">600</span>
              </div>
              <div class="param-item">
                <span class="param-label">申报价格下限（元/MWh）</span>
                <span class="param-value">150</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        style="
          margin-top: 32px;
          padding: 16px;
          background: #f0f0f0;
          border-radius: 8px;
        "
      >
        <h3>设计规范</h3>
        <ul style="margin-top: 8px; padding-left: 20px">
          <li>卡片尺寸：443px × 121px</li>
          <li>背景色：#F6F8FA</li>
          <li>圆角：4px</li>
          <li>内边距：16px</li>
          <li>标题字体：14px，颜色 #424E5F</li>
          <li>标签字体：14px，颜色 #798492</li>
          <li>数值字体：36px，字体拉伸 200.5%，颜色 #13171F</li>
          <li>两个参数项水平排列，各占50%宽度</li>
        </ul>
      </div>
    </div>
  </body>
</html>
