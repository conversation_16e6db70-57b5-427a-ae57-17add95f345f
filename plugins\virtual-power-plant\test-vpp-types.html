<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟电厂类型动态布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-controls {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .test-controls h3 {
            margin-top: 0;
            color: #333;
        }
        .type-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .type-btn {
            padding: 8px 16px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .type-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .type-btn:hover {
            border-color: #007bff;
        }
        .grid-demo {
            display: grid;
            gap: 16px;
            margin-top: 20px;
        }
        .grid-3-cards { grid-template-columns: repeat(3, 1fr); }
        .grid-4-cards { grid-template-columns: repeat(2, 1fr); }
        .grid-5-cards { grid-template-columns: repeat(3, 1fr); }
        
        .card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }
        .param-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .param-label {
            color: #666;
        }
        .param-value {
            color: #333;
            font-weight: 500;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        
        @media (max-width: 1200px) {
            .grid-3-cards, .grid-4-cards, .grid-5-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .grid-3-cards, .grid-4-cards, .grid-5-cards {
                grid-template-columns: 1fr;
            }
            .type-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>虚拟电厂类型动态布局测试</h1>
        
        <div class="test-controls">
            <h3>选择虚拟电厂类型（支持多选）：</h3>
            <div class="type-buttons">
                <button class="type-btn" data-type="1">发电类</button>
                <button class="type-btn" data-type="2">负荷类</button>
                <button class="type-btn active" data-type="3">调节型</button>
                <button class="type-btn active" data-type="4">能量型</button>
            </div>
            
            <div class="info">
                <strong>当前配置：</strong>
                <div id="current-config">调节型 + 能量型 = 5个卡片（3列布局）</div>
            </div>
        </div>
        
        <div id="grid-container" class="grid-demo grid-5-cards">
            <!-- 基础三个卡片 -->
            <div class="card">
                <h4>需求响应</h4>
                <div class="param-item">
                    <span class="param-label">申报价格上限（元/MWh）：</span>
                    <span class="param-value">500.00</span>
                </div>
                <div class="param-item">
                    <span class="param-label">申报价格下限（元/MWh）：</span>
                    <span class="param-value">100.00</span>
                </div>
            </div>
            
            <div class="card">
                <h4>调峰</h4>
                <div class="param-item">
                    <span class="param-label">申报价格上限（元/MWh）：</span>
                    <span class="param-value">800.00</span>
                </div>
                <div class="param-item">
                    <span class="param-label">申报价格下限（元/MWh）：</span>
                    <span class="param-value">200.00</span>
                </div>
            </div>
            
            <div class="card">
                <h4>调频</h4>
                <div class="param-item">
                    <span class="param-label">申报价格上限（元/MWh）：</span>
                    <span class="param-value">1200.00</span>
                </div>
                <div class="param-item">
                    <span class="param-label">申报价格下限（元/MWh）：</span>
                    <span class="param-value">300.00</span>
                </div>
            </div>
            
            <!-- 调节卡片 -->
            <div class="card" id="regulation-card">
                <h4>调节</h4>
                <div class="param-item">
                    <span class="param-label">申报价格上限（元/MWh）：</span>
                    <span class="param-value">1000.00</span>
                </div>
                <div class="param-item">
                    <span class="param-label">申报价格下限（元/MWh）：</span>
                    <span class="param-value">250.00</span>
                </div>
            </div>
            
            <!-- 能量卡片 -->
            <div class="card" id="energy-card">
                <h4>能量</h4>
                <div class="param-item">
                    <span class="param-label">申报价格上限（元/MWh）：</span>
                    <span class="param-value">600.00</span>
                </div>
                <div class="param-item">
                    <span class="param-label">申报价格下限（元/MWh）：</span>
                    <span class="param-value">150.00</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 虚拟电厂类型状态
        let vppTypes = [3, 4]; // 初始包含调节型和能量型
        
        // 类型名称映射
        const typeNames = {
            1: '发电类',
            2: '负荷类', 
            3: '调节型',
            4: '能量型'
        };
        
        // 更新显示
        function updateDisplay() {
            const totalCards = 3 + (vppTypes.includes(3) ? 1 : 0) + (vppTypes.includes(4) ? 1 : 0);
            let gridClass = '';
            
            switch(totalCards) {
                case 3: gridClass = 'grid-3-cards'; break;
                case 4: gridClass = 'grid-4-cards'; break;
                case 5: gridClass = 'grid-5-cards'; break;
            }
            
            // 更新网格样式
            const container = document.getElementById('grid-container');
            container.className = `grid-demo ${gridClass}`;
            
            // 显示/隐藏卡片
            document.getElementById('regulation-card').style.display = vppTypes.includes(3) ? 'block' : 'none';
            document.getElementById('energy-card').style.display = vppTypes.includes(4) ? 'block' : 'none';
            
            // 更新配置信息
            const selectedTypes = vppTypes.map(type => typeNames[type]).join(' + ');
            const layoutDesc = totalCards === 3 ? '3列布局' : totalCards === 4 ? '2列布局' : '3列布局';
            document.getElementById('current-config').textContent = 
                `${selectedTypes || '无'} = ${totalCards}个卡片（${layoutDesc}）`;
        }
        
        // 绑定按钮事件
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const type = parseInt(btn.dataset.type);
                const index = vppTypes.indexOf(type);
                
                if (index > -1) {
                    vppTypes.splice(index, 1);
                    btn.classList.remove('active');
                } else {
                    vppTypes.push(type);
                    btn.classList.add('active');
                }
                
                updateDisplay();
            });
        });
        
        // 初始化显示
        updateDisplay();
    </script>
</body>
</html>
