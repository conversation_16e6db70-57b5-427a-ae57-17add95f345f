# cet-chart 组件强制使用与模板规范（AI 自动生成专用）【优化版】

## 0. 优先使用 cet-chart 的强制要求

- 凡出现 ECharts 图形类需求，**第一时间必须优先使用 cet-chart**，除非明确指定使用 cet-graph 或 omega-trend。
- **当出现“echarts 图形”或类似字眼时，必须使用的是 cet-chart，而不是 echarts。**

## 1. 组件强制要求

- 本项目所有 ECharts 相关功能，**必须**通过 cet-chart 组件（如 `<CetChart>` 或 `CetChartFactory`）实现。
- **禁止**直接引用、操作 echarts 实例、API 或其原生组件（如 `import echarts`、`echarts.init`、`new echarts` 等）。
- 包括但不限于图表渲染、主题切换、地图注册等，均应通过 cet-chart 提供的接口完成。

## 2. 模板层用法与变量命名规范

- 模板层必须使用如下写法：
  ```vue
  <CetChart v-bind="CetChart_英文名称" />
  ```
- **禁止在脚本中 import CetChart from 'cet-chart'，也禁止在 components 注册，直接在模板层使用 <CetChart ... />。**
- 其中 `CetChart_英文名称` 为 data/computed/methods/setup 中定义的对象，命名规则为 `CetChart_` + 图形功能英文名（如 VoltageWaveRecording、FaultTypeBar、DeviceProblemPie 等）。
- 如有多个图表，分别命名为 CetChart*英文名称 1、CetChart*英文名称 2 等。

## 3. options 配置要求（重点优化）

- `CetChart_英文名称` 对象结构如下：
  ```js
  CetChart_英文名称 = {
    inputData_in: null, // 如有数据源则传，否则为 null
    options: {
      /* ECharts 配置对象，按原型图片或需求定制 */
    }
  };
  ```
- options 属性必须为标准 ECharts 配置，类型、坐标轴、series、grid 等均需根据原型图片或需求定制。
- **无论是否有数据，options 的结构和所有参数都必须完整配置。**
  - 即使数据为空（如 data: []），也必须初始化 options，确保包含所有必要的结构（如 title、legend、xAxis、yAxis、series 等），避免初始渲染异常或图形区域为空白。
  - 示例（无数据时）：
    ```js
    CetChart_Example = {
      inputData_in: null,
      options: {
        title: { text: $T("示例图表") },
        legend: { data: [] },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value" },
        series: [{ name: $T("示例"), type: "bar", data: [] }]
      }
    };
    ```
- 禁止仅在有数据时才生成 options，必须在组件初始化时就配置好完整结构。

## 4. 常见错误与反例

### 4.1 错误示例

- **错误写法 1：仅在有数据时才配置 options**
  ```js
  if (data.length) {
    CetChart_Example = {
      options: {
        /* ...完整配置... */
      }
    };
  }
  // 没有数据时未配置 options，导致图表渲染异常
  ```
- **错误写法 2：options 结构不完整**
  ```js
  CetChart_Example = {
    options: {
      series: []
      // 缺少 title、legend、xAxis、yAxis 等
    }
  };
  ```

### 4.2 正确示例

- **无论有无数据，options 结构都完整**
  ```js
  CetChart_Example = {
    inputData_in: null,
    options: {
      title: { text: $T("示例图表") },
      legend: { data: [] },
      xAxis: { type: "category", data: [] },
      yAxis: { type: "value" },
      series: [{ name: $T("示例"), type: "bar", data: [] }]
    }
  };
  ```

## 5. 代码检查建议

- 推荐在代码评审或自动化检测中，检查 `CetChart_` 相关对象的 `options` 字段，确保其结构完整且初始化时已配置所有必要参数。
- 检查模板 `<CetChart v-bind="CetChart_英文名称" />` 是否对应 data/computed/methods/setup 中的对象，且对象结构符合规范。

## 6. 业务开发建议

- **初始化时即配置好 options**，后续仅通过响应式数据（如 series.data）动态更新数据部分，避免因 options 结构变化导致的渲染异常。
- **多图表场景**：每个图表都应有独立的 `CetChart_英文名称` 对象，且 options 结构独立、完整。
- **主题切换、空数据、加载中等场景**，options 结构都必须完整，series.data 允许为空数组，但不能缺失 series 配置。

## 7. FAQ

- **Q: 没有数据时，图表会不会显示异常？**  
  A: 只要 options 结构完整，series.data 为空数组时，ECharts 会正常渲染空图表区域，不会报错或异常。
- **Q: 可以只传 series 吗？**  
  A: 不可以。必须包含 title、legend、xAxis、yAxis、series 等完整结构。
- **Q: 可以直接 import echarts 吗？**  
  A: 严禁。所有 ECharts 相关操作必须通过 cet-chart 组件完成

=
