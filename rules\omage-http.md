# omega-http 使用规则

## 1. HTTP 请求唯一入口

项目中所有 HTTP/HTTPS 请求（包括 GET、POST、PUT、DELETE、PATCH 等）必须通过 omega-http 提供的 http、httping 实例或 OmegaHttpPlugin 进行发起。严禁直接使用 axios、fetch、XMLHttpRequest 或其他第三方 HTTP 客户端。

## 2. 禁止绕过拦截器与全局处理

不允许通过自定义 axios 实例、fetch 等方式绕过 omega-http 的全局拦截器、token 注入、国际化 header、loading、错误处理等机制。

## 3. 禁止设置 `headers` 中的 `Use-ID` 和 `X-Auth-Tenant` 参数

禁止在调用接口时设置 `headers` 中的 `Use-ID` 和 `X-Auth-Tenant` 参数

## 4. 禁止提示错误信息

禁止在接口请求失败时，弹出错误提示框。

## 5. 无需进行错误处理

在调用接口时，无需在接口调用失败时，进行错误信息输出等操作

## 6. 在页面中处理接口返回值时，严格遵循返回值规则，接口返回值示例：

```js
response = {
  code: 0, // 状态码，0为正常调用，其他为不正常调用，用于判断接口是否调用成功
  data: {}, // 接口返回的具体数据，用于在页面中展示具体数据
  msg: "", // 成功/失败信息
  total: 0 // 数据总条数，可用于分页器展示总页数
};
```
