# omega-theme 主题样式规则

## 1. 获取变量

- 检索 `@omega/theme` 包中的内容，获取到所有 css 变量以及 elementui 组件的样式，以便在页面生成过程中使用

## 2. 必须使用变量

- 涉及到修改样式时，必须使用 `@omega/theme` 包中定义的 css 变量（如 `var(--BG1)`、`var(--T1)` 等），禁止直接写死颜色、间距、字体、阴影等样式。

- 当获取到的 UI 图中的样式无法使用变量满足时，寻找相近变量，禁止出现硬编码值

## 3. 可使用 taiwindcss 工具类

- 可以根据 tailwind.config.js 中的配置，使用 tailwindcss 工具类来进行快速样式编写，如 `bg-BG1`、`text-T1`、`border-B1`、`p-J1`、 `text-Sta3`等，涉及到设置具体样式值的，必须使用变量，禁止直接使用例如 `bg-#fff`,`text-14` 等的硬编码值，必须使用变量 `bg-BG1`、`text-Aa` 等。

## 4. 主题切换兼容

- 所有用变量实现的样式，均自动适配 `[data-theme]` 主题切换，无需手动切换类名。

## 5. 组件样式

- 禁止单独在页面中修改组件的样式

## 6. 示例

```html
<input class="bg-BG1 border-B1 p-J1 text-Sta3" />
```

```css
.btn-primary {
  background-color: var(--ZS);
  color: var(--T5);
  padding: var(--J1) var(--J2);
  border-radius: 4px;
  box-shadow: var(--S1);
}
```
