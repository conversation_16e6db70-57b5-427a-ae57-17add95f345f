# 生成接口函数规则

## 1. 执行规则

当识别到`生成接口函数`功能时，执行下方的规则来生成对应的 api 文件

## 2. 每个功能的 api 文件单独存放

为每个功能新增一个 api 文件夹，放在 `src/api` 下，根据获取的中文目录名自动生成英文目录名，如果生成的名字之前已经存在，那么生成的英文目录名后面加数字，如 src/api/test/index.js 和 src/api/test1/index.js。

## 3. 每个 api 方法命名不可重复，为每个 api 方法，生成对应注释，

每个 api 方法命名不可重复，为每个 api 方法，生成对应注释，需要标明明该方法的作用、参数具体信息、返回值具体信息，保证在页面中调用时能够准确设置入参，并对返回值进行正确的处理

## 4. 导出接口使用 `@omega/http` 中的 `download` 方法

如果遇到导出接口，禁止手动进行文件下载处理，请使用 `@omega/http` 中的 `download` 方法进行下载。

## 5. 禁止设置 `headers` 中的 `Use-ID` 和 `X-Auth-Tenant` 参数

禁止在生成 api 文件时，为 api 函数添加 `Use-ID` 和 `X-Auth-Tenant` 参数

## 6. 示例

```js
import { httping, download } from "@omega/http";

/**
 * 根据条件查询危险辨识风险评价控制列表
 * data: 查询参数
 */
export function queryIdentification(data) {
  return httping({
    url: `/powercloud/electricticket/api/identification/list/param`,
    method: "POST",
    data
  });
}

/**
 * 导出危险辨识风险评价控制
 * data: 导出参数
 */
export function exportIdentification(data) {
  return download(`/powercloud/electricticket/api/identification/export`, {
    data,
    method: "POST"
  });
}
```
