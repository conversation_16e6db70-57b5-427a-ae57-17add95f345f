# 目录、路由、菜单项自动生成规则（最新版）

## 1. 执行规则

当识别到`生成目录`功能时，执行下方的规则来生成对应的目录、路由、菜单项

## 2. 生成目录

根据 `src/api` 中的文件夹，过滤掉`文件管理`、`公共`相关的文件夹，之后在 src/projects 中生成对应的文件夹，并生成对应的 `index.vue` 文件，并引入对应的接口，禁止生成具体页面功能;

## 3. 生成路由文件

根据 `projects` 中的文件夹，生成对应的路由，并添加到 `src/router/index.js` 中;

```js
  {
    path: "/test",
    name: "test", // 测试1
    component: () => import("./test/index.vue")
  }
// ...更多子页面
```

## 4. 生成菜单配置项

1. 根据路由配置，生成对应的菜单项，并添加到 `public/config.js` 中的 `navmenu` 属性中，如果 `navmenu` 中已有父级目录，那么仅需生成子级目录即可，不用生成父级目录，如果没有父级目录，那么需要生成一个父级目录，在该父级目录下生成子级目录
2. 在 `public/config.js` 中的 `i18n.en` 中，添加对应的翻译，将已添加的菜单项的 `name` 属性值作为 key，`value` 为对应的翻译。

```js
  {
    label: "文件",
    type: "menuItem",
    category: "project",
    location: "/file",
    permission: "file"
  }
```

- `label` 属性值为当前页面名称，由文件夹名称翻译获得。
- `type` 为菜单项类型，可选值有：`menuItem`、`subMenu`，菜单为父级菜单时，type 值为 `subMenu`，为子菜单时，type 值为 `menuItem`
- `category` 为菜单项类别，可选值有：`project`、`platform`,表示页面属于平台层还是项目层，默认生成的在项目层
- `location` 为菜单项的路由，对应 `src/router/index.js` 中的路由配置中的 `path` 属性
- `permission`: 为菜单项的页面权限，一般为对应 `location` 属性去掉前面的/

## 5. 示例

1. 读取到 src/api 下有一个 file 文件夹，在 src/projects 下生成对应的 file 文件夹，并生成 index.vue 文件，引入 src/api 下 file 文件夹中的接口

2. 在 src/router 中生成路由

```js
const appRoutes = [
  {
    path: "/file",
    name: "file", // 测试1
    component: () => import("./file/index.vue")
  }
];

export default {
  routes: appRoutes
};
// ...更多子页面
```

3. 在 public/config.js 中生成对应的菜单配置项:

```js
__registeAppConfig.setAppconfig({
  navmenu: [
    {
      label: "示例",
      icon: "data-anlysis-one-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "文件",
          category: "project",
          type: "menuItem",
          location: "/file",
          permission: "file"
        }
      ]
    }
  ],
  i18n: {
    en: {
      文件: "File"
    }
  }
});
```
