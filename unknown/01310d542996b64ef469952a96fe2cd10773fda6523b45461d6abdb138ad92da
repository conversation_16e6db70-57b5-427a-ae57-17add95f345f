<template>
  <div class="flex-row flex">
    <el-radio-group v-model="ElSelect_1.value" v-on="ElSelect_1.event">
      <el-radio-button
        v-for="item in timeOptions"
        :label="item.id"
        :key="item.id"
      >
        {{ item.name }}
      </el-radio-button>
    </el-radio-group>
    <el-button
      class="fl ml-J3 mr-J0"
      plain
      icon="el-icon-arrow-left"
      @click="queryPrv"
    ></el-button>
    <el-date-picker
      size="small"
      :clearable="false"
      class="fl"
      style="width: 150px !important"
      v-model="value"
      :picker-options="pickerOptions"
      :type="type"
      :placeholder="$T('选择日期')"
    ></el-date-picker>
    <el-time-select
      v-if="showTimeSelect"
      class="timeSelect fl ml-J0"
      style="width: 100px"
      v-model="timeSelectValue"
      :clearable="false"
      :placeholder="$T('请选择时间')"
      :picker-options="timePickerOptions"
    ></el-time-select>
    <el-button
      class="fl ml-J0"
      plain
      icon="el-icon-arrow-right"
      @click="queryNext"
    ></el-button>
  </div>
</template>
<script>
export default {
  data() {
    return {
      ElSelect_1: {
        value: {},
        style: {
          width: "160px"
        },
        event: {
          change: this.ElSelect_1_change_out
        }
      },
      timeOptions: [
        {
          id: 17,
          name: $T("年"),
          format: "year",
          unit: "year",
          text: $T("今年")
        },
        {
          id: 14,
          name: $T("月"),
          format: "month",
          unit: "month",
          text: $T("当月")
        },
        {
          id: 12,
          name: $T("日"),
          format: "date",
          unit: "day",
          text: $T("今天")
        },
        {
          id: 7,
          name: $T("时"),
          format: "date",
          unit: "hour",
          text: $T("今天")
        }
      ],
      showTimeSelect: false,
      value: new Date(),
      timeSelectValue: "00:00",
      pickerOptions: {
        shortcuts: [
          {
            text: this.$T("今天"),
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          }
        ]
      },
      timePickerOptions: {
        start: "00:00",
        step: "01:00",
        end: "23:00",
        format: "HH"
      },
      type: "date",
      unit: "day",
      cycle: 12
    };
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(val, oldVal) {
        if (this.cycle === 7) {
          //时
          let date1 =
            this.$moment(val).format("YYYY-MM-DD") + " " + this.timeSelectValue;
          let startTime = this.$moment(date1).valueOf();
          let endTime = this.$moment(startTime).endOf(this.unit).valueOf() + 1;
          this.$emit("date_out", [startTime, endTime], this.cycle);
        } else {
          let startTime = this.$moment(val).startOf(this.unit).valueOf();
          let endTime = this.$moment(val).endOf(this.unit).valueOf() + 1;
          this.$emit("date_out", [startTime, endTime], this.cycle);
        }
      }
    },
    timeSelectValue: {
      deep: true,
      handler(val, oldVal) {
        let date1 = this.$moment(this.value).format("YYYY-MM-DD") + " " + val;
        let startTime = this.$moment(date1).valueOf();
        let endTime = this.$moment(startTime).endOf(this.unit).valueOf() + 1;
        this.$emit("date_out", [startTime, endTime], this.cycle);
      }
    }
  },
  methods: {
    ElSelect_1_change_out(id) {
      const val = this.timeOptions.find(item => item.id === id);
      this.cycle = val.id;
      this.type = val.format;
      this.unit = val.unit;
      this.pickerOptions.shortcuts[0].text = val.text;

      if (val.id === 7) {
        this.showTimeSelect = true;
      } else {
        this.showTimeSelect = false;
      }

      if (this.cycle === 7) {
        //时
        let date1 =
          this.$moment(this.value).format("YYYY-MM-DD") +
          " " +
          this.timeSelectValue;
        let startTime = this.$moment(date1).valueOf();
        let endTime = this.$moment(startTime).endOf(this.unit).valueOf() + 1;
        this.$emit("date_out", [startTime, endTime], this.cycle);
      } else {
        let startTime = this.$moment(this.value).startOf(this.unit).valueOf();
        let endTime = this.$moment(this.value).endOf(this.unit).valueOf() + 1;
        this.$emit("date_out", [startTime, endTime], this.cycle);
      }
    },
    queryPrv() {
      if (this.cycle === 7) {
        //时
        let date1 =
          this.$moment(this.value).format("YYYY-MM-DD") +
          " " +
          this.timeSelectValue;
        let value1 = this.$moment(date1);
        let prv = value1.subtract(1, this.unit).valueOf();
        this.value = prv;
        this.timeSelectValue = this.$moment(prv).format("HH:mm");
      } else {
        var date = this.$moment(this.value);
        this.value = date.subtract(1, this.unit).valueOf();
      }
    },
    queryNext() {
      if (this.cycle === 7) {
        //时
        let date1 =
          this.$moment(this.value).format("YYYY-MM-DD") +
          " " +
          this.timeSelectValue;
        let value1 = this.$moment(date1);
        let next = value1.add(1, this.unit).valueOf();
        this.value = next;
        this.timeSelectValue = this.$moment(next).format("HH:mm");
      } else {
        var date = this.$moment(this.value);
        this.value = date.add(1, this.unit).valueOf();
      }
    }
  },
  mounted() {
    this.ElSelect_1.value = 12;
  }
};
</script>
<style lang="scss" scoped></style>
