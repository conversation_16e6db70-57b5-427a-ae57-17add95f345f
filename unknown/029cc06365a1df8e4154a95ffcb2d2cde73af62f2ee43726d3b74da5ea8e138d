<template>
  <div class="dialogUpload">
    <CetDialog
      class="CetDialog"
      v-bind="CetDialog_1"
      v-on="CetDialog_1.event"
      :title="dialogTitle"
      width="600px"
    >
      <slot name="search" />
      <div class="uploadBox bg-BG1 rounded-[Ra]">
        <el-upload
          class="box-upload"
          drag
          ref="upload"
          action=""
          :on-change="uploadChange"
          :http-request="httpRequest"
          :before-remove="fileRemove"
          :auto-upload="false"
          :multiple="false"
          :file-list="fileList"
        >
          <div class="uploadWrap mb-J2">
            <i class="el-icon-upload icon"></i>
            <div class="explain">
              {{ $T("将文件拖到此处，或") }}
              <span class="ZS">{{ $T("点击上传") }}</span>
            </div>
            <div class="extension">
              {{
                $T("只能上传{0}文件,且不超过{1}M", extensionName, uploadDocSize)
              }}
            </div>
            <CetButton
              class="mt-J2"
              @click.stop="download"
              v-permission="downloadPermission"
              v-if="!hideDownload"
              v-bind="CetButton_down"
              v-on="CetButton_down.event"
            ></CetButton>
          </div>
        </el-upload>
      </div>
      <span slot="footer">
        <span class="fl ml-J1" v-if="maxFlinkCount">
          {{ `${$T("最大支持导入数据量{0}条", this.maxFlinkCount)}` }}
        </span>
        <CetButton
          v-bind="CetButton_cancel"
          v-on="CetButton_cancel.event"
        ></CetButton>
        <CetButton
          class="ml-J1"
          v-bind="CetButton_confirm"
          v-on="CetButton_confirm.event"
        ></CetButton>
      </span>
    </CetDialog>
  </div>
</template>
<script>
export default {
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    initTrigger_in: {
      type: Number
    },
    // 支持扩展名，必传
    extensionNameList_in: {
      type: Array
    },
    // 是否需要下载模板按钮
    hideDownload: {
      type: Boolean
    },
    dialogTitle: {
      type: String,
      default: () => {
        return $T("导入");
      }
    },
    downloadPermission: {
      type: String
    },
    maxFlinkCount: {
      type: Number
    }
  },
  computed: {
    extensionName() {
      return this.extensionNameList_in.join(",");
    },
    systemCfg() {
      return this.$store.state.systemCfg;
    },
    uploadDocSize() {
      return 10;
    }
  },
  data() {
    return {
      CetDialog_1: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        event: {},
        showClose: true
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: true,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_down: {
        visible_in: true,
        disable_in: false,
        title: $T("点击下载模板"),
        plain: true,
        event: {}
      },
      fileList: []
    };
  },
  watch: {
    openTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.$nextTick(() => {
        this.init();
      });
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    initTrigger_in() {
      this.init();
    }
  },
  methods: {
    init() {
      this.$refs.upload && this.$refs.upload.clearFiles();
      this.fileList = [];
      this.CetButton_confirm.disable_in = true;
    },
    uploadChange(file, fileList) {
      let verifyExtensionName = false;
      this.extensionNameList_in.forEach(item => {
        if (file.name.indexOf(item) != -1) {
          verifyExtensionName = true;
        }
      });
      if (!verifyExtensionName) {
        this.$message({
          type: "warning",
          message: $T("只能上传{0}格式文件", this.extensionName)
        });
        this.CetButton_confirm.disable_in = true;
        this.fileList = [];
        this.$refs.upload.clearFiles();
        return false;
      }
      const isLimit100M = file.size / 1024 / 1024 < this.uploadDocSize;
      if (!isLimit100M) {
        this.CetButton_confirm.disable_in = true;
        this.$message.error($T("上传文件超过规定的最大上传大小"));
        this.fileList = [];
        this.$refs.upload.clearFiles();
        return false;
      }
      if (fileList && fileList.length > 0) {
        this.fileList = [file];
        this.CetButton_confirm.disable_in = false;
      } else {
        this.fileList = [];
        this.CetButton_confirm.disable_in = true;
      }
    },
    fileRemove() {
      this.CetButton_confirm.disable_in = true;
    },
    httpRequest(val) {
      this.$emit("uploadFile", val);
    },
    download() {
      this.$emit("download");
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out() {
      this.$refs.upload.submit();
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    background-color: var(--BG);
    padding: var(--J1);
  }
  .uploadBox {
    min-height: 300px;
    padding-top: 60px;
    box-sizing: border-box;
    .box-upload {
      width: max-content;
      margin: 0 auto;
      display: block;
      box-sizing: border-box;
      :deep(.el-upload.el-upload--text),
      :deep(.el-upload-dragger) {
        background-color: var(--BG1);
        border-color: var(--B1);
        border-radius: 8px;
        padding: 0 10px;
        width: max-content;
        height: 100%;
        &:hover {
          border-color: var(--ZS);
        }
      }
      .uploadWrap {
        .icon {
          margin-top: var(--J2);
        }
        .explain {
          color: var(--T3);
          .ZS {
            color: var(--ZS);
          }
        }
        .extension {
          color: var(--T4);
          font-size: var(--Ab);
        }
      }
    }
  }
}
</style>
