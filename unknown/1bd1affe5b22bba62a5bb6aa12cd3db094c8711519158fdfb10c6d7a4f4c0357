import * as echarts from "echarts";

export const lightColorList = [
  "rgba(41, 176, 97, 1)",
  "rgba(64, 128, 255, 1)",
  "rgba(255, 193, 5, 1)",
  "rgba(61, 197, 255, 1)",
  "rgba(14, 66, 210, 1)",
  "rgba(255, 157, 9, 1)",
  "rgba(145, 185, 112, 1)",
  "rgba(71, 139, 17, 1)"
];
export const darkColorList = [
  "rgba(13, 134, 255, 1)",
  "rgba(5, 249, 116, 1)",
  "rgba(255, 201, 10, 1)",
  "rgba(97, 236, 255, 1)",
  "rgba(25, 61, 255, 1)",
  "rgba(251, 135, 0, 1)",
  "rgba(177, 231, 65, 1)",
  "rgba(113, 166, 0, 1)"
];

export const lightAreaColorList = [
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(41, 176, 97, 0.2)" },
    { offset: 1, color: "rgba(41, 176, 97, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(64, 128, 255, 0.2)" },
    { offset: 1, color: "rgba(64, 128, 255, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(255, 193, 5, 0.2)" },
    { offset: 1, color: "rgba(255, 193, 5, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(61, 197, 255, 0.2)" },
    { offset: 1, color: "rgba(61, 197, 255, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(14, 66, 210, 0.2)" },
    { offset: 1, color: "rgba(14, 66, 210, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(255, 157, 9, 0.2)" },
    { offset: 1, color: "rgba(255, 157, 9, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(145, 185, 112, 0.2)" },
    { offset: 1, color: "rgba(145, 185, 112, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(71, 139, 17, 0.2)" },
    { offset: 1, color: "rgba(71, 139, 17, 0)" }
  ])
];
export const darkAreaColorList = [
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(13, 134, 255, 0.2)" },
    { offset: 1, color: "rgba(13, 134, 255, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(5, 249, 116, 0.2)" },
    { offset: 1, color: "rgba(5, 249, 116, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(255, 201, 10, 0.2)" },
    { offset: 1, color: "rgba(255, 201, 10, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(97, 236, 255, 0.2)" },
    { offset: 1, color: "rgba(97, 236, 255, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(25, 61, 255, 0.2)" },
    { offset: 1, color: "rgba(25, 61, 255, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(251, 135, 0, 0.2)" },
    { offset: 1, color: "rgba(251, 135, 0, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(177, 231, 65, 0.2)" },
    { offset: 1, color: "rgba(177, 231, 65, 0)" }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "rgba(113, 166, 0, 0.2)" },
    { offset: 1, color: "rgba(113, 166, 0, 0)" }
  ])
];
