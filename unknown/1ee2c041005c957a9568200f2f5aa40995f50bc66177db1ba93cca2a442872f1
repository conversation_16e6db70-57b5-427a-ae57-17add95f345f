<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div>
      <div class="basic-box-label">
        {{ $T("能源类型") }}
        <span class="text-Sta3">*</span>
      </div>
      <ElSelect
        class="mb-J3 w-full"
        v-model="ElSelect_3.value"
        v-bind="ElSelect_3"
        v-on="ElSelect_3.event"
      >
        <ElOption
          v-for="item in ElOption_3.options_in"
          :key="item[ElOption_3.key]"
          :label="item[ElOption_3.label]"
          :value="item[ElOption_3.value]"
          :disabled="item[ElOption_3.disabled]"
        ></ElOption>
      </ElSelect>
      <div class="basic-box-label">
        {{ $T("名称") }}
        <span class="text-Sta3">*</span>
      </div>
      <ElInput
        class="mb-J3 w-full"
        v-model="ElInput_1.value"
        v-bind="ElInput_1"
        v-on="ElInput_1.event"
      ></ElInput>
      <div class="basic-box-label">
        {{ $T("单位系数") }}
      </div>
      <ElSelect
        class="mb-J3 w-full"
        v-model="ElSelect_1.value"
        v-bind="ElSelect_1"
        v-on="ElSelect_1.event"
      >
        <ElOption
          v-for="item in ElOption_1.options_in"
          :key="item[ElOption_1.key]"
          :label="item[ElOption_1.label]"
          :value="item[ElOption_1.value]"
          :disabled="item[ElOption_1.disabled]"
        ></ElOption>
      </ElSelect>
      <div class="basic-box-label">
        {{ $T("单价") }}
      </div>
      <ElInputNumber
        class="w-full"
        v-model="ElInputNumber_1.value"
        v-bind="ElInputNumber_1"
        v-on="ElInputNumber_1.event"
      ></ElInputNumber>
    </div>
    <span slot="footer">
      <CetButton
        class="mr-J1"
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import common from "eem-base/utils/common.js";
import custom from "@/api/custom";
export default {
  name: "ProjectCfg",
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    tableData: Array
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        width: "320px",
        event: {}
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_1: {
        value: "",
        placeholder: $T("请输入内容"),
        style: {},
        event: {}
      },
      ElSelect_1: {
        value: "",
        style: {},
        event: {}
      },
      ElOption_1: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElSelect_3: {
        value: "",
        style: {},
        event: {}
      },
      ElOption_3: {
        options_in: [],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      ElInputNumber_1: {
        ...common.check_numberFloat,
        value: "",
        controls: false,
        event: {}
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.ElOption_3.options_in =
        this.$store.state.enumerations.energytype || [];
      const unitmultiplierList =
        this.$store.state.enumerations.unitmultiplier || [];
      this.ElOption_1.options_in = unitmultiplierList;
      this.init();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    init() {
      if (this.inputData_in) {
        const data = this.inputData_in;
        this.CetDialog_1.title = $T("修改");
        this.ElSelect_3.value = data.energytype;
        this.ElInput_1.value = data.name;
        this.ElSelect_1.value = data.unitmultiplier || null;
        this.ElInputNumber_1.value =
          data.unitprice == null ? undefined : data.unitprice;
      } else {
        this.CetDialog_1.title = $T("新建");
        this.ElSelect_3.value = null;
        this.ElInput_1.value = null;
        this.ElSelect_1.value = null;
        this.ElInputNumber_1.value = undefined;
      }
    },
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    async CetButton_confirm_statusTrigger_out() {
      if (!this.ElSelect_3.value) {
        this.$message({
          type: "warning",
          message: $T("请选择能源类型")
        });
        return;
      }
      if (!this.ElInput_1.value) {
        this.$message({
          type: "warning",
          message: $T("请填写名称")
        });
        return;
      }
      if (
        this.ElSelect_3.value !== this.inputData_in.energytype &&
        this.tableData.some(item => item.energytype === this.ElSelect_3.value)
      ) {
        this.$message({
          type: "warning",
          message: $T("能源类型已存在")
        });
        return;
      }

      const params = {
        id: this.inputData_in.id,
        projectId: this.projectId,
        energyType: this.ElSelect_3.value,
        name: this.ElInput_1.value,
        unitMultiplier: this.ElSelect_1.value,
        unitPrice:
          this.ElInputNumber_1.value === undefined
            ? null
            : this.ElInputNumber_1.value,
        unitSymbol: this.inputData_in.unitsymbol || ""
      };
      const fn = params.id ? custom.editEnergy : custom.addEnergy;
      const res = await fn(params);
      if (res.code !== 0) {
        return;
      }

      this.$emit("addEnergyFinished");
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.$message.success($T("保存成功"));
    }
  }
};
</script>
<style lang="scss" scoped>
.basic-box-label {
  margin-bottom: var(--J1);
}
</style>
