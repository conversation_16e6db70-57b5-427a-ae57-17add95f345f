<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full flex-col flex">
        <customElSelect
          v-model="ElSelect_energytype.value"
          v-bind="ElSelect_energytype"
          v-on="ElSelect_energytype.event"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOption_energytype.options_in"
            :key="item[ElOption_energytype.key]"
            :label="item[ElOption_energytype.label]"
            :value="item[ElOption_energytype.value]"
            :disabled="item[ElOption_energytype.disabled]"
          ></ElOption>
        </customElSelect>
        <el-checkbox class="mb-J3 mt-J3" v-model="checked">
          {{ $T("默认选中子节点") }}
        </el-checkbox>
        <div class="flex-auto">
          <CetGiantTree
            v-show="!checked"
            v-bind="CetGiantTree_1"
            v-on="CetGiantTree_1.event"
          ></CetGiantTree>
          <CetGiantTree
            v-show="checked"
            v-bind="CetGiantTree_2"
            v-on="CetGiantTree_2.event"
          ></CetGiantTree>
        </div>
        <div class="clearfix mt-J3">
          <CetButton
            class="fr"
            :disable_in="checkNodes.length ? false : true"
            v-bind="CetButton_batch"
            v-on="CetButton_batch.event"
          ></CetButton>
        </div>
      </div>
    </template>
    <template #container>
      <div class="flex-col flex h-full">
        <div class="searchBox mb-J3 flex flex-row items-center">
          <ElInput
            class="mr-J3"
            v-model="ElInput_name.value"
            v-bind="ElInput_name"
            v-on="ElInput_name.event"
          >
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </ElInput>
          <customElSelect
            v-model="ElSelect_1.value"
            v-bind="ElSelect_1"
            v-on="ElSelect_1.event"
            :prefix_in="$T('损耗阈值范围')"
          >
            <ElOption
              v-for="item in ElOption_1.options_in"
              :key="item[ElOption_1.key]"
              :label="item[ElOption_1.label]"
              :value="item[ElOption_1.value]"
              :disabled="item[ElOption_1.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <CetTable
          class="flex-auto"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
          :tree-props="{ children: 'childrenNodes' }"
          row-key="tree_id"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn
            :label="$T('操作')"
            width="100"
            header-align="center"
            align="center"
          >
            <template slot-scope="scope">
              <span class="handle" @click.stop="editHandle(scope)">
                {{ $T("编辑") }}
              </span>
            </template>
          </ElTableColumn>
        </CetTable>
      </div>
      <Edit v-bind="edit" v-on="edit.event" />
      <Batch v-bind="batch" v-on="batch.event" />
    </template>
  </CetAside>
</template>

<script>
import customApi from "@/api/custom";
import common from "eem-base/utils/common";
import Edit from "./dialogs/edit.vue";
import Batch from "./dialogs/batch.vue";
import omegaI18n from "@omega/i18n";

export default {
  name: "energyLossConfigThreshold",
  components: {
    Edit,
    Batch
  },
  props: {
    energytypes_in: {
      type: Array
    }
  },
  data() {
    const language = omegaI18n.locale === "en";
    return {
      ElSelect_energytype: {
        value: null,
        style: {
          width: "100%"
        },
        event: {
          change: this.ElSelect_energytype_change
        }
      },
      ElOption_energytype: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      checked: false,
      checkNodes: [],
      currentNode: null,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_1_currentNode_out, //选中单行输出
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          currentNode_out: this.CetGiantTree_2_currentNode_out, //选中单行输出
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      CetButton_batch: {
        visible_in: true,
        // disable_in: false,
        title: $T("批量设置"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batch_statusTrigger_out
        }
      },
      ElInput_name: {
        value: "",
        placeholder: $T("请输入名称"),
        style: {
          width: "300px"
        },
        event: {
          change: this.getTableData
        }
      },
      ElSelect_1: {
        value: null,
        style: {
          width: language ? "300px" : "200px"
        },
        event: {
          change: this.getTableData
        }
      },
      ElOption_1: {
        options_in: [
          {
            id: 1,
            name: $T("全部")
          },
          {
            id: 2,
            name: "0~5%"
          },
          {
            id: 3,
            name: "5%~10%"
          },
          {
            id: 4,
            name: $T("大于") + "10%"
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      totalCount: 0,
      // 1表格组件
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "total,sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: false,
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Columns_1: [
        {
          prop: "deviceName", // 支持path a[0].b
          label: $T("设备名称"), //列名
          headerAlign: "left",
          align: "left",
          minWidth: "200", //该宽度会自适应
          showOverflowTooltip: true,
          formatter: common.formatTextCol()
        },
        {
          prop: "threshold", // 支持path a[0].b
          label: $T("线损率") + "(%)", //列名
          headerAlign: "left",
          align: "left",
          minWidth: "200", //该宽度会自适应
          showOverflowTooltip: true,
          formatter: (row, column, cellValue) => {
            if (cellValue || cellValue === 0) {
              return `${(cellValue * 100).toFixed2(2)}`;
            }
            return "--";
          }
        }
      ],
      edit: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          save_out: this.getTableData
        }
      },
      batch: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        event: {
          save_out: this.getTableData
        }
      }
    };
  },
  watch: {
    checked() {
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_2.checkedNodes = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.$nextTick(() => {
        this.CetGiantTree_1.selectNode = this._.cloneDeep(this.currentNode);
        this.CetGiantTree_2.selectNode = this._.cloneDeep(this.currentNode);
      });
    }
  },
  methods: {
    init() {
      this.ElSelect_energytype.value = null;
      this.ElInput_name.value = "";
      this.ElSelect_1.value = 1;
      this.totalCount = 0;
      this.CetTable_1.data = [];
      this.checked = false;
      this.checkNodes = [];
      this.CetGiantTree_1.checkedNodes = [];
      this.CetGiantTree_2.checkedNodes = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.getProjectEnergy();
    },
    getProjectEnergy() {
      const vm = this;
      vm.ElOption_energytype.options_in = vm.energytypes_in;
      vm.ElSelect_energytype.value = vm._.get(
        vm.energytypes_in,
        "[0].energytype"
      );
      vm.getTreeData();
    },
    ElSelect_energytype_change() {
      this.currentNode = null;
      this.totalCount = 0;
      this.CetTable_1.data = [];
      this.CetGiantTree_1.unCheckTrigger_in = new Date().getTime();
      this.CetGiantTree_2.unCheckTrigger_in = new Date().getTime();
      this.getTreeData();
    },
    getTreeData() {
      if (!this.ElSelect_energytype.value) {
        return;
      }
      let params = {
        energyType: this.ElSelect_energytype.value
      };
      customApi
        .lossConfigProjectTree(params, { keepTransformer: false })
        .then(response => {
          if (response.code === 0) {
            let data = this._.get(response, "data");
            this.CetGiantTree_1.inputData_in = data;
            this.CetGiantTree_2.inputData_in = data;
            this.CetGiantTree_1.selectNode = this._.get(data, "[0]");
            this.CetGiantTree_2.selectNode = this._.get(data, "[0]");
          }
        });
    },
    getTableData() {
      if (!this.currentNode || !this.ElSelect_energytype.value) {
        this.totalCount = 0;
        this.CetTable_1.data = [];
        return;
      }
      let maxLoss = 0,
        minLoss = 0;
      switch (this.ElSelect_1.value) {
        case 1:
          minLoss = null;
          maxLoss = null;
          break;
        case 2:
          minLoss = 0;
          maxLoss = 0.05;
          break;
        case 3:
          minLoss = 0.05;
          maxLoss = 0.1;
          break;
        case 4:
          minLoss = 0.1;
          maxLoss = null;
          break;

        default:
          break;
      }
      let params = {
        energyType: this.ElSelect_energytype.value,
        keyWord: this.ElInput_name.value,
        maxLoss: maxLoss,
        minLoss: minLoss,
        id: this.currentNode.id,
        modelLabel: this.currentNode.modelLabel,
        keepTransformer: false
      };
      customApi.lossConfigNodes(params).then(response => {
        if (response.code === 0) {
          let data = this._.get(response, "data", []) || [];
          this.totalCount = this._.get(response, "total", 0) || 0;
          this.CetTable_1.data = data;
        }
      });
    },

    CetGiantTree_1_currentNode_out(val) {
      if (val.tree_id !== this._.get(this.currentNode, "tree_id")) {
        this.currentNode = this._.cloneDeep(val);
        this.getTableData();
      }
      this.currentNode = this._.cloneDeep(val);
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetGiantTree_2_currentNode_out(val) {
      if (val.tree_id !== this._.get(this.currentNode, "tree_id")) {
        this.currentNode = this._.cloneDeep(val);
        this.getTableData();
      }
      this.currentNode = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetButton_batch_statusTrigger_out() {
      if (this._.get(this.checkNodes, "length")) {
        this.batch.inputData_in = this.checkNodes;
        this.batch.visibleTrigger_in = new Date().getTime();
      }
    },
    editHandle(scope) {
      this.edit.inputData_in = scope.row;
      this.edit.visibleTrigger_in = new Date().getTime();
    }
  },
  mounted() {
    this.init();
  }
};
</script>
<style lang="scss" scoped>
.cet-aside :deep() {
  .cet-content-aside-container {
    background-color: var(--BG1);
    border-radius: var(--Ra);
    margin-left: 0;
  }
  .cet-content-aside-line {
    background-color: var(--B1);
  }
}

.handle {
  cursor: pointer;
  @include font_color(ZS);
}
</style>
