<template>
  <CetDialog v-bind="CetDialog_1" v-on="CetDialog_1.event">
    <div slot="title">
      <i class="el-icon-warning"></i>
      <span class="titleName ml-J1">
        {{ $T("存在重复设备，是否仍进行添加?") }}
      </span>
    </div>
    <div class="content bg1 mb-J3">
      {{ $T("共有{0}个设备存在重复。", repeatNodeNames.length) }}
      <div class="flex-row flex">
        <div
          ref="repeatNodeNamesBox"
          :class="{ repeatNodeNames: true, retract: retract }"
        >
          <span v-if="showRetract" class="handle" @click="retractHandle">
            {{ retract ? $T("展开更多") : $T("收起") }}
          </span>
          {{ repeatNodeNames.join("、") }}
        </div>
      </div>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
export default {
  props: {
    visibleTrigger_in: Number,
    confirmFn: Function,
    repeatNodeNames: Array
  },
  data() {
    return {
      showRetract: false,
      retract: true,
      CetDialog_1: {
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        width: "480px",
        showClose: true,
        appendToBody: true,
        event: {}
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确认"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      }
    };
  },
  watch: {
    visibleTrigger_in(val) {
      this.retract = true;
      this.CetDialog_1.openTrigger_in = val;
      this.checkTextOverflow();
    }
  },
  methods: {
    CetButton_cancel_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
    },
    CetButton_confirm_statusTrigger_out() {
      this.CetDialog_1.closeTrigger_in = Date.now();
      this.confirmFn && this.confirmFn();
    },
    retractHandle() {
      this.retract = !this.retract;
    },
    async checkTextOverflow() {
      this.showRetract = false;
      await this.$nextTick();
      const dom = this.$refs.repeatNodeNamesBox;
      if (!dom) return;
      this.showRetract = dom.scrollHeight > dom.clientHeight;
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__body) {
  padding: 0;
}
.el-icon-warning {
  @include font_color(ZS);
  font-size: 22px;
}
.titleName {
  font-weight: bold;
  @include font_size(H3);
}
.content {
  padding: 0 50px;
  max-height: 652px;
  overflow: auto;
  .repeatNodeNames {
    &.retract {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制在两行内 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .handle {
      float: right;
      clear: both;
      cursor: pointer;
      @include font_color(ZS);
    }
    &::before {
      content: "";
      float: right;
      width: 0;
      background: red;
      height: 100%;
      margin-bottom: -18px;
    }
  }
}
</style>
