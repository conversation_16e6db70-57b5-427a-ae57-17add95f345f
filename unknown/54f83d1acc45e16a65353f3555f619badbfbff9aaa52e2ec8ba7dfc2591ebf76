<template>
  <div :class="blockClass">
    <div class="top">
      <div class="top-left">
        <div class="icon"></div>
        <el-tooltip
          :content="item.tagName || '--'"
          :visible-arrow="false"
          placement="top"
        >
          <div class="name text-ellipsis">
            {{ item.tagName || "--" }}
          </div>
        </el-tooltip>
      </div>
      <div class="top-right">
        <span class="value text-ellipsis" :title="getValue(item.value)">
          {{ getValue(item.value) }}
        </span>
        <span class="top-unit">
          {{ item.unitName ?? "--" }}
        </span>
      </div>
    </div>
    <div class="bottom">
      <div :class="cardClass">
        <span class="label">{{ $T("同比") }}</span>
        <span v-if="positiveY" class="card-num-color1 card-num">
          <omega-icon symbolId="up" />
          <el-tooltip
            :content="getYOY(item)"
            :visible-arrow="false"
            placement="top"
          >
            <span class="per text-ellipsis">
              {{ getYOY(item) }}
            </span>
          </el-tooltip>
          <span class="unit">%</span>
        </span>
        <span v-else class="card-num-color2 card-num">
          <omega-icon symbolId="down" />
          <el-tooltip
            :content="getYOY(item)"
            :visible-arrow="false"
            placement="top"
          >
            <span class="per text-ellipsis">
              {{ getYOY(item) }}
            </span>
          </el-tooltip>
          <span class="unit">%</span>
        </span>
      </div>
      <div :class="cardClass" class="ml-J1">
        <span class="label">{{ $T("环比") }}</span>
        <span v-if="positiveC" class="card-num-color1 card-num">
          <omega-icon symbolId="up" />
          <el-tooltip
            :content="getChain(item)"
            :visible-arrow="false"
            placement="top"
          >
            <span class="per text-ellipsis">
              {{ getChain(item) }}
            </span>
          </el-tooltip>
          <span class="unit">%</span>
        </span>
        <span v-else class="card-num-color2 card-num">
          <omega-icon symbolId="down" />
          <el-tooltip
            :content="getChain(item)"
            :visible-arrow="false"
            placement="top"
          >
            <span class="per text-ellipsis">
              {{ getChain(item) }}
            </span>
          </el-tooltip>
          <span class="unit">%</span>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import omegaTheme from "@omega/theme";

export default {
  props: {
    item: {
      type: Object
    },
    index: {
      type: Number
    },
    cycle: {
      type: Number
    }
  },
  data() {
    return {
      positiveY: false,
      positiveC: false
    };
  },
  computed: {
    theme: function () {
      return omegaTheme.theme === "light" ? "light" : "dark";
    },
    blockClass() {
      return ["block", `block_${this.theme}_${this.index}`];
    },
    cardClass() {
      return ["card", `card_${this.theme}`];
    },
    unitInfo() {
      return this.$store.state.unitInfo;
    }
  },
  methods: {
    getValue(val) {
      let str = "--";
      if (Number.isFinite(val)) {
        str = val.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,");
      }
      return str;
    },
    //同比
    getYOY(item) {
      const { value: current, yoyValue: previous } = item;
      if (!Number.isFinite(current) || !previous) {
        return "--";
      }
      const percent = (100 * (current - previous)) / previous;
      this.positiveY = percent >= 0;
      return Math.abs(percent).toFixed(2);
    },
    //环比
    getChain(item) {
      const { value: current, momValue: previous } = item;
      if (!Number.isFinite(current) || !previous || this.cycle === 1) {
        return "--";
      }
      const percent = (100 * (current - previous)) / previous;
      this.positiveC = percent >= 0;
      return Math.abs(percent).toFixed(2);
    }
  }
};
</script>
<style lang="scss" scoped>
.block {
  width: 100%;
  height: 100%;
  padding: 16px;
  border-radius: 12px;
  box-sizing: border-box;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
}
$sub_energy_consumption_themes: light dark;
$sub_energy_consumption_dark_colors: (
  (
    card_bg_color: rgba(13, 134, 255, 0.2),
    value_bg: linear-gradient(180deg, #e9f4ff, #9acdff)
  ),
  (
    card_bg_color: rgba(5, 249, 116, 0.2),
    value_bg: linear-gradient(180deg, #d5ffe8, #6afead)
  ),
  (
    card_bg_color: rgba(255, 201, 10, 0.2),
    value_bg: linear-gradient(180deg, #fff8de, #ffd646)
  ),
  (
    card_bg_color: rgba(44, 193, 214, 0.2),
    value_bg: linear-gradient(180deg, #e4fcff, #6ddcec)
  ),
  (
    card_bg_color: rgba(74, 103, 255, 0.2),
    value_bg: linear-gradient(180deg, #bcc7ff, #7288ff)
  ),
  (
    card_bg_color: rgba(255, 143, 39, 0.2),
    value_bg: linear-gradient(180deg, #ffc997, #ff8f27)
  ),
  (
    card_bg_color: rgba(177, 231, 65, 0.2),
    value_bg: linear-gradient(180deg, #ebffc2, #b1e741)
  ),
  (
    card_bg_color: rgba(113, 166, 0, 0.2),
    value_bg: linear-gradient(180deg, #ddf9a3, #8bc116)
  )
);
$sub_energy_consumption_colors: (
  (
    card_bg_color: rgba(112, 224, 158, 0.15),
    value_bg: linear-gradient(180deg, #90f3b9, #70e09e)
  ),
  (
    card_bg_color: rgba(76, 166, 255, 0.15),
    value_bg: linear-gradient(180deg, #a1d1ff, #4ca6ff)
  ),
  (
    card_bg_color: rgba(255, 206, 32, 0.15),
    value_bg: linear-gradient(180deg, #ffe175, #ffce20)
  ),
  (
    card_bg_color: rgba(125, 217, 255, 0.15),
    value_bg: linear-gradient(180deg, #a6e5ff, #7dd9ff)
  ),
  (
    card_bg_color: rgba(49, 102, 239, 0.15),
    value_bg: linear-gradient(180deg, #80a3ff, #3166ef)
  ),
  (
    card_bg_color: rgba(255, 157, 9, 0.1),
    value_bg: linear-gradient(180deg, #ffca7a, #ff9d09)
  ),
  (
    card_bg_color: rgba(185, 225, 119, 0.15),
    value_bg: linear-gradient(180deg, #d6f69f, #b9e177)
  ),
  (
    card_bg_color: rgba(111, 190, 11, 0.15),
    value_bg: linear-gradient(180deg, #abe069, #6fbe0b)
  )
);
@each $theme in $sub_energy_consumption_themes {
  $colors: $sub_energy_consumption_colors;
  @if $theme == dark {
    $colors: $sub_energy_consumption_dark_colors;
  }
  .block_#{$theme} {
    @for $i from 1 through length($colors) {
      $item: nth($colors, $i);
      $card_bg_color: map-get($item, "card_bg_color");
      $value_bg: map-get($item, "value_bg");
      &_#{$i - 1} {
        background-image: url("../assets/#{$theme}_#{$i - 1}.png");
        .card {
          background-color: $card_bg_color;
        }
        .icon {
          background-image: url("../../projectOverview/assets/#{$theme}-#{$i}.png");
        }
        .value {
          background: $value_bg;
        }
      }
    }
  }
}
.top {
  flex: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .name {
    font-weight: bold;
    font-size: 14px;
    line-height: 24px;
    margin-left: 8px;
    max-width: 120px;
  }
  .value {
    font-weight: bold;
    font-size: 24px;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent;
  }
  .top-unit {
    font-size: 14px;
    @include font_color(T2);
    margin-left: 4px;
    flex: none;
  }
  .top-right {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    margin-left: 4px;
    overflow: hidden;
  }
  .top-left {
    flex: none;
    display: flex;
    align-items: center;
  }
}
.bottom {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  .card {
    width: calc(100% - 4px);
    height: 41px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .per {
      font-size: 16px;
      font-weight: bold;
      margin-right: 4px;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent;
    }
    .label {
      padding: 0 8px 0 12px;
      font-size: 14px;
      flex: none;
    }
    .card-num {
      width: calc(100% - 48px);
    }
    .unit {
      padding-right: 12px;
      font-size: 14px;
      @include font_color(T2);
    }
    .omega-icon {
      width: 15px;
      height: 15px;
      margin-right: 12px;
    }
  }
}
.card_dark {
  .card-num-color1 {
    display: flex;
    align-items: center;
    .omega-icon {
      color: #ff8b8b;
    }
    .per {
      background: linear-gradient(180deg, #ffe1e1 0%, #ff8b8b 100%);
    }
  }
  .card-num-color2 {
    display: flex;
    align-items: center;
    .omega-icon {
      color: #05f974;
    }
    .per {
      background: linear-gradient(180deg, #e5fff1 0%, #78ffb6 100%);
    }
  }
}
.card_light {
  .card-num-color1 {
    display: flex;
    align-items: center;
    .omega-icon {
      color: #f95e5a;
    }
    .per {
      background: linear-gradient(180deg, #ffa8a6 0%, #f95e5a 100%);
    }
  }
  .card-num-color2 {
    display: flex;
    align-items: center;

    .omega-icon {
      color: #29b061;
    }
    .per {
      background: linear-gradient(180deg, #58db8f 0%, #29b061 100%);
    }
  }
}
.icon {
  width: 40px;
  height: 40px;
  background-size: 100% 100%;
}
</style>
