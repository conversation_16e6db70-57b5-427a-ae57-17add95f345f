<template>
  <div class="pageContain page2">
    <div class="pageContain">
      <el-button type="primary" id="btn1">第一步</el-button>
      <el-button type="primary" id="btn2">第二步</el-button>
      <el-button type="primary" id="btn3">打开弹框</el-button>
    </div>
    <TestDialog
      v-if="AddFormDialog.openTrigger"
      ref="TestDialog"
      v-bind="AddFormDialog"
      @useEventHandler="useEventHandler"
    />
  </div>
</template>

<script>
import { api } from "@altair/knight";
// components
import TestDialog from "./components/TestDialog/index.vue";
// hooks
import useCounter from "@/hooks/useDriver.js";
const [Driver] = useCounter();

export default {
  name: "noviceGuide-page1",
  components: { TestDialog },
  data() {
    return {
      AddFormDialog: {
        openTrigger: false,
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null
      }
    };
  },
  mounted() {
    this.initDriver();
  },
  methods: {
    initDriver() {
      const { step } = api.getRouterQuery();
      if (!step) return;
      Driver.setSteps([
        {
          element: "#btn1",
          popover: { title: $T("第一步"), description: "Description" }
        },
        {
          element: "#btn2",
          popover: { title: $T("第二步"), description: "Description" }
        },
        {
          element: "#btn3",
          popover: {
            title: $T("打开弹框"),
            description: "点击完成,打开一个弹框",
            onNextClick: () => {
              this.onOpenClick();
              Driver.moveNext();
            },
            onPrevClick: () => {
              Driver.movePrevious();
            }
          }
        }
      ]);
      Driver.drive();
    },
    onOpenClick() {
      this.AddFormDialog.openTrigger = true;
      this.AddFormDialog.openTrigger_in = new Date().getTime();

      this.$nextTick(() => {
        this.$refs.TestDialog.CetDialog_pagedialog.openTrigger_in =
          new Date().getTime();
      });
    },
    onClose() {
      this.isDrawerMode = false;
      this.AddFormDialog.openTrigger = false;
    },
    // Unified event handling
    useEventHandler(e, ...args) {
      if (typeof e !== "string" || !this.hasOwnProperty(e)) {
        console.warn(`Invalid function name: ${e}`);
        return;
      }

      const handler = this[e];

      if (typeof handler === "function") {
        try {
          handler.call(this, ...args);
        } catch (error) {
          console.error(`Error executing function ${e}:`, error);
        }
      } else {
        console.warn(` ${e} is not a function`);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.page2 {
  background-color: var(--BG1);
}
</style>
