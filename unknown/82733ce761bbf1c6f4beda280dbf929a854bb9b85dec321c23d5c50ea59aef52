<template>
  <CetAside class="cet-aside">
    <template #aside>
      <div class="h-full flex flex-col" id="projectBatchConfig_treeBox">
        <div class="flex-row flex mb-J1">
          <CustomElSelect
            v-model="ElSelect_status.value"
            v-bind="ElSelect_status"
            v-on="ElSelect_status.event"
            :prefix_in="$T('生成状态')"
          >
            <ElOption
              v-for="item in ElOption_status.options_in"
              :key="item[ElOption_status.key]"
              :label="item[ElOption_status.label]"
              :value="item[ElOption_status.value]"
              :disabled="item[ElOption_status.disabled]"
            ></ElOption>
          </CustomElSelect>
          <el-tooltip
            :content="
              $T(
                '1.当表计关联的管网类型是管道、开关柜或一段线时，该管网层级需要关联管理层级后，表计才属于已经生成配置；2.当表计关联的管网类型是其他类型（比如变压器），该表计直接属于已经生成配置；'
              )
            "
          >
            <i class="el-icon-question leading-[32px] ml-J1"></i>
          </el-tooltip>
        </div>
        <CetGiantTree
          class="flex-auto"
          v-bind="CetGiantTree_project"
          v-on="CetGiantTree_project.event"
        ></CetGiantTree>
        <CetButton
          class="text-right"
          v-bind="CetButton_refresh"
          v-on="CetButton_refresh.event"
        ></CetButton>
      </div>
    </template>

    <template #container>
      <div class="fullfilled flex flex-col">
        <div class="flex flex-row">
          <el-tooltip
            effect="light"
            :disabled="!importing"
            :content="importingStr"
            placement="top"
          >
            <CustomElSelect
              class="recordStatusSelect"
              v-model="recordStatus"
              @change="recordStatusChange"
              :prefix_in="$T('配置状态')"
              :disabled="importing"
            >
              <ElOption
                v-for="item in ElOption_configStatus.options_in"
                :key="item[ElOption_configStatus.key]"
                :label="item[ElOption_configStatus.label]"
                :value="item[ElOption_configStatus.value]"
                :disabled="item[ElOption_configStatus.disabled]"
              ></ElOption>
            </CustomElSelect>
          </el-tooltip>
          <div class="legend ml-J3">
            <i class="notGenerated"></i>
            {{ $T("编辑中配置") }}
          </div>
          <div class="legend ml-J3">
            <i class="complete"></i>
            {{ $T("已生成配置（不允许编辑）") }}
          </div>
          <div class="flex-auto flex flex-row justify-end">
            <el-tooltip
              effect="light"
              :disabled="!importing"
              :content="importingStr"
              placement="top"
            >
              <CetButton
                class="ml-J1"
                id="projectBatchConfig_tableDelete"
                v-bind="CetButton_batchDel"
                v-on="CetButton_batchDel.event"
                :disable_in="handsontable.deleteDisable || importing"
              ></CetButton>
            </el-tooltip>
            <el-tooltip
              effect="light"
              :disabled="!importing"
              :content="importingStr"
              placement="top"
            >
              <CetButton
                class="ml-J1"
                v-bind="CetButton_reset"
                v-on="CetButton_reset.event"
                :disable_in="CetButton_reset.disable_in || importing"
              ></CetButton>
            </el-tooltip>
            <el-tooltip
              effect="light"
              :disabled="!importing"
              :content="importingStr"
              placement="top"
            >
              <CetButton
                class="ml-J1"
                id="projectBatchConfig_draft"
                v-bind="CetButton_draft"
                v-on="CetButton_draft.event"
                :disable_in="draftSaveDisable || importing"
              ></CetButton>
            </el-tooltip>
            <el-tooltip
              effect="light"
              :disabled="!importing"
              :content="importingStr"
              placement="top"
            >
              <CetButton
                class="ml-J1"
                v-bind="CetButton_back"
                v-on="CetButton_back.event"
                :disable_in="CetButton_back.disable_in || importing"
              ></CetButton>
            </el-tooltip>
            <el-tooltip
              effect="light"
              :disabled="!importing"
              :content="importingStr"
              placement="top"
            >
              <CetButton
                class="ml-J1"
                id="projectBatchConfig_save"
                v-bind="CetButton_save"
                v-on="CetButton_save.event"
                :disable_in="saveDisable"
              ></CetButton>
            </el-tooltip>
          </div>
        </div>
        <div
          class="flex-auto mt-J3 projectBatchConfigHandsontable"
          id="projectBatchConfig_table"
        >
          <Handsontable
            ref="handsontable"
            v-bind="handsontable"
            v-on="handsontable.event"
            :deleteDisable.sync="handsontable.deleteDisable"
            v-show="!draftSaveDisable"
          />
          <div
            class="emptyBox bg-BG1"
            :class="{
              light: lightTheme
            }"
            v-if="draftSaveDisable"
          >
            <div class="img"></div>
            <div class="text">
              {{ $T("请从左边节点树拖入节点，进行设置！") }}
            </div>
          </div>
        </div>
      </div>
      <RepeatConfirm v-bind="repeatConfirm" />
    </template>
  </CetAside>
</template>

<script>
import customApi from "@/api/custom";
import Handsontable from "./handsontable.vue";
import common from "eem-base/utils/common";
import RepeatConfirm from "./repeatConfirm.vue";
import omegaTheme from "@omega/theme";
import { CustomElSelect } from "eem-base/components";
export default {
  name: "batchConfig",
  components: {
    Handsontable,
    RepeatConfirm,
    CustomElSelect
  },
  props: {
    energyType_in: Number
  },
  data() {
    return {
      lastRecordStatus: 1,
      recordStatus: 1,
      configLimit: 5000,
      roomConfig: null,
      manageModelLabels: [],
      columnsData: [],
      ElSelect_status: {
        value: 2,
        style: {},
        event: {
          change: this.ElSelect_status_change_out
        }
      },
      ElOption_status: {
        options_in: [
          {
            id: 0,
            name: $T("全部")
          },
          {
            id: 1,
            name: $T("已经生成配置")
          },
          {
            id: 2,
            name: $T("未生成配置")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOption_configStatus: {
        options_in: [
          {
            id: 0,
            name: $T("全部")
          },
          {
            id: 1,
            name: $T("未生成配置")
          },
          {
            id: 2,
            name: $T("已生成配置")
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetGiantTree_project: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          edit: {
            enable: true,
            drag: {
              prev: false,
              next: false,
              inner: false
            }
          },
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "id"
            },
            key: {
              name: "text",
              children: "children"
            }
          },
          callback: {
            onDrop: this.dropTree2Dom
          }
        },
        event: {}
      },
      CetButton_batchDel: {
        visible_in: true,
        disable_in: true,
        title: $T("批量删除"),
        type: "danger",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_batchDel_statusTrigger_out
        }
      },
      CetButton_reset: {
        visible_in: true,
        disable_in: false,
        title: $T("重置全部过滤"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_reset_statusTrigger_out
        }
      },
      CetButton_draft: {
        visible_in: true,
        disable_in: true,
        title: $T("保存为草稿"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_draft_statusTrigger_out
        }
      },
      CetButton_back: {
        visible_in: true,
        disable_in: false,
        title: $T("上一步"),
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_back_statusTrigger_out
        }
      },
      CetButton_save: {
        visible_in: true,
        disable_in: true,
        title: $T("生成配置"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_save_statusTrigger_out
        }
      },
      handsontable: {
        data: [],
        columns: [],
        deleteDisable: true,
        clearFilterHandle: Date.now(),
        clearSortHandle: Date.now(),
        clearHot: Date.now(),
        event: {}
      },
      repeatConfirm: {
        visibleTrigger_in: Date.now(),
        confirmFn: null,
        addSectionNum: 0,
        repeatNodeNames: []
      },
      CetButton_refresh: {
        visible_in: true,
        disable_in: false,
        title: $T("刷新"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_refresh_statusTrigger_out
        }
      }
    };
  },
  computed: {
    projectTenantId() {
      return this.$store.state.projectTenantId;
    },
    projectId() {
      return this.$store.state.projectId;
    },
    draftSaveDisable() {
      return !this.handsontable.data?.length;
    },
    importing() {
      return (
        this.$store.getters["importProgress/importing"](5) ||
        this.$store.getters["importProgress/importing"](15)
      );
    },
    saveDisable() {
      return !this.handsontable.data?.length || this.importing;
    },
    importingStr() {
      return (
        this.$store.getters["importProgress/importingStr"](5) ||
        this.$store.getters["importProgress/importingStr"](15)
      );
    },
    lightTheme() {
      return omegaTheme.theme === "light";
    }
  },
  methods: {
    async init() {
      this.recordStatus = 1;
      this.ElSelect_status.value = 2;
      this.getTreeData();
      this.getLimit();
      await this.getColumn();
      this.initTable();
    },
    async getTreeData() {
      const queryData = {
        tenantId: this.projectTenantId,
        buildStatus: this.ElSelect_status.value
      };
      const res = await customApi.organizationConfigTree(queryData);
      const data = res?.data ?? [];
      this.CetGiantTree_project.inputData_in = data;
    },
    async getLimit() {
      const res = await customApi.organizationConfigLimit();
      this.configLimit = res?.data ?? 5000;
    },
    async getColumn() {
      const columns = [
        {
          className: "htLeft",
          data: "deviceName",
          type: "text",
          label: `${$T("表计名称")} <span class='required'>*</span>`,
          columnSorting: true,
          readOnly: true
        },
        {
          className: "htLeft",
          data: "channelName",
          type: "text",
          label: `${$T("通道名称")} <span class='required'>*</span>`,
          readOnly: true
        }
      ];
      // 先查列
      const columnRes = await customApi.organizationConfigColumnName();
      const columnsData = columnRes?.data ?? [];
      if (!columnsData?.length) return;

      // 查列选项
      const manageNameRes = await customApi.organizationConfigManageName();
      const manageNames = manageNameRes?.data ?? [];

      const roomConfigRes = await customApi.organizationConfigRoomConfig();
      const roomConfigs = roomConfigRes?.data ?? [];

      const columnsConfig = [];

      // 所属功能房 能源类型为电，房间选项为配电房名称下拉；其他能源类型下拉选项为管道房名称下拉
      const roomType = this.energyType_in === 2 ? 1 : 6;

      const roomConfig = roomConfigs.find(i => i.room.roomType === roomType);
      const typeList = roomConfig?.children?.map(i => i.name) ?? [];
      columnsConfig.push({
        allowInvalid: true,
        data: "componentType",
        source: typeList,
        strict: true,
        type: "autocomplete",
        label: `${$T("元件类型")} <span class='required'>*</span>`
      });

      const roomObj = manageNames.find(i => {
        return i.modelLabel === "room" && i.roomType === roomType;
      });
      columnsConfig.push({
        allowInvalid: true,
        data: "functionalRoomName",
        source: roomObj?.nameList ?? [],
        strict: false,
        type: "autocomplete",
        label: `${
          roomType == 1 ? $T("所属配电房") : $T("所属管道房")
        } <span class='required'>*</span>`
      });

      // 存一下，给后面保存的时候解析用
      this.manageModelLabels = columnsData.map(i => i.modelLabel);
      this.columnsData = columnsData;
      this.roomConfig = roomConfig;

      // 补上其他层级
      columnsData.forEach(item => {
        let nameObj;
        if (item.modelLabel === "room") {
          // 房间需要特殊处理
          nameObj = manageNames.find(i => {
            return i.modelLabel === item.modelLabel && i.roomType === null;
          });
        } else {
          nameObj = manageNames.find(i => i.modelLabel === item.modelLabel);
        }
        const config = {
          allowInvalid: true,
          data: item.modelLabel,
          source: nameObj?.nameList ?? [],
          strict: false,
          type: "autocomplete",
          label: item.name
        };
        columnsConfig.push(config);
      });
      columns.push(...columnsConfig);
      this.handsontable.columns = columns;
    },
    ElSelect_status_change_out() {
      this.getTreeData();
    },
    recordStatusChange() {
      this.saveDraftTips(
        () => {
          this.initTable();
        },
        () => {
          this.recordStatus = this.lastRecordStatus;
        }
      );
    },
    // 查询已有草稿
    async initTable() {
      const queryData = {
        recordStatus: this.recordStatus,
        energyType: this.energyType_in
      };
      this.lastRecordStatus = this.recordStatus;
      const res = await customApi.organizationConfigQuery(queryData);
      const data = res?.data ?? [];
      data.forEach(item => {
        item.deleteStatus = false;
        // 解管理层级
        item.manageDataList.forEach(i => {
          item[i.modelLabel] = i.name;
        });
        // 解析元件类型
        const list = this.roomConfig?.children ?? [];
        item.componentType = list.find(
          i =>
            i.modelLabel === item.componentType &&
            i.lineFunctionType === item.lineFunctionType
        )?.name;
        item.tree_id = `${item.deviceId}_${item.id}`;
      });
      this.handsontable.clearFilterHandle = Date.now();
      this.handsontable.clearSortHandle = Date.now();
      this.handsontable.clearHot = Date.now();
      await this.$nextTick();
      this.handsontable.data = data;
    },
    CetButton_batchDel_statusTrigger_out() {
      this.$confirm($T("是否确认删除?"), $T("提示"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      })
        .then(() => {
          this.handsontableDelete();
        })
        .catch(() => {
          this.$message.info($T("已取消"));
        });
    },
    async handsontableDelete() {
      const list = this.$refs.handsontable.getTableData();
      let newList = [],
        deleteIds = [],
        deleteItems = [];
      list.forEach(item => {
        if (!item.deleteStatus) {
          newList.push(item);
          return;
        }
        if (item.id) {
          deleteIds.push(item.id);
        }
        deleteItems.push(item);
      });
      if (!deleteItems.length) {
        this.$message.warning($T("请选择删除项"));
        return;
      }
      if (deleteIds.length) {
        const res = await customApi.organizationDeleteConfig(deleteIds);
        if (res.code !== 0) return;
        this.$message.success($T("操作成功"));
        this.$store.dispatch("importProgress/noticeProgress", {
          vm: this,
          initialProcessInfo: res.data,
          cb: async () => {
            await this.getColumn();
            this.$refs.handsontable.clearDeleteData();
          }
        });
      } else {
        this.$message.success($T("操作成功"));
        this.handsontable.data = newList;
      }
    },
    CetButton_reset_statusTrigger_out() {
      this.handsontable.clearFilterHandle = Date.now();
    },
    CetButton_draft_statusTrigger_out() {
      this.saveDraft();
    },
    async saveDraft(cb) {
      if (!this.ifHandsontableFilter()) return;
      const saveData = this.getSaveTableData();
      if (!saveData) return;
      const res = await customApi.organizationConfigSaveDraft(saveData);
      if (res.code !== 0) return;
      this.$message.success($T("操作成功"));
      if (cb) {
        cb();
      } else {
        this.initTable();
      }
    },
    getSaveTableData(check) {
      let tableData = this.$refs.handsontable.getTableData();
      let formatRowFlag = false;
      const columnsData = this._.cloneDeep(this.columnsData);
      columnsData.push({
        name: this.energyType_in === 2 ? $T("所属配电房") : $T("所属管道房"),
        modelLabel: "functionalRoomName"
      });
      const checkNamePattern = common.pattern_name.pattern;
      const checkNameLength = common.check_name.max;
      // 第xx行元件类型未选择
      const checkComponentTypeRequired = [];
      // 第xx行所属功能房未选择
      const checkFunctionalRoomNameRequired = [];
      let checkObj = {};
      columnsData.forEach(item => {
        const { modelLabel, name } = item;
        checkObj[modelLabel] = {
          checkTextLength: [],
          checkTextLength$text: val => {
            return $T(`第【{0}】行{1}名称过长`, val.join("、"), name);
          },
          checkTextCharacter: [],
          checkTextCharacter$text: val => {
            return $T(`第【{0}】行{1}存在特殊字符`, val.join("、"), name);
          }
        };
      });

      tableData.forEach((item, index) => {
        if (item.recordStatus === 2) {
          return;
        }
        item.rowNumber = index + 1;
        // 组管理层级
        const manageDataList = [];
        this.manageModelLabels.forEach(i => {
          if (item[i]) {
            manageDataList.push({
              modelLabel: i,
              name: item[i]
            });
          }
        });
        item.manageDataList = manageDataList;
        item.lineFunctionType = this.getLineFunctionType(
          item.componentType,
          this.roomConfig
        );
        // 元件类型由名称转为modelLabel
        const list = this.roomConfig?.children ?? [];
        item.componentType = list.find(
          i => i.name === item.componentType
        )?.modelLabel;

        if (!check) return;
        // 校验检查
        if (!item.componentType) {
          checkComponentTypeRequired.push(index + 1);
        }
        if (!item.functionalRoomName) {
          checkFunctionalRoomNameRequired.push(index + 1);
        }

        columnsData.forEach(({ modelLabel }) => {
          if (item[modelLabel]?.length > checkNameLength) {
            checkObj[modelLabel].checkTextLength.push(index + 1);
            formatRowFlag = true;
          }
          if (!checkNamePattern.test(item[modelLabel])) {
            checkObj[modelLabel].checkTextCharacter.push(index + 1);
            formatRowFlag = true;
          }
        });
      });
      if (
        check &&
        (checkComponentTypeRequired.length ||
          checkFunctionalRoomNameRequired.length ||
          formatRowFlag)
      ) {
        let text = "";
        if (checkComponentTypeRequired.length) {
          text += $T(
            "第【{0}】行元件类型未选择",
            checkComponentTypeRequired.join("、")
          );
          text += "</br>";
        }
        if (checkFunctionalRoomNameRequired.length) {
          const roomName =
            this.energyType_in === 2 ? $T("所属配电房") : $T("所属管道房");
          text += $T(
            `第【{0}】行{1}未选择`,
            checkFunctionalRoomNameRequired.join("、"),
            roomName
          );
          text += "</br>";
        }
        columnsData.forEach(({ modelLabel }) => {
          if (checkObj[modelLabel].checkTextLength?.length) {
            text += checkObj[modelLabel].checkTextLength$text(
              checkObj[modelLabel].checkTextLength
            );
            text += "</br>";
          }
          if (checkObj[modelLabel].checkTextCharacter?.length) {
            text += checkObj[modelLabel].checkTextCharacter$text(
              checkObj[modelLabel].checkTextCharacter
            );
            text += "</br>";
          }
        });
        this.$message({
          type: "warning",
          dangerouslyUseHTMLString: true,
          message: text
        });
        return false;
      }

      tableData = tableData.filter(i => i.recordStatus !== 2);
      return tableData;
    },
    // 从功能房中根据元件类型解析出线功能
    getLineFunctionType(componentType, roomConfig) {
      const list = roomConfig?.children ?? [];
      const item = list.find(i => i.name === componentType);
      return item?.lineFunctionType;
    },
    CetButton_back_statusTrigger_out() {
      this.saveDraftTips(() => {
        this.$emit("back_out");
      });
    },
    async CetButton_save_statusTrigger_out() {
      // 如果已有过滤条件存在，则不进行保存
      if (!this.ifHandsontableFilter($T("请点击重置全部过滤，再点生成配置！")))
        return;
      const saveData = this.getSaveTableData(true);
      if (!saveData) return;
      const res = await customApi.organizationConfigSaveConfig(saveData);
      if (res.code !== 0) return;
      this.$store.dispatch("importProgress/noticeProgress", {
        vm: this,
        initialProcessInfo: res.data,
        cb: async () => {
          this.getTreeData();
          await this.getColumn();
          this.initTable();
        }
      });
      this.initTable();
    },
    ifHandsontableFilter(warningText) {
      const handsontableFilter = this.$refs.handsontable.handsontableFilter;
      if (handsontableFilter) {
        this.$message.warning(warningText || $T("请点击重置全部过滤"));
        return false;
      }
      return true;
    },
    async dropTree2Dom(event, treeId, treeNodes, targetNode, moveType) {
      // 判断是否拖拽至目标区域
      let target = event.target,
        enter = target.className.includes("projectBatchConfigHandsontable");
      while (target && !enter) {
        target = target.parentNode;
        enter = target?.className?.includes("projectBatchConfigHandsontable");
      }
      if (!enter) return;
      if (this.importing) {
        this.$message.warning(this.importingStr);
        return;
      }

      // 如果已有过滤条件存在，则不进行添加节点
      if (!this.ifHandsontableFilter()) return;
      const nodes = this.getDevices(treeNodes);
      if (!nodes?.length) return;

      // 先查在数据库是否有重复的草稿或已生成的配置
      const deviceIds = nodes.map(i => i.nodeId);
      const res = await customApi.organizationConfigCheckConfig({
        deviceIds,
        energyType: this.energyType_in
      });
      const repeatIds = (res?.data ?? []).map(i => i.id);
      const filterNodes = this.filterItems(nodes, repeatIds);
      this.addTableItems(filterNodes);
    },
    getDevices(value) {
      let nodes = [];
      if (!value?.length) return [];
      const loop = function (val) {
        if (!val?.length) return;
        val.forEach(item => {
          if (item.nodeType === 269619472) {
            const parentNode = item.getParentNode();
            item.channelId = parentNode.nodeId;
            item.channelName = parentNode.text;
            nodes.push(item);
          }
          if (item?.children?.length) {
            loop(item.children);
          }
        });
      };
      loop(value);
      return nodes;
    },
    // 添加的节点去重
    filterItems(nodes, repeatIds_in) {
      const nodeIds = nodes.map(i => i.nodeId);
      const tableData = this.$refs.handsontable.getTableData();
      const ids = tableData.map(i => i.deviceId);
      const repeatIds = this.getRepeatNum([...nodeIds, ...ids]);
      const repeatNodeNames = [];
      const addNodes = nodes.filter(i => {
        if (!repeatIds.includes(i.nodeId) && !repeatIds_in.includes(i.nodeId)) {
          return true;
        } else {
          repeatNodeNames.push(i.text);
        }
      });
      if (!repeatNodeNames?.length) {
        return addNodes;
      }
      this.repeatConfirm.confirmFn = isAddAll => {
        this.addTableItems(isAddAll ? nodes : addNodes);
      };
      this.repeatConfirm.repeatNodeNames = repeatNodeNames;

      this.repeatConfirm.addSectionNum = addNodes?.length ?? 0;
      this.repeatConfirm.visibleTrigger_in = Date.now();
    },
    // 能添加重复设备
    addTableItems(nodes) {
      if (!nodes?.length) return;
      const tableData = this.$refs.handsontable.getTableData();
      this.handsontable.clearSortHandle = Date.now();
      const list = nodes.map(item => {
        return {
          deviceId: item.nodeId,
          deviceName: item.text,
          channelId: item.channelId,
          channelName: item.channelName,
          componentType: null, //元件类型
          lineFunctionType: null, //线功能
          functionalRoomName: null, //功能房名称
          projectId: this.projectId,
          energyType: this.energyType_in,
          recordStatus: -1, //状态
          deleteStatus: false,
          tree_id: `${item.nodeId}_${Date.now()}`
        };
      });
      tableData.push(...list);
      // 限制添加节点总数
      if (tableData.length > this.configLimit) {
        this.$message.warning(
          $T("单次编辑行数不能超过{0}行", this.configLimit)
        );
        return;
      }
      this.handsontable.data = tableData;
    },
    // 查重复的数字
    getRepeatNum(arr) {
      const counts = this._.countBy(arr);
      let duplicateNumbers = [];
      Object.keys(counts).forEach(key => {
        if (counts[key] > 1) {
          duplicateNumbers.push(+key);
        }
      });
      return duplicateNumbers;
    },
    // 提示是否先保存草稿
    saveDraftTips(giveUp, cancel) {
      const list = this.$refs.handsontable.getTableData();
      const editFlag = list.find(item => item.recordStatus === -1);
      if (!editFlag) {
        giveUp && giveUp();
        return;
      }
      this.$confirm($T("存在未保存项，数据会丢失，是否保存草稿?"), $T("提示"), {
        confirmButtonText: $T("保存"),
        cancelButtonText: $T("放弃保存"),
        distinguishCancelAndClose: true,
        type: "warning"
      })
        .then(() => {
          this.saveDraft(giveUp);
        })
        .catch(action => {
          if (action === "cancel") {
            giveUp && giveUp();
          } else {
            this.$message.info($T("已取消"));
            cancel && cancel();
          }
        });
    },
    beforeunload(event) {
      const list = this.$refs.handsontable.getTableData();
      const editFlag = list.find(item => item.recordStatus === -1);
      if (!editFlag) return;
      event.returnValue = $T("确定要离开此页面吗？你未保存的更改将丢失。");
    },
    CetButton_refresh_statusTrigger_out() {
      const callback = async () => {
        const res = await customApi.organizationConfigTreeRefresh();
        if (res.code !== 0) return;
        this.$message.success($T("操作成功"));
        this.$store.dispatch("importProgress/noticeProgress", {
          vm: this,
          initialProcessInfo: res.data
        });
      };
      this.saveDraftTips(callback);
    }
  },
  mounted() {
    this.init();
    window.addEventListener("beforeunload", this.beforeunload);
  },
  beforeDestroy() {
    window.removeEventListener("beforeunload", this.beforeunload);
  },
  deactivated() {
    window.removeEventListener("beforeunload", this.beforeunload);
  }
};
</script>

<style lang="scss" scoped>
.cet-aside :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
.recordStatusSelect {
  width: 200px;
}
.legend {
  line-height: 32px;
  .notGenerated {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px 2px 2px 2px;
    background-color: var(--Sta2);
  }
  .complete {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px 2px 2px 2px;
    background-color: var(--Sta1);
  }
}
.projectBatchConfigHandsontable {
  position: relative;
  .emptyBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: -2px;
    right: -2px;
    .img {
      position: absolute;
      top: 70px;
      left: 50%;
      width: 310px;
      height: 302px;
      transform: translate(-50%, 0);
      background-image: url(("../assets/empty.png"));
      background-size: 100% 100%;
    }
    &.light .img {
      background-image: url(("../assets/empty_light.png"));
      background-size: 100% 100%;
    }
    .text {
      position: absolute;
      top: 384px;
      left: 50%;
      transform: translate(-50%, 0);
      text-align: center;
      font-size: var(--H5);
    }
  }
}
</style>
