{
  // Place your V2.x 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  /***表单部分的组件,包括from,select,input, checkbox等***/
  //cet-form的代码片段
  "cet-form-template": {
    "prefix": "cet-form-template",
    "body": [
      " <CetForm                                                  ",
      "   :data.sync=\"CetForm_$1.data\"                                   ",
      "   v-bind=\"CetForm_${1:请输入组件唯一识别字符串}\"                  ",
      "   v-on=\"CetForm_${1:请输入组件唯一识别字符串}.event\"                   ",
      " ></CetForm>"
    ],
    "description": ""
  },
  "cet-form-data": {
    "prefix": "cet-form-data",
    "body": [
      "// ${1:设置组件唯一识别字段}表单组件       ",
      " CetForm_$1: {        ",
      "   dataMode: \"static\", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static           ",
      "   queryMode: \"trigger\", // 查询按钮触发trigger，或者查询条件变化立即查询diff                      ",
      "   //组件数据绑定设置项                                                         ",
      "   dataConfig: {                                       ",
      "     queryFunc: \"\",                                   ",
      "     writeFunc: \"\",                                   ",
      "     modelLabel: \"\",                                   ",
      "     dataIndex: [],      //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2                          ",
      "     modelList: [],                                       ",
      "     groups: []             //例子     {   name: \"treenode\",   models: [\"floor\", \"building\", \"sectionarea\"] }                      ",
      "   },                                   ",
      "   //组件输入项",
      "   inputData_in: {},                                   ",
      "   data: {},                                   ",
      "   queryId_in: -1,                                   ",
      "   queryTrigger_in: Date.now(),                                   ",
      "   saveTrigger_in: Date.now(),                                   ",
      "   localSaveTrigger_in: Date.now(),                                   ",
      "   resetTrigger_in: Date.now(),                                   ",
      "   size: \"small\",                                               ",
      "   labelWidth:\"80px\",                                           ",
      "   rules:{},                                                        ",
      "   event:{                                                        ",
      "     currentData_out: this.CetForm_$1_currentData_out,                            ",
      "     saveData_out: this.CetForm_$1_saveData_out,                                  ",
      "     finishData_out: this.CetForm_$1_finishData_out,                            ",
      "     finishTrigger_out: this.CetForm_$1_finishTrigger_out                                  ",
      "   }                                                             ",
      " },                                                "
    ],
    "description": ""
  },
  "cet-form-method": {
    "prefix": "cet-form-method",
    "body": [
      "// ${1:设置组件唯一识别字段}表单输出",
      "    CetForm_$1_currentData_out(val) {                                      ",
      "    },                                                               ",
      "    CetForm_$1_saveData_out(val) {    ",
      "    },                                   ",
      "    CetForm_$1_finishData_out(val) { ",
      "    },                                   ",
      "    CetForm_$1_finishTrigger_out(val) { ",
      "    },                                   "
    ],
    "description": ""
  },

  //el-form-item的代码片段
  "el-form-item-template": {
    "prefix": "el-form-item-template",
    "body": [
      " <el-form-item label=\"名称\" prop=\"name\"                 ",
      " ></el-form-item>"
    ],
    "description": ""
  },

  //cet-input的代码片段
  "cet-input-template": {
    "prefix": "cet-input-template",
    "body": [
      " <ElInput",
      "  v-model=\"ElInput_${1:请输入组件唯一识别字符串}.value\"                        ",
      "  v-bind=\"ElInput_$1\"                                                  ",
      "  v-on=\"ElInput_${1:请输入组件唯一识别字符串}.event\"                  ",
      " ></ElInput>"
    ],
    "description": ""
  },
  "cet-input-data": {
    "prefix": "cet-input-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                   ",
      " ElInput_$1: {                     ",
      "   value: \"\",                                               ",
      "   placeholder: \"请输入内容\",                                               ",
      "   style: {                                                              ",
      "      width:\"200px\"                                                ",
      "   },                                                                           ",
      "   event: {                                                              ",
      "      change:this.ElInput_$1_change_out,                                                ",
      "      input: this.ElInput_$1_input_out                                       ",
      "   }                                                                           ",
      " },                                                         "
    ],
    "description": ""
  },
  "cet-input-method": {
    "prefix": "cet-input-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                        ",
      "ElInput_$1_change_out(val) {},                                  ",
      "ElInput_$1_input_out(val) {},                                  "
    ],
    "description": ""
  },

  //cet-inputnumber的代码片段
  "cet-inputnumber-template": {
    "prefix": "cet-inputnumber-template",
    "body": [
      " <ElInputNumber",
      "  v-model=\"ElInputNumber_${1:请输入组件唯一识别字符串}.value\"                        ",
      "  v-bind=\"ElInputNumber_$1\"                                                  ",
      "  v-on=\"ElInputNumber_${1:请输入组件唯一识别字符串}.event\"                  ",
      " ></ElInputNumber>"
    ],
    "description": ""
  },
  "cet-inputnumber-data": {
    "prefix": "cet-inputnumber-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                   ",
      " ElInputNumber_$1: {                     ",
      "   value: \"\",                                               ",
      "   controls: false,                                               ",
      "   style: {                                                              ",
      "      width:\"200px\"                                                ",
      "   },                                                                           ",
      "   event: {                                                              ",
      "      change:this.ElInputNumber_$1_change_out,                                                ",
      "   }                                                                           ",
      " },                                                         "
    ],
    "description": ""
  },
  "cet-inputnumber-method": {
    "prefix": "cet-inputnumber-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                        ",
      "ElInputNumber_$1_change_out(val) {},                                  ",
      "                                         "
    ],
    "description": ""
  },

  //cet-select的代码片段
  "cet-select-template": {
    "prefix": "cet-select-template",
    "body": [
      " <ElSelect",
      "  v-model=\"ElSelect_${1:请输入组件唯一识别字符串}.value\"                        ",
      "  v-bind=\"ElSelect_${1:请输入组件唯一识别字符串}\"                  ",
      "  v-on=\"ElSelect_${1:请输入组件唯一识别字符串}.event\"                  ",
      " ></ElSelect>"
    ],
    "description": ""
  },
  "cet-select-data": {
    "prefix": "cet-select-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                               ",
      " ElSelect_$1: {                                       ",
      "   value: \"\",                                         ",
      "   style: {                                                              ",
      "      width:\"200px\"                                                ",
      "   },                                                                           ",
      "   event:{                                                        ",
      "     change: this.ElSelect_$1_change_out                            ",
      "   },                                                             ",
      " },                                                                "
    ],
    "description": ""
  },
  "cet-select-method": {
    "prefix": "cet-select-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                        ",
      "ElSelect_$1_change_out(val) {},                                  ",
      "                                                               "
    ],
    "description": ""
  },

  //cet-option的代码片段
  "cet-option-template": {
    "prefix": "cet-option-template",
    "body": [
      " <ElOption                                                                                                         ",
      "  v-for=\"item in ElOption_${1:请输入组件唯一识别字符串}.options_in\"                                                ",
      "  :key=\"item[ElOption_${1:请输入组件唯一识别字符串}.key]\"                                                          ",
      "  :label=\"item[ElOption_${1:请输入组件唯一识别字符串}.label]\"                                                      ",
      "  :value=\"item[ElOption_${1:请输入组件唯一识别字符串}.value]\"                                                      ",
      "  :disabled=\"item[ElOption_${1:请输入组件唯一识别字符串}.disabled]\"                                                ",
      " ></ElOption>                                                                                                      "
    ],
    "description": ""
  },

  "cet-option-data": {
    "prefix": "cet-option-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                                  ",
      " ElOption_$1: {                                                              ",
      "   options_in: [],                                                                                         ",
      "   key: \"value\",                                                                          ",
      "   value: \"value\",                                                                          ",
      "   label: \"label\",                                                                          ",
      "   disabled: \"disabled\"                                                                          ",
      " },                                                                                "
    ],
    "description": ""
  },

  //cet-checkbox的代码片段
  "cet-checkbox-template": {
    "prefix": "cet-checkbox-template",
    "body": [
      " <ElCheckbox",
      "  v-model=\"ElCheckbox_${1:请输入组件唯一识别字符串}.value\"                        ",
      "  v-bind=\"ElCheckbox_$1\"                                                  ",
      "  v-on=\"ElCheckbox_${1:请输入组件唯一识别字符串}.event\"                  ",
      " >{{ElCheckbox_$1.text}}</ElCheckbox>"
    ],
    "description": ""
  },
  "cet-checkbox-data": {
    "prefix": "cet-checkbox-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                   ",
      " ElCheckbox_$1: {                     ",
      "   value: false,                                               ",
      "   text: \"\",                                               ",
      "   disabled: false,                                               ",
      "   event: {                                                              ",
      "      change:this.ElCheckbox_$1_change_out,                                                ",
      "   }                                                                           ",
      " },                                                         "
    ],
    "description": ""
  },
  "cet-checkbox-method": {
    "prefix": "cet-checkbox-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                        ",
      "ElCheckbox_$1_change_out(val) {},                                  ",
      "                                         "
    ],
    "description": ""
  },

  //cet-checkgroup的代码片段
  "cet-checkgroup-template": {
    "prefix": "cet-checkgroup-template",
    "body": [
      " <ElCheckboxGroup",
      "  v-model=\"ElCheckboxGroup_${1:请输入组件唯一识别字符串}.value\"                        ",
      "  v-bind=\"ElCheckboxGroup_${1:请输入组件唯一识别字符串}\"                  ",
      "  v-on=\"ElCheckboxGroup_${1:请输入组件唯一识别字符串}.event\"                  ",
      " ></ElCheckboxGroup>"
    ],
    "description": ""
  },
  "cet-checkgroup-data": {
    "prefix": "cet-checkgroup-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                               ",
      " ElCheckboxGroup_$1: {                                       ",
      "   value: [],                                         ",
      "   style: {                                                              ",
      "      width:\"300px\"                                                ",
      "   },                                                                           ",
      "   event:{                                                        ",
      "     change: this.ElCheckboxGroup_$1_change_out                            ",
      "   },                                                             ",
      " },                                                                "
    ],
    "description": ""
  },
  "cet-checkgroup-method": {
    "prefix": "cet-checkgroup-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                        ",
      "ElCheckboxGroup_$1_change_out(val) {},                                  ",
      "                                                               "
    ],
    "description": ""
  },

  //cet-checklist 的代码片段
  "cet-checklist-template": {
    "prefix": "cet-checklist-template",
    "body": [
      " <ElCheckbox                                                                                                         ",
      "  v-for=\"item in ElCheckboxList_${1:请输入组件唯一识别字符串}.options_in\"                                                ",
      "  :key=\"item[ElCheckboxList_$1.key]\"                                                          ",
      "  :label=\"item[ElCheckboxList_$1.label]\"                                                      ",
      "  :disabled=\"item[ElCheckboxList_$1.disabled]\"                                                ",
      " >{{item[ElCheckboxList_$1.text]}}</ElCheckbox>                                                                                                      "
    ],
    "description": ""
  },

  "cet-checklist-data": {
    "prefix": "cet-checklist-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                                  ",
      " ElCheckboxList_$1: {                                                              ",
      "   options_in: [],                                                                                         ",
      "   key: \"label\",                                                                          ",
      "   label: \"label\",                                                                          ",
      "   text: \"text\",                                                                          ",
      "   disabled: \"disabled\"                                                                          ",
      " },                                                                                "
    ],
    "description": ""
  },

  //cet-radiogroup 的代码片段
  "cet-radiogroup-template": {
    "prefix": "cet-radiogroup-template",
    "body": [
      " <ElRadioGroup",
      "  v-model=\"ElRadioGroup_${1:请输入组件唯一识别字符串}.value\"                        ",
      "  v-bind=\"ElRadioGroup_${1:请输入组件唯一识别字符串}\"                  ",
      "  v-on=\"ElRadioGroup_${1:请输入组件唯一识别字符串}.event\"                  ",
      " ></ElRadioGroup>"
    ],
    "description": ""
  },
  "cet-radiogroup-data": {
    "prefix": "cet-radiogroup-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                               ",
      " ElRadioGroup_$1: {                                       ",
      "   value: \"\",                                         ",
      "   style: {                                                              ",
      "      width:\"300px\"                                                ",
      "   },                                                                           ",
      "   event:{                                                        ",
      "     change: this.ElRadioGroup_$1_change_out                            ",
      "   },                                                             ",
      " },                                                                "
    ],
    "description": ""
  },
  "cet-radiogroup-method": {
    "prefix": "cet-radiogroup-method",
    "body": [
      "// ${1:设置组件唯一识别字段}输出,方法名要带_out后缀                                        ",
      "ElRadioGroup_$1_change_out(val) {},                                  ",
      "                                                               "
    ],
    "description": ""
  },

  //cet-radiolist 的代码片段
  "cet-radiolist-template": {
    "prefix": "cet-radiolist-template",
    "body": [
      " <ElRadio                                                                                                         ",
      "  v-for=\"item in ElRadioList_${1:请输入组件唯一识别字符串}.options_in\"                                                ",
      "  :key=\"item[ElRadioList_$1.key]\"                                                          ",
      "  :label=\"item[ElRadioList_$1.label]\"                                                      ",
      "  :disabled=\"item[ElRadioList_$1.disabled]\"                                                ",
      " >{{item[ElRadioList_$1.text]}}</ElRadio>                                                                                                      "
    ],
    "description": ""
  },

  "cet-radiolist-data": {
    "prefix": "cet-radiolist-data",
    "body": [
      "// ${1:设置组件唯一识别字段}组件                                                                  ",
      " ElRadioList_$1: {                                                              ",
      "   options_in: [],                                                                                         ",
      "   key: \"label\",                                                                          ",
      "   label: \"label\",                                                                          ",
      "   text: \"text\",                                                                          ",
      "   disabled: \"disabled\"                                                                          ",
      " },                                                                                "
    ],
    "description": ""
  }
}
