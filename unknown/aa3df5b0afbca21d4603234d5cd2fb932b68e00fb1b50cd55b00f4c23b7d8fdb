<!--
 * @Author: wwj <EMAIL>
 * @Date: 2022-11-17 15:34:08
 * @LastEditors: wwj <EMAIL>
 * @LastEditTime: 2022-11-18 14:04:28
 * @FilePath: \BIZ-iPowerCloud-Web2022\src\layout\components\setting\components\AlarmSoundMode.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-select v-model="soundMode" placeholder="" class="alarm-mode">
    <el-option
      v-for="item in soundModeOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  name: "AlarmMode",
  components: {},
  computed: {
    soundMode: {
      get() {
        return this.$store.state.settings.soundMode;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "soundMode",
          value: val
        });
      }
    }
  },
  data() {
    return {
      soundModeOptions: [
        {
          label: $T("单提示音模式"),
          value: "single"
        },
        {
          label: $T("语音播报模式"),
          value: "speech"
        },
        {
          label: $T("等级声音模式"),
          value: "level"
        },
        {
          label: $T("持续声音模式"),
          value: "continuous"
        }
      ]
    };
  },
  watch: {},
  methods: {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.alarm-mode {
  display: block;
  position: relative;
  width: 85%;
  margin: 0 auto;
}
</style>
