import { store } from "@omega/app";

export const loadBMap = async () => {
  const isOnline = store?.state?.systemCfg?.onLine;
  let BMap_URL = [
    "static/library/baiduMap/map3.0_init.js",
    "static/library/baiduMap/map3.0.js"
  ];
  if (isOnline) {
    BMap_URL = ["http://lbsyun.baidu.com/custom/stylelist.js"];
    window._BMapSecurityConfig = {
      // 代理服务器地址
      serviceHost: `${window.location.origin}/baiduMapService/`
    };
    BMap_URL.unshift("/baiduMapService/api?v=3.0&callback=onBMapCallback");

    // BMap_URL.unshift(
    //   `http://api.map.baidu.com/api?v=3.0&ak=aW6qp5b7zdFx3172twk8O5IQxnt9x5F6&callback=onBMapCallback`
    // );
  }
  if (typeof BMap !== "undefined") {
    // 如果已加载直接返回
    return true;
  }
  // // 百度地图异步加载回调处理
  return new Promise(res => {
    if (isOnline) {
      window.onBMapCallback = () => {
        console.log("百度地图脚本初始化成功...");
        setTimeout(() => {
          res();
        }, 300);
      };
    }
    // 插入script脚本
    let finishCount = 0;
    BMap_URL.forEach(sUrl => {
      let scriptNode = document.createElement("script");
      scriptNode.type = "text/javascript";
      scriptNode.src = sUrl;

      scriptNode.onload = scriptNode.onreadystatechange = function () {
        if (
          !this.readyState ||
          this.readyState === "loaded" ||
          this.readyState === "complete"
        )
          finishCount += 1;

        if (finishCount === BMap_URL.length) {
          setTimeout(() => {
            res();
          }, 300);
        }
      };
      document.body.appendChild(scriptNode);
    });
  });
};
