<template>
  <div id="graphID" ref="container"></div>
</template>
<script>
import G6 from "@antv/g6";
import common from "eem-base/utils/common";
import addDark from "../assets/addDark.png";
import addLight from "../assets/addLight.png";
import reduceDark from "../assets/reduceDark.png";
import reduceLight from "../assets/reduceLight.png";
import customApi from "@/api/custom";
import omegaTheme from "@omega/theme";

export default {
  props: {
    inputData_in: Object,
    unit_in: {
      type: String,
      default: ""
    },
    params: Object
  },
  components: {},
  computed: {
    lightTheme() {
      return omegaTheme.theme === "light";
    },
    echartTheme() {
      return omegaTheme.theme;
    }
  },
  data() {
    return {
      deep: 2,
      graph: null,
      graphData: {},
      isExpand: [], //存储节点展开状态
      initInputData: {}, //初始化数据存放
      handInputData: {}, //过滤使用数据存放
      projectValue: 0, //储存第一层能源流向值
      childrenData: {} //子节点数据
    };
  },
  watch: {
    inputData_in: {
      handler(val) {
        this.deep = 2;
        this.$nextTick(() => {
          this.paintChart();
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化图形配置
    initGraphConf() {
      let _this = this;
      if (this.graph) return;
      var width = this.$refs.container.scrollWidth;
      var height = this.$refs.container.scrollHeight || 500;
      this.graph = new G6.Graph({
        container: "graphID",
        width: width,
        height: height,
        fitView: true,
        minZoom: 0.5,
        maxZoom: 3,
        modes: {
          default: [
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            {
              type: "tooltip",
              formatText: this.formatLabel
            }
          ]
        },
        layout: {
          type: "dagre",
          rankdir: "LR",
          ranksep: 125,
          nodesep: 20,
          controlPoints: true
        },
        defaultNode: {
          type: "lossAnalysis-node"
        },
        defaultEdge: {
          type: "cubic-horizontal",
          style: {
            lineWidth: 2,
            stroke: "#B6BAC7"
          }
        }
      });
      this.graph.on("node:click", function (evt) {
        let val = _this._.get(evt, "item._cfg.model", {}) || {};
        _this.clickChart(val);
      });
    },
    formatLabel(params) {
      if (["switchcabinet", "arraycabinet"].includes(params.modelLabel)) {
        return "";
      }
      let isLast = this.inputData_in.linkNode.find(
        item => item.source === params.id
      );
      let str = `${params.label}`;

      const changeNodeData = params.changeNodeData;

      str += `<br />${
        changeNodeData?.length ? $T("总能耗") : $T("能耗值")
      }：${common.formatNum(common.roundNumber(params.value, 2, "--"))}${
        this.unit_in
      }`;

      // 展示合并前各个节点的能耗值
      if (changeNodeData?.length) {
        changeNodeData.forEach(item => {
          str += `<br />${item.name}：${common.formatNum(
            common.roundNumber(item.value, 2, "--")
          )}${this.unit_in}`;
        });
      }

      // 最后层级节点没有损耗
      if (params.depth === this.deep || !isLast) {
        return str;
      }

      let color =
        params.lossPercent != null &&
        params.threshold != null &&
        Math.abs(params.lossPercent) >= params.threshold
          ? "color:#F76771!important;"
          : "";

      const totalVal = `${common.formatNum(
        common.roundNumber(params.nextLevelEnergy, 2, "--")
      )}${this.unit_in}`;
      str += `<br />${$T("下级能耗之和")}：${totalVal}`;

      str += `<br />${$T("损耗值")}：${common.formatNum(
        common.roundNumber(params.loss, 2, "--")
      )}${this.unit_in}`;

      str += `<br />${$T("损耗率")}：<span style=${color}>${
        params.lossPercent || params.lossPercent === 0
          ? Number(params.lossPercent * 100).toFixed(2) + "%"
          : "--"
      }</span>`;
      return str;
    },
    // 获取图表数据
    getChartData() {
      this.isExpand = [];
      let data = this.handInputData;
      if (!data || !data.lossDataVoList || !data.linkNode) return;
      // 数据源
      const lossDataVoList = data.lossDataVoList || [];
      // 数据连线
      const edges = data.linkNode || [];
      let depth = 0;
      // 先默认所有节点都可展开
      lossDataVoList.forEach(item => {
        item.expend = true;
        if (item.depth > depth) {
          depth = item.depth;
        }
      });
      //如果是最后一层子节点，不需要显示/可展示/
      lossDataVoList.forEach(item => {
        let expanded = false;
        let isChild = false;
        let getIsChild = this.initInputData.linkNode.filter(ii => {
          return item.name === ii.source;
        });
        isChild = getIsChild.length === 0;
        if (item.depth < depth) {
          expanded = true;
        } else {
          expanded = this._.cloneDeep(isChild);
        }
        this.isExpand.push({
          id: item.name,
          expanded: expanded,
          isChild: isChild,
          depth: item.depth
        });
      });
      // 点集
      let nodes = [];
      lossDataVoList.forEach(item => {
        let status =
          item.lossPercent != null &&
          item.threshold != null &&
          Math.abs(item.lossPercent) >= item.threshold
            ? "red"
            : "blue";
        const hasNextLevelEnergy = Object.prototype.hasOwnProperty.call(
          item,
          "nextLevelEnergy"
        );
        let obj = {
          id: item.name,
          label: item.deviceName,
          value: item.value,
          loss: item.loss,
          lossPercent: item.lossPercent,
          threshold: item.threshold,
          status,
          modelLabel: item.modelLabel,
          depth: item.depth,
          expend: item.expend,
          iindex: item.iindex,
          changeNodeData: item.changeNodeData,
          nextLevelEnergy: hasNextLevelEnergy
            ? item.nextLevelEnergy
            : item.value - item.loss
        };
        nodes.push(obj);
      });
      return { nodes, edges };
    },
    // 绘制图形
    paintChart() {
      const inputData = this.inputData_in;
      if (!inputData || !inputData.lossDataVoList || !inputData.linkNode)
        return;
      if (Array.isArray(inputData.lossDataVoList)) {
        inputData.lossDataVoList.forEach((item, index) => {
          item.iindex = index;
        });
      }
      this.initInputData = this._.cloneDeep(inputData);
      this.handInputData = this.filterInitData(inputData);
      const data = this.getChartData();
      this.$nextTick(() => {
        if (this._.isEmpty(this.graph)) this.initGraphConf();
        this.graphData = this._.cloneDeep(data);
        this.graph.data(data);
        this.graph.render();
      });
    },
    // 超长文本显示
    fittingString(str, maxWidth, fontSize) {
      const ellipsis = "...";
      const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0];
      let currentWidth = 0;
      let res = str;
      const pattern = new RegExp("[\u4E00-\u9FA5]+"); // distinguish the Chinese charactors and letters
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth - ellipsisLength) return;
        if (pattern.test(letter)) {
          // Chinese charactors
          currentWidth += fontSize;
        } else {
          // get the width of single letter according to the fontSize
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth - ellipsisLength) {
          res = `${str.substr(0, i)}${ellipsis}`;
        }
      });
      return res;
    },
    //过滤初始化返回数据，只需要显示三层数据结构;depth ===0和depth === 1归类为父节点
    filterInitData(val) {
      let lossDataVoList = val.lossDataVoList || [],
        linkNode = val.linkNode || [],
        toDelete = [];
      let filEnergyFlowNodeDataList = lossDataVoList.filter(
        item => item.depth <= 2
      );
      toDelete = lossDataVoList.filter(item => item.depth > 2);
      let filLinkNodeList = linkNode.filter(item => {
        return !toDelete.find(
          toDeleteItem => item.target === toDeleteItem.treeId
        );
      });
      return {
        lossDataVoList: filEnergyFlowNodeDataList,
        linkNode: filLinkNodeList
      };
    },
    // 点击节点触发方法
    async clickChart(val) {
      if (!val) {
        return;
      }
      //查找对应节点信息
      const target = this.initInputData.linkNode.find(
        item => item.source === val.id
      );
      //如果是点击最后一层子节点，直接return退出处理
      if (!target) return;
      if (val.depth === 0) {
        return;
      }
      //找到节点现在是收缩还是展开状态
      let index = 0;
      const result = this.isExpand.find((item, idx) => {
        index = idx;
        return item.id === val.id;
      });
      if (!result) {
        return;
      }
      // 收缩，根节点不能收缩
      if (result.expanded) {
        this.isExpand[index].expanded = false;
        const nodes = this._.cloneDeep(this.graphData.nodes);
        let list = this._.cloneDeep(this.graphData.edges);
        const edges = _.unionWith(list, (arr, oth) => {
          return arr.source === oth.source && arr.target === oth.target;
        });
        const copyEdges = this._.cloneDeep(edges);
        let numList = this.graphData.nodes.filter(item => {
          return item.depth === this.deep;
        });
        if (val.depth === this.deep - 1 && numList.length === 1) {
          this.deep--;
        }
        this.handleNode(edges, val, nodes, copyEdges);
        let data = { nodes, edges };
        this.graphData = this._.cloneDeep(data);
        this.graph.data(data);
        this.graph.render();
        // 展开
      } else if (!result.expanded) {
        this.isExpand[index].expanded = true;
        await this.getChildNodeData(val);
        const childNodeData = this.childrenData;

        // 将同一层的状态改为打开
        this.isExpand.forEach(item => {
          if (item.depth === val.depth) {
            item.expanded = true;
          }
        });

        const chartData = [];
        // 桑基图data
        const lossDataVoList = childNodeData.lossDataVoList;
        // 先默认所有节点都可展开
        lossDataVoList.forEach(item => {
          item.expend = true;
          //如果是最后一层子节点，不需要显示/ 可展示
          let expanded = false;
          let isChild = false;
          let getIsChild = this.initInputData.linkNode.filter(ii => {
            return item.name === ii.source;
          });
          isChild = getIsChild.length === 0;
          expanded = this._.cloneDeep(isChild);
          this.isExpand.push({
            id: item.name,
            expanded: expanded,
            isChild: isChild,
            depth: item.depth
          });
          let status =
            item.lossPercent != null &&
            item.threshold != null &&
            Math.abs(item.lossPercent) >= item.threshold
              ? "red"
              : "blue";
          const hasNextLevelEnergy = Object.prototype.hasOwnProperty.call(
            item,
            "nextLevelEnergy"
          );
          chartData.push({
            id: item.name,
            label: item.deviceName,
            value: item.value,
            loss: item.loss,
            lossPercent: item.lossPercent,
            threshold: item.threshold,
            status,
            modelLabel: item.modelLabel,
            depth: item.depth,
            expend: item.expend,
            iindex: item.iindex,
            changeNodeData: item.changeNodeData,
            nextLevelEnergy: hasNextLevelEnergy
              ? item.nextLevelEnergy
              : item.value - item.loss
          });
        });
        let nodes = this._.cloneDeep(chartData);
        const edges = this._.cloneDeep(childNodeData.linkNode);
        if (val.depth === this.deep) {
          this.deep++;
        }
        let data = { nodes, edges };
        this.graphData = this._.cloneDeep(data);
        this.graph.data(data);
        this.graph.render();
      }
    },
    //点击缩收删除选中节点下子节点
    handleNode(linkData, data, nodeData, copyLinkData) {
      const toDelete = [];
      // linkData中找到所有source为当前点击的节点
      let deleteArr = linkData.filter(item => {
        return data && item.source === data.id;
      });
      let nameList = linkData.map(item => item.source);
      if (deleteArr.length) {
        // linkData中删除deleteArr数据
        deleteArr.forEach(item => {
          this.deleteFun(linkData, item, toDelete);
        });
      }

      if (toDelete.length) {
        // linkData中被删除的数据继续调用handleNode
        toDelete.forEach(item => {
          // 判断子节点存在两个父节点情况
          const filFatherNodes = copyLinkData.filter(ii => {
            return item.target === ii.target;
          });
          if (filFatherNodes.length < 2) {
            const deleteData = nodeData.find(it => {
              return it.id === item.target;
            });
            this.handleNode(linkData, deleteData, nodeData, copyLinkData);
          }
        });
      }
      // nodeData中删除deleteArr中target节点，可展开节点中同步删除
      deleteArr.forEach(item => {
        // 判断子节点存在两个父节点情况
        const filFatherNodes = copyLinkData.filter(ii => {
          return item.target === ii.target;
        });
        if (filFatherNodes.length < 2) {
          const idx = nodeData.findIndex(it => {
            return item.target === it.id;
          });
          const it = this.isExpand.findIndex(i => {
            return item.target === i.id;
          });
          if (idx >= 0) {
            nodeData.splice(idx, 1);
          }
          if (it >= 0) {
            this.isExpand.splice(it, 1);
          }
        }
      });
    },
    deleteFun(linkData, item, toDelete) {
      const idx = linkData.findIndex(it => {
        return this._.isEqual(it, item);
      });
      if (idx >= 0) {
        toDelete.push(linkData.splice(idx, 1)[0]);
      }
    },
    //获取点击选择节点下一子层级
    async getChildNodeData(data) {
      let params = this.params;
      params.depth = data.depth + 1;
      let loss = [];
      let link = [];
      const res = await customApi.getLossAnalysisDepth(params);
      if (res.code === 0) {
        loss = res.data.lossDataVoList || [];
        link = res.data.linkNode || [];
      }
      this.childrenData = {
        lossDataVoList: loss,
        linkNode: link
      };
    }
  },
  mounted() {
    const _this = this;
    G6.registerNode("lossAnalysis-node", {
      drawShape(cfg, group) {
        const color =
          cfg.status === "red"
            ? ["#F65D68", "#ffffff"]
            : ["#3E77FC", "#ffffff"];
        const r = 5;
        let shape;

        let width = 220;
        let height = 80;
        // 文字大小
        const H3 = 16;
        const H1 = 22;
        shape = group.addShape("rect", {
          attrs: {
            x: -width / 2,
            y: -height / 2,
            stroke: color[0],
            width,
            height,
            radius: r
          }
        });
        // 判断是否是最后一层级节点
        const isLast = _this.inputData_in.linkNode.find(
          item => item.source === cfg.id
        );
        group.addShape("rect", {
          attrs: {
            x: -width / 2,
            y: -height / 2,
            width,
            height: height / 2,
            fill: color[0],
            radius: [r, r, 0, 0]
          },
          name: "main-box",
          draggable: false
        });

        group.addShape("rect", {
          attrs: {
            x: -width / 2 + 1,
            y: 0,
            width: width - 2,
            height: height / 2 - 1,
            fill: color[1],
            radius: [0, 0, r, r]
          },
          name: "title-box",
          draggable: false
        });

        // 节点值
        group.addShape("text", {
          attrs: {
            x: 0,
            y: height / 4,
            textAlign: "center",
            textBaseline: "middle",
            text: _this.fittingString(
              common.formatNum(common.roundNumber(cfg.value, 2, "--")) +
                _this.unit_in,
              width - 32,
              H1
            ),
            fontSize: H1,
            fill: "#000",
            opacity: 0.7
          },
          name: "percentage"
        });

        // 节点文本
        group.addShape("text", {
          attrs: {
            x: 0,
            y: -height / 4,
            textAlign: "center",
            textBaseline: "middle",
            text: _this.fittingString(cfg.label, width - 32, H1),
            fontSize: H1,
            fill: "#fff"
          },
          name: "value"
        });

        if (isLast && cfg.depth < _this.deep) {
          const totalVal = `${common.formatNum(
            common.roundNumber(cfg.nextLevelEnergy, 2, "--")
          )}${_this.unit_in}`;
          // 下级能耗之和
          group.addShape("text", {
            attrs: {
              x: width / 2 + 20,
              y: 0 - height / 2 + 15,
              textAlign: "left",
              textBaseline: "top",
              text: `${$T("下级能耗之和")}：${totalVal}`,
              fontSize: 12,
              fill: ["dark", "blue"].includes(_this.echartTheme)
                ? "#fff"
                : "#000"
            },
            name: "percentage"
          });
          // 损耗量
          group.addShape("text", {
            attrs: {
              x: width / 2 + 20,
              y: 8,
              textAlign: "left",
              textBaseline: "top",
              text: `${$T("损耗量")}：${common.formatNum(
                common.roundNumber(cfg.loss, 2, "--")
              )}${_this.unit_in}`,
              fontSize: 12,
              fill: ["dark", "blue"].includes(_this.echartTheme)
                ? "#fff"
                : "#000"
            },
            name: "percentage"
          });
          // 损耗率
          group.addShape("text", {
            attrs: {
              x: width / 2 + 20,
              y: 22,
              textAlign: "left",
              textBaseline: "top",
              text: `${$T("损耗率")}：`,
              fontSize: 12,
              fill: ["dark", "blue"].includes(_this.echartTheme)
                ? "#fff"
                : "#000"
            },
            name: "percentage"
          });
          // 损耗率
          group.addShape("text", {
            attrs: {
              x: width / 2 + 68,
              y: 22,
              textAlign: "left",
              textBaseline: "top",
              text: `${
                cfg.lossPercent || cfg.lossPercent === 0
                  ? Number(cfg.lossPercent * 100).toFixed(2) + "%"
                  : "--"
              }`,
              fontSize: 12,
              fill:
                cfg.status === "red"
                  ? "#F65D68"
                  : ["dark", "blue"].includes(_this.echartTheme)
                  ? "#fff"
                  : "#000"
            },
            name: "percentage"
          });
        }

        //加减符号
        const result = _this.isExpand.find(item => item.id === cfg.id);
        let isExpend = false;
        if (cfg.expend) {
          isExpend = true;
        }
        if (result && result.expanded) {
          isExpend = false;
        }
        if (isExpend) {
          group.addShape("image", {
            attrs: {
              x: width / 2,
              y: -height / 4,
              width: 20,
              height: 20,
              img: _this.lightTheme ? addLight : addDark
            },
            name: "image-shape"
          });
        } else if (!result.isChild && cfg.depth !== 0) {
          group.addShape("image", {
            attrs: {
              x: width / 2,
              y: -height / 4,
              width: 20,
              height: 20,
              img: _this.lightTheme ? reduceLight : reduceDark
            },
            name: "image-shape"
          });
        }

        return shape;
      },
      getAnchorPoints() {
        return [
          [1, 0.5],
          [0, 0.5]
        ];
      }
    });
    this.$nextTick(() => {
      this.paintChart();
    });
  },
  activated() {}
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
#graphID {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.g6-tooltip {
  white-space: nowrap;
  transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s,
    top 0.4s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  background-color: rgba(50, 50, 50, 0.7);
  font-size: 14px;
  border-radius: 4px;
  color: rgb(255, 255, 255);
  padding: 10px;
  pointer-events: none;
}
</style>
